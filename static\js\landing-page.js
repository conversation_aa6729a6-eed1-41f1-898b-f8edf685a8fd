/**
 * Landing Page Interactive Features
 * تفاعلات صفحة الهبوط الجذابة
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all features
    initCounterAnimation();
    initScrollAnimations();
    initSearchEnhancements();
    initSmoothScrolling();
    initParallaxEffect();
    initTypingEffect();
    initFloatingElements();
});

/**
 * Counter Animation for Statistics
 * تحريك أرقام الإحصائيات
 */
function initCounterAnimation() {
    const counters = document.querySelectorAll('.stat-number');
    const speed = 200; // Animation speed
    
    const animateCounter = (counter) => {
        const target = parseInt(counter.getAttribute('data-count'));
        const count = parseInt(counter.innerText);
        const increment = target / speed;
        
        if (count < target) {
            counter.innerText = Math.ceil(count + increment);
            setTimeout(() => animateCounter(counter), 1);
        } else {
            counter.innerText = target.toLocaleString('ar-SA');
        }
    };
    
    // Intersection Observer for triggering animation
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                if (!counter.classList.contains('animated')) {
                    counter.classList.add('animated');
                    animateCounter(counter);
                }
            }
        });
    }, { threshold: 0.5 });
    
    counters.forEach(counter => observer.observe(counter));
}

/**
 * Scroll Animations
 * تحريكات التمرير
 */
function initScrollAnimations() {
    // Add CSS for animations
    const style = document.createElement('style');
    style.textContent = `
        .fade-in-up {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }
        
        .fade-in-up.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        .fade-in-left {
            opacity: 0;
            transform: translateX(-30px);
            transition: all 0.6s ease;
        }
        
        .fade-in-left.visible {
            opacity: 1;
            transform: translateX(0);
        }
        
        .fade-in-right {
            opacity: 0;
            transform: translateX(30px);
            transition: all 0.6s ease;
        }
        
        .fade-in-right.visible {
            opacity: 1;
            transform: translateX(0);
        }
        
        .scale-in {
            opacity: 0;
            transform: scale(0.8);
            transition: all 0.6s ease;
        }
        
        .scale-in.visible {
            opacity: 1;
            transform: scale(1);
        }
    `;
    document.head.appendChild(style);
    
    // Add animation classes to elements
    const animatedElements = [
        { selector: '.category-card', animation: 'fade-in-up' },
        { selector: '.ad-card', animation: 'scale-in' },
        { selector: '.stat-item', animation: 'fade-in-up' },
        { selector: '.section-title', animation: 'fade-in-up' }
    ];
    
    animatedElements.forEach(({ selector, animation }) => {
        document.querySelectorAll(selector).forEach((element, index) => {
            element.classList.add(animation);
            element.style.transitionDelay = `${index * 0.1}s`;
        });
    });
    
    // Intersection Observer for scroll animations
    const scrollObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, { threshold: 0.1 });
    
    document.querySelectorAll('.fade-in-up, .fade-in-left, .fade-in-right, .scale-in')
        .forEach(el => scrollObserver.observe(el));
}

/**
 * Enhanced Search Features
 * تحسينات البحث
 */
function initSearchEnhancements() {
    const searchInput = document.querySelector('.search-input');
    const searchForm = document.querySelector('.search-form');
    
    if (!searchInput) return;
    
    // Search suggestions (mock data)
    const suggestions = [
        'سيارات للبيع',
        'شقق للإيجار',
        'هواتف ذكية',
        'وظائف في الرياض',
        'أثاث مستعمل',
        'لابتوب للبيع',
        'عقارات جدة',
        'دراجات نارية'
    ];
    
    // Create suggestions dropdown
    const suggestionsContainer = document.createElement('div');
    suggestionsContainer.className = 'search-suggestions';
    suggestionsContainer.style.cssText = `
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border-radius: 10px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
    `;
    
    searchInput.parentElement.style.position = 'relative';
    searchInput.parentElement.appendChild(suggestionsContainer);
    
    // Show suggestions on focus
    searchInput.addEventListener('focus', () => {
        showSuggestions();
    });
    
    // Filter suggestions on input
    searchInput.addEventListener('input', (e) => {
        const query = e.target.value.toLowerCase();
        if (query.length > 0) {
            const filtered = suggestions.filter(s => s.includes(query));
            showSuggestions(filtered);
        } else {
            showSuggestions();
        }
    });
    
    // Hide suggestions on blur (with delay for clicks)
    searchInput.addEventListener('blur', () => {
        setTimeout(() => {
            suggestionsContainer.style.display = 'none';
        }, 200);
    });
    
    function showSuggestions(items = suggestions) {
        suggestionsContainer.innerHTML = '';
        items.slice(0, 5).forEach(item => {
            const div = document.createElement('div');
            div.textContent = item;
            div.style.cssText = `
                padding: 10px 15px;
                cursor: pointer;
                border-bottom: 1px solid #eee;
                transition: background 0.2s;
            `;
            div.addEventListener('mouseenter', () => {
                div.style.background = '#f8f9fa';
            });
            div.addEventListener('mouseleave', () => {
                div.style.background = 'white';
            });
            div.addEventListener('click', () => {
                searchInput.value = item;
                suggestionsContainer.style.display = 'none';
                searchForm.submit();
            });
            suggestionsContainer.appendChild(div);
        });
        suggestionsContainer.style.display = items.length > 0 ? 'block' : 'none';
    }
}

/**
 * Smooth Scrolling for Anchor Links
 * التمرير السلس للروابط
 */
function initSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

/**
 * Parallax Effect for Hero Section
 * تأثير المنظور للقسم الرئيسي
 */
function initParallaxEffect() {
    const heroSection = document.querySelector('.hero-section');
    if (!heroSection) return;
    
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;
        heroSection.style.transform = `translateY(${rate}px)`;
    });
}

/**
 * Typing Effect for Hero Title
 * تأثير الكتابة للعنوان الرئيسي
 */
function initTypingEffect() {
    const heroTitle = document.querySelector('.hero-title');
    if (!heroTitle) return;
    
    const text = heroTitle.textContent;
    heroTitle.textContent = '';
    heroTitle.style.borderRight = '2px solid white';
    
    let i = 0;
    const typeWriter = () => {
        if (i < text.length) {
            heroTitle.textContent += text.charAt(i);
            i++;
            setTimeout(typeWriter, 100);
        } else {
            // Remove cursor after typing
            setTimeout(() => {
                heroTitle.style.borderRight = 'none';
            }, 1000);
        }
    };
    
    // Start typing after a delay
    setTimeout(typeWriter, 1000);
}

/**
 * Floating Elements Animation
 * تحريك العناصر العائمة
 */
function initFloatingElements() {
    const floatingElements = document.querySelectorAll('.floating-element');
    
    floatingElements.forEach((element, index) => {
        // Random initial position
        const randomX = Math.random() * 100;
        const randomY = Math.random() * 100;
        element.style.left = randomX + '%';
        element.style.top = randomY + '%';
        
        // Continuous floating animation
        setInterval(() => {
            const newX = Math.random() * 100;
            const newY = Math.random() * 100;
            element.style.transition = 'all 10s ease-in-out';
            element.style.left = newX + '%';
            element.style.top = newY + '%';
        }, 10000 + (index * 2000)); // Stagger the animations
    });
}

/**
 * Enhanced Card Hover Effects
 * تحسين تأثيرات التمرير على البطاقات
 */
function initCardEffects() {
    const cards = document.querySelectorAll('.category-card, .ad-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
            this.style.boxShadow = '0 20px 40px rgba(0,0,0,0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 5px 20px rgba(0,0,0,0.1)';
        });
    });
}

/**
 * Loading Animation
 * تحريك التحميل
 */
function showLoadingAnimation() {
    const loader = document.createElement('div');
    loader.innerHTML = `
        <div style="
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
            font-size: 1.5rem;
        ">
            <div style="text-align: center;">
                <div style="
                    width: 50px;
                    height: 50px;
                    border: 3px solid rgba(255,255,255,0.3);
                    border-top: 3px solid white;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 1rem;
                "></div>
                جاري التحميل...
            </div>
        </div>
    `;
    
    // Add spin animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);
    document.body.appendChild(loader);
    
    // Remove loader after page load
    window.addEventListener('load', () => {
        setTimeout(() => {
            loader.style.opacity = '0';
            loader.style.transition = 'opacity 0.5s';
            setTimeout(() => {
                document.body.removeChild(loader);
            }, 500);
        }, 1000);
    });
}

// Initialize loading animation
showLoadingAnimation();

{% extends 'admin_panel/base.html' %}
{% load static %}

{% block title %}تعديل الإعلان - {{ object.title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>
                        <i class="fas fa-edit me-2"></i>تعديل الإعلان
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'admin_panel:dashboard' %}">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'admin_panel:ad_list' %}">الإعلانات</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'admin_panel:ad_detail' object.pk %}">{{ object.title|truncatechars:30 }}</a></li>
                            <li class="breadcrumb-item active">تعديل</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'admin_panel:ad_detail' object.pk %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>العودة
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- نموذج التعديل -->
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-edit me-2"></i>تعديل معلومات الإعلان
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="post" enctype="multipart/form-data">
                                {% csrf_token %}
                                
                                <!-- المعلومات الأساسية -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="border-bottom pb-2 mb-3">المعلومات الأساسية</h6>
                                    </div>
                                    
                                    <div class="col-12 mb-3">
                                        <label for="id_title" class="form-label">عنوان الإعلان *</label>
                                        <input type="text" class="form-control" id="id_title" name="title" value="{{ form.title.value|default:object.title }}" required>
                                        {% if form.title.errors %}
                                            <div class="text-danger small">{{ form.title.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-12 mb-3">
                                        <label for="id_description" class="form-label">وصف الإعلان *</label>
                                        <textarea class="form-control" id="id_description" name="description" rows="5" required>{{ form.description.value|default:object.description }}</textarea>
                                        {% if form.description.errors %}
                                            <div class="text-danger small">{{ form.description.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="id_category" class="form-label">القسم *</label>
                                        <select class="form-select" id="id_category" name="category" required>
                                            <option value="">اختر القسم</option>
                                            {% for category in categories %}
                                                <option value="{{ category.id }}" {% if category.id == object.category.id %}selected{% endif %}>
                                                    {{ category.name }}
                                                </option>
                                            {% endfor %}
                                        </select>
                                        {% if form.category.errors %}
                                            <div class="text-danger small">{{ form.category.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="id_price" class="form-label">السعر (ريال)</label>
                                        <input type="number" class="form-control" id="id_price" name="price" value="{{ form.price.value|default:object.price }}" step="0.01" min="0">
                                        {% if form.price.errors %}
                                            <div class="text-danger small">{{ form.price.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="id_location" class="form-label">الموقع</label>
                                        <input type="text" class="form-control" id="id_location" name="location" value="{{ form.location.value|default:object.location }}">
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="id_phone" class="form-label">رقم الهاتف</label>
                                        <input type="text" class="form-control" id="id_phone" name="phone" value="{{ form.phone.value|default:object.phone }}">
                                    </div>
                                </div>

                                <!-- الصور الحالية -->
                                {% if object.images.exists %}
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="border-bottom pb-2 mb-3">الصور الحالية</h6>
                                    </div>
                                    {% for image in object.images.all %}
                                    <div class="col-md-3 mb-3">
                                        <div class="position-relative">
                                            <img src="{{ image.image.url }}" class="img-fluid rounded" alt="صورة الإعلان">
                                            <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0 m-1" onclick="deleteImage({{ image.id }})">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                                {% endif %}

                                <!-- إضافة صور جديدة -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="border-bottom pb-2 mb-3">إضافة صور جديدة</h6>
                                    </div>
                                    
                                    <div class="col-12">
                                        <label for="id_images" class="form-label">اختر صور جديدة</label>
                                        <input type="file" class="form-control" id="id_images" name="images" multiple accept="image/*">
                                        <div class="form-text">يمكنك اختيار عدة صور. الأنواع المدعومة: JPG, PNG, GIF</div>
                                    </div>
                                </div>

                                <!-- الحالة والإعدادات -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="border-bottom pb-2 mb-3">الحالة والإعدادات</h6>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="id_status" class="form-label">حالة الإعلان</label>
                                        <select class="form-select" id="id_status" name="status">
                                            <option value="pending" {% if object.status == 'pending' %}selected{% endif %}>في الانتظار</option>
                                            <option value="approved" {% if object.status == 'approved' %}selected{% endif %}>معتمد</option>
                                            <option value="rejected" {% if object.status == 'rejected' %}selected{% endif %}>مرفوض</option>
                                            <option value="expired" {% if object.status == 'expired' %}selected{% endif %}>منتهي الصلاحية</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <div class="form-check form-switch mt-4">
                                            <input class="form-check-input" type="checkbox" id="id_is_featured" name="is_featured" {% if object.is_featured %}checked{% endif %}>
                                            <label class="form-check-label" for="id_is_featured">
                                                إعلان مميز
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <div class="form-check form-switch mt-4">
                                            <input class="form-check-input" type="checkbox" id="id_is_urgent" name="is_urgent" {% if object.is_urgent %}checked{% endif %}>
                                            <label class="form-check-label" for="id_is_urgent">
                                                إعلان عاجل
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- أزرار الحفظ -->
                                <div class="row">
                                    <div class="col-12">
                                        <hr>
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-save me-1"></i>حفظ التغييرات
                                                </button>
                                                <a href="{% url 'admin_panel:ad_detail' object.pk %}" class="btn btn-secondary ms-2">
                                                    <i class="fas fa-times me-1"></i>إلغاء
                                                </a>
                                            </div>
                                            <div>
                                                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                                    <i class="fas fa-trash me-1"></i>حذف الإعلان
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>معلومات الإعلان
                            </h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>المعلن:</strong></td>
                                    <td>{{ object.user.get_full_name|default:object.user.username }}</td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ النشر:</strong></td>
                                    <td>{{ object.created_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>آخر تحديث:</strong></td>
                                    <td>{{ object.updated_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>عدد المشاهدات:</strong></td>
                                    <td>{{ object.views_count }}</td>
                                </tr>
                                <tr>
                                    <td><strong>عدد التقارير:</strong></td>
                                    <td>{{ object.reports.count }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-tools me-2"></i>إجراءات سريعة
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{% url 'ads:detail' object.pk %}" class="btn btn-outline-primary btn-sm" target="_blank">
                                    <i class="fas fa-external-link-alt me-1"></i>معاينة الإعلان
                                </a>
                                <a href="{% url 'admin_panel:user_detail' object.user.pk %}" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-user me-1"></i>ملف المعلن
                                </a>
                                {% if object.reports.exists %}
                                <a href="{% url 'admin_panel:report_list' %}?ad={{ object.id }}" class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-flag me-1"></i>عرض التقارير
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد حذف الإعلان</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير!</strong> هذا الإجراء لا يمكن التراجع عنه.
                </div>
                <p>هل أنت متأكد من حذف الإعلان <strong>{{ object.title }}</strong>؟</p>
                <p class="text-muted">سيتم حذف الإعلان وجميع صوره وتقاريره نهائياً.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="post" action="{% url 'admin_panel:ad_delete' object.pk %}" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>حذف نهائياً
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deleteImage(imageId) {
    if (confirm('هل تريد حذف هذه الصورة؟')) {
        fetch(`/admin-panel/delete-image/${imageId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ في حذف الصورة');
            }
        });
    }
}
</script>
{% endblock %}

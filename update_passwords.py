#!/usr/bin/env python
"""
Script to update user passwords after database import
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
# تأكد من استخدام MySQL
os.environ['USE_MYSQL'] = 'true'

django.setup()

def update_passwords():
    """تحديث كلمات مرور المستخدمين"""
    print("🔐 تحديث كلمات مرور المستخدمين")
    print("=" * 40)
    
    try:
        from accounts.models import CustomUser
        
        # تحديث كلمة مرور المدير
        print("👑 تحديث كلمة مرور المدير...")
        try:
            admin = CustomUser.objects.get(username='admin')
            admin.set_password('admin123')
            admin.save()
            print("✅ تم تحديث كلمة مرور المدير: admin / admin123")
        except CustomUser.DoesNotExist:
            print("❌ المستخدم الإداري غير موجود")
        
        # تحديث كلمات مرور المستخدمين التجريبيين
        print("\n👥 تحديث كلمات مرور المستخدمين التجريبيين...")
        
        test_users = ['user1', 'user2', 'user3']
        for username in test_users:
            try:
                user = CustomUser.objects.get(username=username)
                user.set_password('password123')
                user.save()
                print(f"✅ تم تحديث كلمة مرور {username}: {username} / password123")
            except CustomUser.DoesNotExist:
                print(f"❌ المستخدم {username} غير موجود")
        
        print("\n✅ تم تحديث جميع كلمات المرور بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث كلمات المرور: {e}")
        return False

def verify_database():
    """التحقق من قاعدة البيانات"""
    print("\n🔍 التحقق من قاعدة البيانات...")
    
    try:
        from accounts.models import CustomUser
        from ads.models import Category, Advertisement
        
        # عد المستخدمين
        users_count = CustomUser.objects.count()
        print(f"👥 المستخدمون: {users_count}")
        
        # عد الأقسام
        categories_count = Category.objects.count()
        print(f"📂 الأقسام: {categories_count}")
        
        # عد الإعلانات
        ads_count = Advertisement.objects.count()
        print(f"📢 الإعلانات: {ads_count}")
        
        # عرض أسماء الأقسام
        print("\n📋 الأقسام الموجودة:")
        for category in Category.objects.all():
            print(f"   - {category.name}")
        
        # عرض الإعلانات
        print("\n📢 الإعلانات الموجودة:")
        for ad in Advertisement.objects.all():
            status_icon = "⭐" if ad.is_featured else "📄"
            print(f"   {status_icon} {ad.title} - {ad.user.username}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق من قاعدة البيانات: {e}")
        return False

def test_login():
    """اختبار تسجيل الدخول"""
    print("\n🧪 اختبار تسجيل الدخول...")
    
    try:
        from django.contrib.auth import authenticate
        
        # اختبار المدير
        admin_user = authenticate(username='admin', password='admin123')
        if admin_user:
            print("✅ تسجيل دخول المدير يعمل")
        else:
            print("❌ فشل تسجيل دخول المدير")
        
        # اختبار مستخدم تجريبي
        test_user = authenticate(username='user1', password='password123')
        if test_user:
            print("✅ تسجيل دخول المستخدم التجريبي يعمل")
        else:
            print("❌ فشل تسجيل دخول المستخدم التجريبي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 إعداد قاعدة البيانات بعد الاستيراد")
    print("=" * 50)
    
    # 1. تحديث كلمات المرور
    if not update_passwords():
        print("❌ فشل في تحديث كلمات المرور")
        return False
    
    # 2. التحقق من قاعدة البيانات
    if not verify_database():
        print("❌ فشل في التحقق من قاعدة البيانات")
        return False
    
    # 3. اختبار تسجيل الدخول
    if not test_login():
        print("❌ فشل في اختبار تسجيل الدخول")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 تم إعداد قاعدة البيانات بنجاح!")
    print("=" * 50)
    
    print("\n🌐 الموقع جاهز للاستخدام:")
    print("- الموقع الرئيسي: http://127.0.0.1:8000/")
    print("- لوحة الإدارة: http://127.0.0.1:8000/admin-panel/")
    print("- إدارة Django: http://127.0.0.1:8000/admin/")
    
    print("\n🔑 حسابات الدخول:")
    print("- المدير: admin / admin123")
    print("- مستخدم تجريبي: user1 / password123")
    
    print("\n📋 لتشغيل الموقع:")
    print("python manage.py runserver")
    
    return True

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ تم إلغاء العملية بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        print("تأكد من:")
        print("- تشغيل خدمة MySQL")
        print("- استيراد قاعدة البيانات بنجاح")
        print("- صحة إعدادات Django")

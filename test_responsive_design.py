#!/usr/bin/env python
"""
اختبار شامل للتصميم المتجاوب
Comprehensive Responsive Design Testing
"""
import os
import django
import requests
import time
from urllib.parse import urljoin

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

def test_responsive_breakpoints():
    """اختبار نقاط التوقف المتجاوبة"""
    print("📱 اختبار نقاط التوقف المتجاوبة")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:8000"
    
    # Device configurations
    devices = {
        'Mobile Small (320px)': {
            'width': 320,
            'height': 568,
            'user_agent': 'Mozilla/5.0 (iPhone SE; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
        },
        'Mobile Medium (375px)': {
            'width': 375,
            'height': 667,
            'user_agent': 'Mozilla/5.0 (iPhone 8; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
        },
        'Mobile Large (425px)': {
            'width': 425,
            'height': 896,
            'user_agent': 'Mozilla/5.0 (iPhone 11 Pro Max; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
        },
        'Tablet Small (768px)': {
            'width': 768,
            'height': 1024,
            'user_agent': 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
        },
        'Tablet Large (1024px)': {
            'width': 1024,
            'height': 1366,
            'user_agent': 'Mozilla/5.0 (iPad Pro; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
        },
        'Desktop Small (1200px)': {
            'width': 1200,
            'height': 800,
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        'Desktop Large (1440px)': {
            'width': 1440,
            'height': 900,
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        'Desktop XL (1920px)': {
            'width': 1920,
            'height': 1080,
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
    }
    
    results = {}
    
    for device_name, config in devices.items():
        try:
            headers = {
                'User-Agent': config['user_agent'],
                'Viewport': f"width={config['width']}, height={config['height']}"
            }
            
            start_time = time.time()
            response = requests.get(base_url, headers=headers, timeout=10)
            load_time = time.time() - start_time
            
            if response.status_code == 200:
                print(f"✅ {device_name}: تحميل ناجح ({load_time:.2f}s)")
                
                # Check for responsive elements
                content = response.text
                responsive_indicators = [
                    'container-responsive',
                    'card-grid-responsive',
                    'btn-responsive',
                    'form-responsive',
                    'navbar-responsive'
                ]
                
                found_indicators = sum(1 for indicator in responsive_indicators if indicator in content)
                responsive_score = (found_indicators / len(responsive_indicators)) * 100
                
                results[device_name] = {
                    'status': 'success',
                    'load_time': load_time,
                    'responsive_score': responsive_score,
                    'width': config['width']
                }
                
                print(f"   📊 نقاط التجاوب: {responsive_score:.1f}%")
                
            else:
                print(f"❌ {device_name}: خطأ {response.status_code}")
                results[device_name] = {'status': 'error', 'code': response.status_code}
                
        except Exception as e:
            print(f"❌ {device_name}: خطأ في الاتصال")
            results[device_name] = {'status': 'error', 'error': str(e)}
    
    return results

def test_touch_targets():
    """اختبار أهداف اللمس"""
    print(f"\n👆 اختبار أهداف اللمس")
    print("-" * 30)
    
    base_url = "http://127.0.0.1:8000"
    
    try:
        response = requests.get(base_url, timeout=5)
        content = response.text
        
        # Check for touch-friendly elements
        touch_elements = [
            'btn-responsive',
            'nav-link-responsive',
            'form-input-responsive',
            'card-item-responsive'
        ]
        
        found_elements = []
        for element in touch_elements:
            if element in content:
                found_elements.append(element)
                print(f"✅ {element}: موجود")
            else:
                print(f"❌ {element}: مفقود")
        
        touch_score = (len(found_elements) / len(touch_elements)) * 100
        print(f"\n📊 نقاط أهداف اللمس: {touch_score:.1f}%")
        
        return {
            'score': touch_score,
            'found_elements': len(found_elements),
            'total_elements': len(touch_elements)
        }
        
    except Exception as e:
        print(f"❌ خطأ في اختبار أهداف اللمس: {e}")
        return {'error': str(e)}

def test_css_grid_flexbox():
    """اختبار CSS Grid و Flexbox"""
    print(f"\n🎨 اختبار CSS Grid و Flexbox")
    print("-" * 35)
    
    base_url = "http://127.0.0.1:8000"
    
    try:
        response = requests.get(base_url, timeout=5)
        content = response.text
        
        # Check for modern layout systems
        layout_systems = {
            'CSS Grid': ['grid-responsive', 'card-grid-responsive', 'stats-grid'],
            'Flexbox': ['flex-responsive', 'flex-center', 'flex-between'],
            'Container System': ['container-responsive'],
            'Form Layout': ['form-responsive', 'form-row-responsive']
        }
        
        results = {}
        
        for system_name, classes in layout_systems.items():
            found_classes = [cls for cls in classes if cls in content]
            score = (len(found_classes) / len(classes)) * 100
            results[system_name] = {
                'score': score,
                'found': len(found_classes),
                'total': len(classes)
            }
            
            print(f"✅ {system_name}: {score:.1f}% ({len(found_classes)}/{len(classes)})")
        
        overall_score = sum(result['score'] for result in results.values()) / len(results)
        print(f"\n📊 نقاط التخطيط الإجمالية: {overall_score:.1f}%")
        
        return {
            'overall_score': overall_score,
            'systems': results
        }
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التخطيط: {e}")
        return {'error': str(e)}

def test_mobile_navigation():
    """اختبار التنقل المحمول"""
    print(f"\n📱 اختبار التنقل المحمول")
    print("-" * 30)
    
    base_url = "http://127.0.0.1:8000"
    
    try:
        # Simulate mobile request
        headers = {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
        }
        
        response = requests.get(base_url, headers=headers, timeout=5)
        content = response.text
        
        # Check for mobile navigation elements
        mobile_nav_elements = [
            'navbar-responsive',
            'navbar-toggle',
            'navbar-menu',
            'nav-link-responsive',
            'hidden-mobile',
            'visible-mobile'
        ]
        
        found_elements = []
        for element in mobile_nav_elements:
            if element in content:
                found_elements.append(element)
                print(f"✅ {element}: موجود")
            else:
                print(f"❌ {element}: مفقود")
        
        nav_score = (len(found_elements) / len(mobile_nav_elements)) * 100
        print(f"\n📊 نقاط التنقل المحمول: {nav_score:.1f}%")
        
        return {
            'score': nav_score,
            'found_elements': len(found_elements),
            'total_elements': len(mobile_nav_elements)
        }
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التنقل المحمول: {e}")
        return {'error': str(e)}

def test_performance_across_devices():
    """اختبار الأداء عبر الأجهزة"""
    print(f"\n⚡ اختبار الأداء عبر الأجهزة")
    print("-" * 35)
    
    base_url = "http://127.0.0.1:8000"
    
    device_types = {
        'Mobile': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
        'Tablet': 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
        'Desktop': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    results = {}
    
    for device_type, user_agent in device_types.items():
        load_times = []
        
        for i in range(3):  # Test 3 times for average
            try:
                headers = {'User-Agent': user_agent}
                start_time = time.time()
                response = requests.get(base_url, headers=headers, timeout=10)
                load_time = time.time() - start_time
                
                if response.status_code == 200:
                    load_times.append(load_time)
                    
            except Exception:
                continue
        
        if load_times:
            avg_time = sum(load_times) / len(load_times)
            results[device_type] = avg_time
            
            # Performance rating
            if avg_time < 1:
                rating = "ممتاز 🚀"
            elif avg_time < 2:
                rating = "جيد جداً ✅"
            elif avg_time < 3:
                rating = "جيد ⚠️"
            else:
                rating = "يحتاج تحسين ❌"
            
            print(f"📱 {device_type}: {avg_time:.2f}s - {rating}")
        else:
            print(f"❌ {device_type}: فشل في الاختبار")
            results[device_type] = None
    
    return results

def generate_responsive_report():
    """إنشاء تقرير شامل للتصميم المتجاوب"""
    print("🧪 اختبار شامل للتصميم المتجاوب")
    print("=" * 70)
    
    # Run all tests
    breakpoints_test = test_responsive_breakpoints()
    touch_test = test_touch_targets()
    layout_test = test_css_grid_flexbox()
    navigation_test = test_mobile_navigation()
    performance_test = test_performance_across_devices()
    
    print(f"\n📊 التقرير النهائي للتصميم المتجاوب")
    print("=" * 70)
    
    # Calculate overall score
    scores = []
    
    # Breakpoints score
    if breakpoints_test:
        successful_devices = sum(1 for result in breakpoints_test.values() if result.get('status') == 'success')
        breakpoints_score = (successful_devices / len(breakpoints_test)) * 100
        scores.append(breakpoints_score)
        print(f"📱 نقاط التوقف: {breakpoints_score:.1f}% ({successful_devices}/{len(breakpoints_test)} أجهزة)")
    
    # Touch targets score
    if 'score' in touch_test:
        scores.append(touch_test['score'])
        print(f"👆 أهداف اللمس: {touch_test['score']:.1f}%")
    
    # Layout score
    if 'overall_score' in layout_test:
        scores.append(layout_test['overall_score'])
        print(f"🎨 التخطيط: {layout_test['overall_score']:.1f}%")
    
    # Navigation score
    if 'score' in navigation_test:
        scores.append(navigation_test['score'])
        print(f"📱 التنقل المحمول: {navigation_test['score']:.1f}%")
    
    # Performance score
    if performance_test:
        avg_performance = sum(time for time in performance_test.values() if time is not None)
        if avg_performance > 0:
            perf_score = max(0, 100 - (avg_performance * 20))  # Lower time = higher score
            scores.append(perf_score)
            print(f"⚡ الأداء: {perf_score:.1f}%")
    
    # Overall score
    if scores:
        overall_score = sum(scores) / len(scores)
        print(f"\n🏆 النتيجة الإجمالية: {overall_score:.1f}%")
        
        if overall_score >= 90:
            print(f"🎉 ممتاز! التصميم المتجاوب يعمل بشكل مثالي")
            print(f"✨ جميع الأجهزة مدعومة بالكامل")
        elif overall_score >= 75:
            print(f"✅ جيد جداً! التصميم المتجاوب يعمل بشكل جيد")
            print(f"🔧 بعض التحسينات الطفيفة مطلوبة")
        elif overall_score >= 60:
            print(f"⚠️ مقبول! التصميم يحتاج بعض التحسينات")
        else:
            print(f"❌ يحتاج تحسين! التصميم يحتاج إصلاحات جوهرية")
    
    print(f"\n🌐 الصفحات المختبرة:")
    print(f"   - الصفحة الرئيسية: http://127.0.0.1:8000/")
    print(f"   - قائمة الإعلانات: http://127.0.0.1:8000/ads/")
    print("=" * 70)
    
    return overall_score if scores else 0

def main():
    """الدالة الرئيسية"""
    score = generate_responsive_report()
    return score >= 75

if __name__ == '__main__':
    main()

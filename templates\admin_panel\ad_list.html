{% extends 'admin_panel/base.html' %}

{% block title %}إدارة الإعلانات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-bullhorn me-2"></i>
        إدارة الإعلانات
    </h1>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <input type="text" class="form-control" name="search" placeholder="البحث في الإعلانات..." value="{{ request.GET.search }}">
            </div>
            <div class="col-md-3">
                <select class="form-select" name="status">
                    <option value="">جميع الحالات</option>
                    {% for status_key, status_value in status_choices %}
                        <option value="{{ status_key }}" {% if request.GET.status == status_key %}selected{% endif %}>
                            {{ status_value }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>بحث
                </button>
                <a href="{% url 'admin_panel:ad_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i>مسح
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Ads Table -->
<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-list me-2"></i>قائمة الإعلانات ({{ ads|length }} إعلان)</h5>
    </div>
    <div class="card-body">
        {% if ads %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>العنوان</th>
                            <th>القسم</th>
                            <th>المستخدم</th>
                            <th>السعر</th>
                            <th>المشاهدات</th>
                            <th>تاريخ النشر</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for ad in ads %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    {% if ad.images.first %}
                                        <img src="{{ ad.images.first.image.url }}" class="rounded me-2" width="50" height="50" style="object-fit: cover;" alt="{{ ad.title }}">
                                    {% else %}
                                        <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    {% endif %}
                                    <div>
                                        <strong>{{ ad.title|truncatechars:30 }}</strong>
                                        {% if ad.is_featured %}
                                            <i class="fas fa-star text-warning ms-1" title="إعلان مميز"></i>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ ad.category.name }}</span>
                            </td>
                            <td>{{ ad.user.username }}</td>
                            <td>
                                {% if ad.price %}
                                    {{ ad.price }} ريال
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-info">{{ ad.views_count }}</span>
                            </td>
                            <td>{{ ad.created_at|date:"Y/m/d" }}</td>
                            <td>
                                {% if ad.status == 'approved' %}
                                    <span class="badge bg-success">موافق عليه</span>
                                {% elif ad.status == 'pending' %}
                                    <span class="badge bg-warning">في الانتظار</span>
                                {% elif ad.status == 'rejected' %}
                                    <span class="badge bg-danger">مرفوض</span>
                                {% elif ad.status == 'expired' %}
                                    <span class="badge bg-secondary">منتهي الصلاحية</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'admin_panel:ad_detail' ad.pk %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if ad.status == 'pending' %}
                                        <form method="post" action="{% url 'admin_panel:approve_ad' ad.pk %}" class="d-inline">
                                            {% csrf_token %}
                                            <button type="submit" class="btn btn-sm btn-outline-success" title="موافقة">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                        <form method="post" action="{% url 'admin_panel:reject_ad' ad.pk %}" class="d-inline">
                                            {% csrf_token %}
                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="رفض">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </form>
                                    {% endif %}
                                    <form method="post" action="{% url 'admin_panel:toggle_feature_ad' ad.pk %}" class="d-inline">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-sm {% if ad.is_featured %}btn-warning{% else %}btn-outline-warning{% endif %}" title="تمييز">
                                            <i class="fas fa-star"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
            <nav aria-label="Page navigation" class="mt-3">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page=1">الأولى</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}">السابقة</a>
                        </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                    </li>
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}">التالية</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-bullhorn fa-5x text-muted mb-3"></i>
                <h4>لا توجد إعلانات</h4>
                <p class="text-muted">لم يتم العثور على إعلانات مطابقة لمعايير البحث</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

#!/usr/bin/env python
"""
Test MariaDB 10.4 compatibility fix
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

def test_user_registration():
    """
    Test user registration functionality
    """
    print("🧪 اختبار تسجيل المستخدمين...")
    
    try:
        from accounts.models import CustomUser
        from django.db import transaction
        
        # Test creating a new user
        test_username = "test_mariadb_user"
        test_email = "<EMAIL>"
        
        # Delete existing test user if exists
        CustomUser.objects.filter(username=test_username).delete()
        
        # Create new user using the model directly
        with transaction.atomic():
            user = CustomUser(
                username=test_username,
                email=test_email,
                first_name="اختبار",
                last_name="MariaDB",
                is_verified=True
            )
            user.set_password("testpass123")
            user.save()
        
        print(f"✅ تم إنشاء المستخدم: {user.username}")
        
        # Test authentication
        from django.contrib.auth import authenticate
        auth_user = authenticate(username=test_username, password="testpass123")
        
        if auth_user:
            print("✅ تسجيل الدخول يعمل")
        else:
            print("❌ فشل تسجيل الدخول")
        
        # Clean up
        user.delete()
        print("✅ تم حذف المستخدم التجريبي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل المستخدمين: {e}")
        return False

def test_advertisement_creation():
    """
    Test advertisement creation
    """
    print("\n📢 اختبار إنشاء الإعلانات...")
    
    try:
        from ads.models import Category, Advertisement
        from accounts.models import CustomUser
        from django.db import transaction
        
        # Get existing user and category
        user = CustomUser.objects.first()
        category = Category.objects.first()
        
        if not user or not category:
            print("❌ لا توجد بيانات أساسية للاختبار")
            return False
        
        # Create test advertisement
        with transaction.atomic():
            ad = Advertisement(
                title="إعلان اختبار MariaDB",
                description="هذا إعلان اختبار للتأكد من عمل قاعدة البيانات",
                category=category,
                user=user,
                price=1000.00,
                location="الرياض",
                phone="**********",
                status="approved"
            )
            ad.save()
        
        print(f"✅ تم إنشاء الإعلان: {ad.title}")
        
        # Clean up
        ad.delete()
        print("✅ تم حذف الإعلان التجريبي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء الإعلانات: {e}")
        return False

def test_database_features():
    """
    Test database features
    """
    print("\n🔍 اختبار ميزات قاعدة البيانات...")
    
    try:
        from django.db import connection
        
        # Check database features
        features = connection.features
        
        print(f"📊 معلومات قاعدة البيانات:")
        print(f"   - النوع: {connection.vendor}")
        print(f"   - RETURNING support: {features.can_return_columns_from_insert}")
        print(f"   - Bulk RETURNING: {features.can_return_rows_from_bulk_insert}")
        
        # Test basic query
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM accounts_customuser")
            count = cursor.fetchone()[0]
            print(f"   - عدد المستخدمين: {count}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ميزات قاعدة البيانات: {e}")
        return False

def test_forms_and_views():
    """
    Test forms and views functionality
    """
    print("\n📝 اختبار النماذج والعروض...")
    
    try:
        from accounts.forms import CustomUserCreationForm
        from django.test import RequestFactory
        
        # Test form validation
        form_data = {
            'username': 'test_form_user',
            'email': '<EMAIL>',
            'first_name': 'اختبار',
            'last_name': 'النموذج',
            'password1': 'testpass123456',
            'password2': 'testpass123456',
            'phone': '**********'
        }
        
        form = CustomUserCreationForm(data=form_data)
        
        if form.is_valid():
            print("✅ النموذج صحيح")
            
            # Test saving (but don't actually save)
            # user = form.save(commit=False)
            # print(f"✅ يمكن حفظ المستخدم: {user.username}")
            
        else:
            print(f"❌ النموذج غير صحيح: {form.errors}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النماذج: {e}")
        return False

def main():
    """
    Main test function
    """
    print("🧪 اختبار إصلاحات MariaDB 10.4")
    print("=" * 50)
    
    all_tests_passed = True
    
    # 1. Test database features
    if not test_database_features():
        all_tests_passed = False
    
    # 2. Test user registration
    if not test_user_registration():
        all_tests_passed = False
    
    # 3. Test advertisement creation
    if not test_advertisement_creation():
        all_tests_passed = False
    
    # 4. Test forms and views
    if not test_forms_and_views():
        all_tests_passed = False
    
    print("\n" + "=" * 50)
    if all_tests_passed:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ الموقع جاهز للاستخدام مع MariaDB 10.4")
        
        print("\n🌐 يمكنك الآن:")
        print("- تشغيل الموقع: python manage.py runserver")
        print("- تسجيل مستخدمين جدد")
        print("- إنشاء إعلانات")
        print("- استخدام جميع ميزات الموقع")
        
    else:
        print("❌ بعض الاختبارات فشلت")
        print("يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 50)
    
    return all_tests_passed

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")
        sys.exit(1)

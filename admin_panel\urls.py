from django.urls import path
from . import views

app_name = 'admin_panel'

urlpatterns = [
    # لوحة التحكم الرئيسية
    path('', views.DashboardView.as_view(), name='dashboard'),
    
    # إدارة المستخدمين
    path('users/', views.UserListView.as_view(), name='user_list'),
    path('users/<int:pk>/', views.UserDetailView.as_view(), name='user_detail'),
    path('users/<int:pk>/toggle-status/', views.ToggleUserStatusView.as_view(), name='toggle_user_status'),
    
    # إدارة الإعلانات
    path('ads/', views.AdminAdListView.as_view(), name='ad_list'),
    path('ads/<int:pk>/', views.AdminAdDetailView.as_view(), name='ad_detail'),
    path('ads/<int:pk>/approve/', views.ApproveAdView.as_view(), name='approve_ad'),
    path('ads/<int:pk>/reject/', views.RejectAdView.as_view(), name='reject_ad'),
    path('ads/<int:pk>/feature/', views.ToggleFeatureAdView.as_view(), name='toggle_feature_ad'),

    # إدارة الإعلانات المعلقة (النظام الجديد)
    path('pending-ads/', views.PendingAdsView.as_view(), name='pending_ads'),
    path('approve-ad/<int:ad_id>/', views.approve_advertisement, name='approve_ad_new'),
    path('reject-ad/<int:ad_id>/', views.reject_advertisement, name='reject_ad_new'),
    path('bulk-approve/', views.bulk_approve_ads, name='bulk_approve'),
    
    # إدارة الأقسام
    path('categories/', views.CategoryListView.as_view(), name='category_list'),
    path('categories/create/', views.CategoryCreateView.as_view(), name='category_create'),
    path('categories/<int:pk>/edit/', views.CategoryUpdateView.as_view(), name='category_edit'),
    path('categories/<int:pk>/delete/', views.CategoryDeleteView.as_view(), name='category_delete'),
    
    # إدارة التقارير
    path('reports/', views.ReportListView.as_view(), name='report_list'),
    path('reports/<int:pk>/', views.ReportDetailView.as_view(), name='report_detail'),
    path('reports/<int:pk>/resolve/', views.ResolveReportView.as_view(), name='resolve_report'),
    
    # الإحصائيات
    path('statistics/', views.StatisticsView.as_view(), name='statistics'),
]

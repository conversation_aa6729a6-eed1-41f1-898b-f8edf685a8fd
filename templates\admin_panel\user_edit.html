{% extends 'admin_panel/base.html' %}
{% load static %}

{% block title %}تعديل المستخدم - {{ object.get_full_name|default:object.username }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>
                        <i class="fas fa-user-edit me-2"></i>تعديل المستخدم
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'admin_panel:dashboard' %}">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'admin_panel:user_list' %}">المستخدمون</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'admin_panel:user_detail' object.pk %}">{{ object.get_full_name|default:object.username }}</a></li>
                            <li class="breadcrumb-item active">تعديل</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'admin_panel:user_detail' object.pk %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>العودة
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- نموذج التعديل -->
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-edit me-2"></i>تعديل معلومات المستخدم
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="post" enctype="multipart/form-data">
                                {% csrf_token %}
                                
                                <!-- المعلومات الأساسية -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="border-bottom pb-2 mb-3">المعلومات الأساسية</h6>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="id_username" class="form-label">اسم المستخدم *</label>
                                        <input type="text" class="form-control" id="id_username" name="username" value="{{ form.username.value|default:object.username }}" required>
                                        {% if form.username.errors %}
                                            <div class="text-danger small">{{ form.username.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="id_email" class="form-label">البريد الإلكتروني *</label>
                                        <input type="email" class="form-control" id="id_email" name="email" value="{{ form.email.value|default:object.email }}">
                                        {% if form.email.errors %}
                                            <div class="text-danger small">{{ form.email.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="id_first_name" class="form-label">الاسم الأول</label>
                                        <input type="text" class="form-control" id="id_first_name" name="first_name" value="{{ form.first_name.value|default:object.first_name }}">
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="id_last_name" class="form-label">اسم العائلة</label>
                                        <input type="text" class="form-control" id="id_last_name" name="last_name" value="{{ form.last_name.value|default:object.last_name }}">
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="id_phone" class="form-label">رقم الهاتف</label>
                                        <input type="text" class="form-control" id="id_phone" name="phone" value="{{ form.phone.value|default:object.phone }}">
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="id_location" class="form-label">الموقع</label>
                                        <input type="text" class="form-control" id="id_location" name="location" value="{{ form.location.value|default:object.location }}">
                                    </div>
                                </div>

                                <!-- صورة المستخدم -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="border-bottom pb-2 mb-3">صورة المستخدم</h6>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        {% if object.profile_picture %}
                                            <img src="{{ object.profile_picture.url }}" class="img-fluid rounded" alt="صورة المستخدم الحالية">
                                            <p class="small text-muted mt-2">الصورة الحالية</p>
                                        {% else %}
                                            <div class="bg-light p-4 rounded text-center">
                                                <i class="fas fa-user fa-3x text-muted"></i>
                                                <p class="small text-muted mt-2">لا توجد صورة</p>
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-8">
                                        <label for="id_profile_picture" class="form-label">تغيير الصورة</label>
                                        <input type="file" class="form-control" id="id_profile_picture" name="profile_picture" accept="image/*">
                                        <div class="form-text">اختر صورة جديدة (اختياري). الأنواع المدعومة: JPG, PNG, GIF</div>
                                    </div>
                                </div>

                                <!-- الحالة والصلاحيات -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="border-bottom pb-2 mb-3">الحالة والصلاحيات</h6>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="id_is_active" name="is_active" {% if object.is_active %}checked{% endif %}>
                                            <label class="form-check-label" for="id_is_active">
                                                حساب نشط
                                            </label>
                                        </div>
                                        <div class="form-text">إلغاء التفعيل يمنع المستخدم من تسجيل الدخول</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="id_is_verified" name="is_verified" {% if object.is_verified %}checked{% endif %}>
                                            <label class="form-check-label" for="id_is_verified">
                                                حساب موثق
                                            </label>
                                        </div>
                                        <div class="form-text">الحسابات الموثقة تحصل على مزايا إضافية</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="id_is_staff" name="is_staff" {% if object.is_staff %}checked{% endif %}>
                                            <label class="form-check-label" for="id_is_staff">
                                                موظف
                                            </label>
                                        </div>
                                        <div class="form-text">الموظفون يمكنهم الوصول للوحة الإدارة</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="id_is_superuser" name="is_superuser" {% if object.is_superuser %}checked{% endif %}>
                                            <label class="form-check-label" for="id_is_superuser">
                                                مدير عام
                                            </label>
                                        </div>
                                        <div class="form-text text-warning">المديرون العامون لديهم صلاحيات كاملة</div>
                                    </div>
                                </div>

                                <!-- أزرار الحفظ -->
                                <div class="row">
                                    <div class="col-12">
                                        <hr>
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-save me-1"></i>حفظ التغييرات
                                                </button>
                                                <a href="{% url 'admin_panel:user_detail' object.pk %}" class="btn btn-secondary ms-2">
                                                    <i class="fas fa-times me-1"></i>إلغاء
                                                </a>
                                            </div>
                                            <div>
                                                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                                    <i class="fas fa-trash me-1"></i>حذف المستخدم
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>معلومات الحساب
                            </h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>تاريخ التسجيل:</strong></td>
                                    <td>{{ object.date_joined|date:"Y-m-d" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>آخر دخول:</strong></td>
                                    <td>{{ object.last_login|date:"Y-m-d H:i"|default:"لم يسجل دخول" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>عدد الإعلانات:</strong></td>
                                    <td>{{ object.advertisements.count }}</td>
                                </tr>
                                <tr>
                                    <td><strong>عدد التقارير:</strong></td>
                                    <td>{{ object.reports_made.count }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-shield-alt me-2"></i>إجراءات الأمان
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-key me-1"></i>إعادة تعيين كلمة المرور
                                </button>
                                <button type="button" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-envelope me-1"></i>إرسال رسالة تحقق
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-ban me-1"></i>تعليق الحساب مؤقتاً
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد حذف المستخدم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير!</strong> هذا الإجراء لا يمكن التراجع عنه.
                </div>
                <p>هل أنت متأكد من حذف المستخدم <strong>{{ object.get_full_name|default:object.username }}</strong>؟</p>
                <p class="text-muted">سيتم حذف جميع إعلانات المستخدم وبياناته نهائياً.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="post" action="{% url 'admin_panel:user_delete' object.pk %}" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>حذف نهائياً
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% extends 'admin_panel/base.html' %}
{% load static %}

{% block title %}حذف الإعلان{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-trash text-danger me-2"></i>حذف الإعلان
                </h2>
                <a href="{% url 'admin_panel:ad_detail' ad.pk %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>العودة
                </a>
            </div>

            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                تأكيد حذف الإعلان
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-danger">
                                <i class="fas fa-warning me-2"></i>
                                <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه. سيتم حذف الإعلان نهائياً من النظام.
                            </div>

                            <div class="row">
                                <div class="col-md-8">
                                    <h6 class="text-danger mb-3">هل أنت متأكد من حذف هذا الإعلان؟</h6>
                                    
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <h6 class="card-title">{{ ad.title }}</h6>
                                            <p class="card-text text-muted">{{ ad.description|truncatewords:20 }}</p>
                                            
                                            <div class="row text-sm">
                                                <div class="col-6">
                                                    <strong>المعلن:</strong> {{ ad.user.get_full_name|default:ad.user.username }}
                                                </div>
                                                <div class="col-6">
                                                    <strong>القسم:</strong> {{ ad.category.name }}
                                                </div>
                                                <div class="col-6">
                                                    <strong>السعر:</strong> 
                                                    {% if ad.price %}
                                                        {{ ad.price }} ريال
                                                    {% else %}
                                                        غير محدد
                                                    {% endif %}
                                                </div>
                                                <div class="col-6">
                                                    <strong>تاريخ الإنشاء:</strong> {{ ad.created_at|date:"Y-m-d" }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-info-circle me-1"></i>ما سيحدث عند الحذف:</h6>
                                        <ul class="mb-0">
                                            <li>سيتم حذف الإعلان نهائياً من قاعدة البيانات</li>
                                            <li>سيتم حذف جميع الصور المرتبطة بالإعلان</li>
                                            <li>سيتم إرسال إشعار للمستخدم بحذف الإعلان</li>
                                            <li>سيتم تسجيل هذا الإجراء في سجل الأنشطة</li>
                                        </ul>
                                    </div>

                                    <form method="post" class="mt-4">
                                        {% csrf_token %}
                                        <div class="d-flex justify-content-between">
                                            <a href="{% url 'admin_panel:ad_detail' ad.pk %}" class="btn btn-secondary">
                                                <i class="fas fa-times me-1"></i>إلغاء
                                            </a>
                                            <button type="submit" class="btn btn-danger">
                                                <i class="fas fa-trash me-1"></i>نعم، احذف الإعلان
                                            </button>
                                        </div>
                                    </form>
                                </div>

                                <div class="col-md-4">
                                    {% if ad.images.exists %}
                                        <h6>صور الإعلان:</h6>
                                        <div class="row">
                                            {% for image in ad.images.all %}
                                                <div class="col-6 mb-2">
                                                    <img src="{{ image.image.url }}" class="img-fluid rounded" 
                                                         style="height: 80px; object-fit: cover; width: 100%;">
                                                </div>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        <div class="text-center text-muted">
                                            <i class="fas fa-image fa-3x mb-2"></i>
                                            <p>لا توجد صور</p>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تأكيد إضافي قبل الحذف
document.querySelector('form').addEventListener('submit', function(e) {
    if (!confirm('هل أنت متأكد تماماً من حذف هذا الإعلان؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        e.preventDefault();
    }
});
</script>
{% endblock %}

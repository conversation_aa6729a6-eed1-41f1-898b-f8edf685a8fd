{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}تعديل الملف الشخصي{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-edit me-2"></i>تعديل الملف الشخصي</h3>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    {{ form|crispy }}
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'accounts:profile' %}" class="btn btn-secondary me-md-2">إلغاء</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

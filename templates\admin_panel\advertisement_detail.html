{% extends 'admin_panel/base.html' %}
{% load static %}

{% block title %}تفاصيل الإعلان - {{ ad.title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>
                        <i class="fas fa-bullhorn me-2"></i>تفاصيل الإعلان
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'admin_panel:dashboard' %}">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'admin_panel:ad_list' %}">الإعلانات</a></li>
                            <li class="breadcrumb-item active">{{ ad.title|truncatechars:30 }}</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'admin_panel:ad_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>العودة للقائمة
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- تفاصيل الإعلان -->
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>معلومات الإعلان
                            </h5>
                            <div>
                                {% if ad.status == 'approved' %}
                                    <span class="badge bg-success">معتمد</span>
                                {% elif ad.status == 'pending' %}
                                    <span class="badge bg-warning">في الانتظار</span>
                                {% elif ad.status == 'rejected' %}
                                    <span class="badge bg-danger">مرفوض</span>
                                {% elif ad.status == 'expired' %}
                                    <span class="badge bg-secondary">منتهي الصلاحية</span>
                                {% endif %}
                                
                                {% if ad.is_featured %}
                                    <span class="badge bg-primary">مميز</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- صور الإعلان -->
                            {% if ad.images.exists %}
                            <div class="mb-4">
                                <h6>صور الإعلان:</h6>
                                <div class="row">
                                    {% for image in ad.images.all %}
                                    <div class="col-md-4 mb-3">
                                        <img src="{{ image.image.url }}" class="img-fluid rounded" alt="صورة الإعلان">
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}

                            <!-- معلومات أساسية -->
                            <table class="table table-borderless">
                                <tr>
                                    <td width="150"><strong>العنوان:</strong></td>
                                    <td>{{ ad.title }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الوصف:</strong></td>
                                    <td>{{ ad.description|linebreaks }}</td>
                                </tr>
                                <tr>
                                    <td><strong>القسم:</strong></td>
                                    <td>{{ ad.category.name }}</td>
                                </tr>
                                {% if ad.price %}
                                <tr>
                                    <td><strong>السعر:</strong></td>
                                    <td class="text-success fw-bold">{{ ad.price|floatformat:0 }} ريال</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td><strong>الموقع:</strong></td>
                                    <td>{{ ad.location|default:"غير محدد" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>رقم الهاتف:</strong></td>
                                    <td>{{ ad.phone|default:"غير محدد" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ الإنشاء:</strong></td>
                                    <td>{{ ad.created_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>آخر تحديث:</strong></td>
                                    <td>{{ ad.updated_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>عدد المشاهدات:</strong></td>
                                    <td>{{ ad.views_count }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- معلومات المستخدم والإجراءات -->
                <div class="col-lg-4 mb-4">
                    <!-- معلومات المستخدم -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-user me-2"></i>معلومات المعلن
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                {% if ad.user.profile_picture %}
                                    <img src="{{ ad.user.profile_picture.url }}" class="rounded-circle" width="60" height="60" alt="صورة المستخدم">
                                {% else %}
                                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                        <i class="fas fa-user fa-2x text-white"></i>
                                    </div>
                                {% endif %}
                            </div>
                            
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>الاسم:</strong></td>
                                    <td>{{ ad.user.get_full_name|default:ad.user.username }}</td>
                                </tr>
                                <tr>
                                    <td><strong>البريد:</strong></td>
                                    <td>{{ ad.user.email|default:"غير محدد" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الهاتف:</strong></td>
                                    <td>{{ ad.user.phone|default:"غير محدد" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>إعلاناته:</strong></td>
                                    <td>{{ ad.user.advertisements.count }}</td>
                                </tr>
                            </table>
                            
                            <div class="d-grid">
                                <a href="{% url 'admin_panel:user_detail' ad.user.pk %}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>عرض الملف الشخصي
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- إجراءات الإدارة -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-cogs me-2"></i>إجراءات الإدارة
                            </h6>
                        </div>
                        <div class="card-body">
                            {% if can_approve %}
                            <form method="post" action="{% url 'admin_panel:approve_ad_new' ad.id %}" class="mb-2">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-success w-100" onclick="return confirm('هل تريد اعتماد هذا الإعلان؟')">
                                    <i class="fas fa-check me-1"></i>اعتماد الإعلان
                                </button>
                            </form>
                            {% endif %}

                            {% if can_reject %}
                            <form method="post" action="{% url 'admin_panel:reject_ad_new' ad.id %}" class="mb-2">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-danger w-100" onclick="return confirm('هل تريد رفض هذا الإعلان؟')">
                                    <i class="fas fa-times me-1"></i>رفض الإعلان
                                </button>
                            </form>
                            {% endif %}

                            <a href="{% url 'ads:detail' ad.pk %}" class="btn btn-outline-primary w-100 mb-2" target="_blank">
                                <i class="fas fa-external-link-alt me-1"></i>معاينة في الموقع
                            </a>

                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-h me-1"></i>المزيد
                                </button>
                                <ul class="dropdown-menu w-100">
                                    <li>
                                        <a class="dropdown-item" href="#">
                                            <i class="fas fa-star me-2"></i>تمييز الإعلان
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#">
                                            <i class="fas fa-edit me-2"></i>تعديل الإعلان
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item text-danger" href="#" onclick="return confirm('هل تريد حذف هذا الإعلان؟')">
                                            <i class="fas fa-trash me-2"></i>حذف الإعلان
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التقارير إن وجدت -->
            {% if ad.reports.exists %}
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-flag me-2"></i>التقارير المرسلة ({{ ad.reports.count }})
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>المبلغ</th>
                                            <th>السبب</th>
                                            <th>الوصف</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for report in ad.reports.all %}
                                        <tr>
                                            <td>{{ report.user.get_full_name|default:report.user.username }}</td>
                                            <td>{{ report.get_reason_display }}</td>
                                            <td>{{ report.description|truncatechars:50 }}</td>
                                            <td>{{ report.created_at|date:"Y-m-d H:i" }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

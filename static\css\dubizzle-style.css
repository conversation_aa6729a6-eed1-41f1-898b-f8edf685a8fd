/* 
===========================================
Dubizzle Style CSS - نظام تصميم متقدم
===========================================
*/

/* متغيرات الألوان الأساسية */
:root {
    /* Dubizzle Brand Colors */
    --dubizzle-primary: #ff6b35;
    --dubizzle-primary-dark: #e55a2b;
    --dubizzle-primary-light: #ff8c66;
    --dubizzle-secondary: #2c3e50;
    --dubizzle-accent: #3498db;
    
    /* Neutral Colors */
    --neutral-50: #f8fafc;
    --neutral-100: #f1f5f9;
    --neutral-200: #e2e8f0;
    --neutral-300: #cbd5e1;
    --neutral-400: #94a3b8;
    --neutral-500: #64748b;
    --neutral-600: #475569;
    --neutral-700: #334155;
    --neutral-800: #1e293b;
    --neutral-900: #0f172a;
    
    /* Success & Status Colors */
    --success-500: #10b981;
    --warning-500: #f59e0b;
    --error-500: #ef4444;
    --info-500: #3b82f6;
    
    /* Spacing System */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    
    /* Typography */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-full: 9999px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--neutral-700);
    background-color: var(--neutral-50);
    direction: rtl;
    text-align: right;
}

/* Header Styles - Dubizzle Inspired */
.dubizzle-header {
    background: white;
    box-shadow: var(--shadow-md);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid var(--neutral-200);
}

.header-top {
    background: var(--neutral-100);
    padding: var(--space-2) 0;
    font-size: var(--font-size-sm);
}

.header-top .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.header-links {
    display: flex;
    gap: var(--space-4);
}

.header-links a {
    color: var(--neutral-600);
    text-decoration: none;
    transition: var(--transition-fast);
}

.header-links a:hover {
    color: var(--dubizzle-primary);
}

.header-main {
    padding: var(--space-4) 0;
}

.header-main .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
    gap: var(--space-6);
}

/* Logo Styles */
.dubizzle-logo {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    text-decoration: none;
    color: var(--dubizzle-primary);
    font-weight: 700;
    font-size: var(--font-size-2xl);
    min-width: 150px;
}

.dubizzle-logo i {
    font-size: var(--font-size-3xl);
}

/* Search Bar - Central & Large */
.dubizzle-search {
    flex: 1;
    max-width: 600px;
    position: relative;
}

.search-form {
    display: flex;
    background: white;
    border: 2px solid var(--neutral-300);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
}

.search-form:focus-within {
    border-color: var(--dubizzle-primary);
    box-shadow: var(--shadow-lg);
}

.search-input {
    flex: 1;
    padding: var(--space-4) var(--space-5);
    border: none;
    outline: none;
    font-size: var(--font-size-lg);
    background: transparent;
}

.search-input::placeholder {
    color: var(--neutral-400);
}

.search-category-select {
    padding: var(--space-4) var(--space-3);
    border: none;
    border-left: 1px solid var(--neutral-300);
    background: var(--neutral-50);
    color: var(--neutral-600);
    font-size: var(--font-size-base);
    min-width: 120px;
    cursor: pointer;
}

.search-btn {
    background: var(--dubizzle-primary);
    color: white;
    border: none;
    padding: var(--space-4) var(--space-6);
    font-size: var(--font-size-lg);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.search-btn:hover {
    background: var(--dubizzle-primary-dark);
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    min-width: 200px;
    justify-content: flex-end;
}

.header-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-lg);
    text-decoration: none;
    color: var(--neutral-700);
    font-weight: 500;
    transition: var(--transition-fast);
    background: white;
}

.header-btn:hover {
    border-color: var(--dubizzle-primary);
    color: var(--dubizzle-primary);
}

.header-btn.primary {
    background: var(--dubizzle-primary);
    color: white;
    border-color: var(--dubizzle-primary);
}

.header-btn.primary:hover {
    background: var(--dubizzle-primary-dark);
}

/* Navigation Menu */
.dubizzle-nav {
    background: var(--neutral-50);
    border-top: 1px solid var(--neutral-200);
    padding: var(--space-3) 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.nav-menu {
    display: flex;
    gap: var(--space-6);
    list-style: none;
    overflow-x: auto;
    padding: var(--space-2) 0;
}

.nav-item {
    white-space: nowrap;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    text-decoration: none;
    color: var(--neutral-600);
    font-weight: 500;
    border-radius: var(--radius-lg);
    transition: var(--transition-fast);
}

.nav-link:hover {
    background: white;
    color: var(--dubizzle-primary);
    box-shadow: var(--shadow-sm);
}

.nav-link.active {
    background: var(--dubizzle-primary);
    color: white;
}

/* Search Suggestions */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--neutral-300);
    border-top: none;
    border-radius: 0 0 var(--radius-xl) var(--radius-xl);
    box-shadow: var(--shadow-lg);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1001;
    display: none;
}

.search-suggestions.show {
    display: block;
}

.suggestion-item {
    padding: var(--space-3) var(--space-5);
    border-bottom: 1px solid var(--neutral-100);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.suggestion-item:hover {
    background: var(--neutral-50);
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-icon {
    color: var(--neutral-400);
    font-size: var(--font-size-sm);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-main .container {
        flex-direction: column;
        gap: var(--space-4);
    }
    
    .dubizzle-search {
        max-width: 100%;
        order: 2;
    }
    
    .header-actions {
        order: 1;
        justify-content: space-between;
        width: 100%;
        min-width: auto;
    }
    
    .dubizzle-logo {
        order: 0;
        justify-content: center;
        width: 100%;
    }
    
    .nav-menu {
        gap: var(--space-3);
    }
    
    .nav-link {
        padding: var(--space-2) var(--space-3);
        font-size: var(--font-size-sm);
    }
    
    .search-form {
        flex-direction: column;
    }
    
    .search-category-select {
        border-left: none;
        border-bottom: 1px solid var(--neutral-300);
    }
}

@media (max-width: 480px) {
    .header-actions {
        flex-direction: column;
        gap: var(--space-2);
    }
    
    .header-btn {
        width: 100%;
        justify-content: center;
    }
    
    .nav-menu {
        justify-content: center;
    }
}

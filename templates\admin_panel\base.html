<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}لوحة الإدارة - موقع الإعلانات{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 0.375rem;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        .main-content {
            padding: 2rem;
        }
        .card {
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-radius: 0.5rem;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .stats-card .card-body {
            padding: 1.5rem;
        }
        .navbar-admin {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-cogs me-2"></i>
                            لوحة الإدارة
                        </h4>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}" 
                               href="{% url 'admin_panel:dashboard' %}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                الرئيسية
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link {% if 'user' in request.resolver_match.url_name %}active{% endif %}" 
                               href="{% url 'admin_panel:user_list' %}">
                                <i class="fas fa-users me-2"></i>
                                المستخدمون
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link {% if 'ad' in request.resolver_match.url_name and 'admin' in request.resolver_match.url_name %}active{% endif %}" 
                               href="{% url 'admin_panel:ad_list' %}">
                                <i class="fas fa-bullhorn me-2"></i>
                                الإعلانات
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link {% if 'category' in request.resolver_match.url_name %}active{% endif %}" 
                               href="{% url 'admin_panel:category_list' %}">
                                <i class="fas fa-tags me-2"></i>
                                الأقسام
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link {% if 'report' in request.resolver_match.url_name %}active{% endif %}" 
                               href="{% url 'admin_panel:report_list' %}">
                                <i class="fas fa-flag me-2"></i>
                                التقارير
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'statistics' %}active{% endif %}"
                               href="{% url 'admin_panel:statistics' %}">
                                <i class="fas fa-chart-bar me-2"></i>
                                الإحصائيات
                            </a>
                        </li>

                        <hr class="text-white-50">

                        <!-- الميزات المتقدمة -->
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'settings' %}active{% endif %}"
                               href="{% url 'admin_panel:settings' %}">
                                <i class="fas fa-cogs me-2"></i>
                                إعدادات النظام
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'activity_log' %}active{% endif %}"
                               href="{% url 'admin_panel:activity_log' %}">
                                <i class="fas fa-history me-2"></i>
                                سجل الأنشطة
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'backup' %}active{% endif %}"
                               href="{% url 'admin_panel:backup' %}">
                                <i class="fas fa-database me-2"></i>
                                النسخ الاحتياطية
                            </a>
                        </li>

                        <hr class="text-white-50">

                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'home' %}">
                                <i class="fas fa-home me-2"></i>
                                العودة للموقع
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'accounts:logout' %}">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- Top navbar -->
                <div class="navbar navbar-admin navbar-expand-lg navbar-light mb-4">
                    <div class="container-fluid">
                        <button class="navbar-toggler d-md-none" type="button" data-bs-toggle="collapse" data-bs-target=".sidebar">
                            <span class="navbar-toggler-icon"></span>
                        </button>
                        
                        <div class="navbar-nav ms-auto">
                            <!-- إشعارات فورية -->
                            <div class="nav-item dropdown me-3">
                                <a class="nav-link position-relative" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-bell"></i>
                                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="notificationBadge" style="display: none;">
                                        0
                                    </span>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" style="width: 350px; max-height: 400px; overflow-y: auto;">
                                    <li class="dropdown-header d-flex justify-content-between align-items-center">
                                        <span>الإشعارات</span>
                                        <button class="btn btn-sm btn-outline-primary" onclick="markAllAsRead()">
                                            <i class="fas fa-check-double"></i>
                                        </button>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <div id="notificationsList">
                                        <li class="dropdown-item text-center text-muted">
                                            <i class="fas fa-bell-slash"></i>
                                            <br>لا توجد إشعارات جديدة
                                        </li>
                                    </div>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item text-center" href="{% url 'ads:notifications' %}">
                                            عرض جميع الإشعارات
                                        </a>
                                    </li>
                                </ul>
                            </div>

                            <div class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user-circle me-1"></i>
                                    {{ user.username }}
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">الملف الشخصي</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'accounts:logout' %}">تسجيل الخروج</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Messages -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
                
                <!-- Page content -->
                <div class="main-content">
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- نظام الإشعارات الفوري -->
    <script>
    // تحديث الإشعارات كل 30 ثانية
    function updateNotifications() {
        fetch('/ads/api/notifications/recent/')
            .then(response => response.json())
            .then(data => {
                const badge = document.getElementById('notificationBadge');
                const list = document.getElementById('notificationsList');

                if (data.unread_count > 0) {
                    badge.textContent = data.unread_count;
                    badge.style.display = 'block';
                } else {
                    badge.style.display = 'none';
                }

                if (data.notifications && data.notifications.length > 0) {
                    list.innerHTML = '';
                    data.notifications.forEach(notification => {
                        const item = document.createElement('li');
                        item.className = `dropdown-item ${!notification.is_read ? 'bg-light' : ''}`;
                        item.innerHTML = `
                            <div class="d-flex">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">${notification.title}</h6>
                                    <p class="mb-1 small">${notification.message}</p>
                                    <small class="text-muted">${notification.created_at}</small>
                                </div>
                                ${!notification.is_read ? '<div class="text-primary"><i class="fas fa-circle small"></i></div>' : ''}
                            </div>
                        `;
                        item.onclick = () => markAsRead(notification.id);
                        list.appendChild(item);
                    });
                } else {
                    list.innerHTML = `
                        <li class="dropdown-item text-center text-muted">
                            <i class="fas fa-bell-slash"></i>
                            <br>لا توجد إشعارات جديدة
                        </li>
                    `;
                }
            })
            .catch(error => console.error('خطأ في جلب الإشعارات:', error));
    }

    function markAsRead(notificationId) {
        fetch(`/ads/notifications/${notificationId}/read/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCsrfToken()
            }
        }).then(() => updateNotifications());
    }

    function markAllAsRead() {
        fetch('/ads/notifications/mark-all-read/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCsrfToken()
            }
        }).then(() => updateNotifications());
    }

    function getCsrfToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '{{ csrf_token }}';
    }

    // تحديث الإشعارات عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        updateNotifications();
        // تحديث كل 30 ثانية
        setInterval(updateNotifications, 30000);
    });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>

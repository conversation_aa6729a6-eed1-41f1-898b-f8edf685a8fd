#!/usr/bin/env python
"""
اختبار شامل لصفحة الهبوط الجديدة
"""
import os
import django
import requests
import time
from urllib.parse import urljoin

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

def test_landing_page_load():
    """اختبار تحميل صفحة الهبوط"""
    print("🌐 اختبار تحميل صفحة الهبوط الجديدة")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:8000"
    
    try:
        start_time = time.time()
        response = requests.get(base_url, timeout=10)
        load_time = time.time() - start_time
        
        if response.status_code == 200:
            print(f"✅ الصفحة تحملت بنجاح")
            print(f"⏱️ وقت التحميل: {load_time:.2f} ثانية")
            
            # فحص المحتوى
            content = response.text
            
            # فحص العناصر المطلوبة
            required_elements = [
                'hero-section',
                'categories-section', 
                'featured-section',
                'stats-section',
                'search-container',
                'cta-buttons'
            ]
            
            found_elements = []
            missing_elements = []
            
            for element in required_elements:
                if element in content:
                    found_elements.append(element)
                    print(f"✅ {element}: موجود")
                else:
                    missing_elements.append(element)
                    print(f"❌ {element}: مفقود")
            
            # فحص النصوص المطلوبة
            required_texts = [
                'أفضل موقع للإعلانات المبوبة',
                'ابحث عن أي شيء',
                'أضف إعلانك مجاناً',
                'الإعلانات المميزة',
                'تصفح حسب الأقسام'
            ]
            
            found_texts = []
            for text in required_texts:
                if text in content:
                    found_texts.append(text)
                    print(f"✅ النص '{text}': موجود")
                else:
                    print(f"❌ النص '{text}': مفقود")
            
            return {
                'success': True,
                'load_time': load_time,
                'found_elements': len(found_elements),
                'total_elements': len(required_elements),
                'found_texts': len(found_texts),
                'total_texts': len(required_texts)
            }
            
        else:
            print(f"❌ فشل في تحميل الصفحة: {response.status_code}")
            return {'success': False, 'error': f'HTTP {response.status_code}'}
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return {'success': False, 'error': str(e)}

def test_responsive_design():
    """اختبار التصميم المتجاوب"""
    print(f"\n📱 اختبار التصميم المتجاوب")
    print("-" * 30)
    
    base_url = "http://127.0.0.1:8000"
    
    # محاكاة أجهزة مختلفة
    user_agents = {
        'Desktop': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Mobile': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
        'Tablet': 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
    }
    
    results = {}
    
    for device, user_agent in user_agents.items():
        try:
            headers = {'User-Agent': user_agent}
            response = requests.get(base_url, headers=headers, timeout=5)
            
            if response.status_code == 200:
                print(f"✅ {device}: يعمل بشكل صحيح")
                results[device] = True
            else:
                print(f"❌ {device}: خطأ {response.status_code}")
                results[device] = False
                
        except Exception as e:
            print(f"❌ {device}: خطأ في الاتصال")
            results[device] = False
    
    return results

def test_static_files():
    """اختبار الملفات الثابتة"""
    print(f"\n📁 اختبار الملفات الثابتة")
    print("-" * 30)
    
    base_url = "http://127.0.0.1:8000"
    static_files = [
        '/static/js/landing-page.js',
        '/static/css/bootstrap.min.css',  # إذا كان محلي
    ]
    
    results = {}
    
    for file_path in static_files:
        try:
            full_url = urljoin(base_url, file_path)
            response = requests.get(full_url, timeout=5)
            
            if response.status_code == 200:
                print(f"✅ {file_path}: متاح")
                results[file_path] = True
            else:
                print(f"❌ {file_path}: غير متاح ({response.status_code})")
                results[file_path] = False
                
        except Exception as e:
            print(f"❌ {file_path}: خطأ في الاتصال")
            results[file_path] = False
    
    return results

def test_seo_elements():
    """اختبار عناصر SEO"""
    print(f"\n🔍 اختبار عناصر SEO")
    print("-" * 30)
    
    base_url = "http://127.0.0.1:8000"
    
    try:
        response = requests.get(base_url, timeout=5)
        content = response.text
        
        seo_elements = {
            '<title>': 'عنوان الصفحة',
            'meta name="description"': 'وصف الصفحة',
            'meta name="keywords"': 'الكلمات المفتاحية',
            'meta property="og:': 'Open Graph',
            'meta name="twitter:': 'Twitter Cards',
            'application/ld+json': 'Structured Data'
        }
        
        found_seo = {}
        
        for element, description in seo_elements.items():
            if element in content:
                print(f"✅ {description}: موجود")
                found_seo[element] = True
            else:
                print(f"❌ {description}: مفقود")
                found_seo[element] = False
        
        return found_seo
        
    except Exception as e:
        print(f"❌ خطأ في فحص SEO: {e}")
        return {}

def test_performance():
    """اختبار الأداء"""
    print(f"\n⚡ اختبار الأداء")
    print("-" * 30)
    
    base_url = "http://127.0.0.1:8000"
    
    # اختبار متعدد للحصول على متوسط الوقت
    load_times = []
    
    for i in range(5):
        try:
            start_time = time.time()
            response = requests.get(base_url, timeout=10)
            load_time = time.time() - start_time
            
            if response.status_code == 200:
                load_times.append(load_time)
                print(f"✅ اختبار {i+1}: {load_time:.2f} ثانية")
            else:
                print(f"❌ اختبار {i+1}: فشل")
                
        except Exception as e:
            print(f"❌ اختبار {i+1}: خطأ")
    
    if load_times:
        avg_time = sum(load_times) / len(load_times)
        min_time = min(load_times)
        max_time = max(load_times)
        
        print(f"\n📊 نتائج الأداء:")
        print(f"   متوسط وقت التحميل: {avg_time:.2f} ثانية")
        print(f"   أسرع تحميل: {min_time:.2f} ثانية")
        print(f"   أبطأ تحميل: {max_time:.2f} ثانية")
        
        # تقييم الأداء
        if avg_time < 2:
            print(f"🚀 الأداء ممتاز!")
        elif avg_time < 4:
            print(f"✅ الأداء جيد")
        elif avg_time < 6:
            print(f"⚠️ الأداء مقبول")
        else:
            print(f"❌ الأداء يحتاج تحسين")
        
        return {
            'avg_time': avg_time,
            'min_time': min_time,
            'max_time': max_time,
            'tests_count': len(load_times)
        }
    
    return {'error': 'فشل في جميع الاختبارات'}

def test_database_integration():
    """اختبار تكامل قاعدة البيانات"""
    print(f"\n💾 اختبار تكامل قاعدة البيانات")
    print("-" * 40)
    
    try:
        from ads.models import Advertisement, Category
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # إحصائيات قاعدة البيانات
        stats = {
            'users': User.objects.count(),
            'categories': Category.objects.filter(is_active=True).count(),
            'total_ads': Advertisement.objects.count(),
            'approved_ads': Advertisement.objects.filter(status='approved').count(),
            'featured_ads': Advertisement.objects.filter(status='approved', is_featured=True).count(),
            'urgent_ads': Advertisement.objects.filter(status='approved', is_urgent=True).count(),
        }
        
        print(f"📊 إحصائيات قاعدة البيانات:")
        for key, value in stats.items():
            print(f"   {key}: {value}")
        
        # اختبار البيانات للصفحة الرئيسية
        from classified_ads_site.views import HomeView
        
        view = HomeView()
        context = view.get_context_data()
        
        print(f"\n📋 بيانات الصفحة الرئيسية:")
        print(f"   الأقسام: {len(context.get('categories', []))}")
        print(f"   الإعلانات المميزة: {len(context.get('featured_ads', []))}")
        print(f"   أحدث الإعلانات: {len(context.get('latest_ads', []))}")
        
        return {
            'database_stats': stats,
            'page_data': {
                'categories_count': len(context.get('categories', [])),
                'featured_ads_count': len(context.get('featured_ads', [])),
                'latest_ads_count': len(context.get('latest_ads', []))
            }
        }
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return {'error': str(e)}

def generate_landing_page_report():
    """إنشاء تقرير شامل لصفحة الهبوط"""
    print("🧪 اختبار شامل لصفحة الهبوط الجديدة")
    print("=" * 70)
    
    # تشغيل جميع الاختبارات
    page_test = test_landing_page_load()
    responsive_test = test_responsive_design()
    static_files_test = test_static_files()
    seo_test = test_seo_elements()
    performance_test = test_performance()
    database_test = test_database_integration()
    
    print(f"\n📊 التقرير النهائي لصفحة الهبوط")
    print("=" * 70)
    
    # تقييم النتائج
    total_score = 0
    max_score = 0
    
    # تقييم تحميل الصفحة
    if page_test.get('success'):
        page_score = (page_test.get('found_elements', 0) / page_test.get('total_elements', 1)) * 100
        print(f"📄 تحميل الصفحة: {page_score:.1f}% ({page_test.get('found_elements')}/{page_test.get('total_elements')} عناصر)")
        total_score += page_score
    else:
        print(f"📄 تحميل الصفحة: فشل")
    max_score += 100
    
    # تقييم التصميم المتجاوب
    responsive_score = (sum(responsive_test.values()) / len(responsive_test)) * 100
    print(f"📱 التصميم المتجاوب: {responsive_score:.1f}% ({sum(responsive_test.values())}/{len(responsive_test)} أجهزة)")
    total_score += responsive_score
    max_score += 100
    
    # تقييم SEO
    if seo_test:
        seo_score = (sum(seo_test.values()) / len(seo_test)) * 100
        print(f"🔍 تحسين محركات البحث: {seo_score:.1f}% ({sum(seo_test.values())}/{len(seo_test)} عناصر)")
        total_score += seo_score
    else:
        print(f"🔍 تحسين محركات البحث: فشل")
    max_score += 100
    
    # تقييم الأداء
    if 'avg_time' in performance_test:
        if performance_test['avg_time'] < 2:
            perf_score = 100
        elif performance_test['avg_time'] < 4:
            perf_score = 80
        elif performance_test['avg_time'] < 6:
            perf_score = 60
        else:
            perf_score = 40
        print(f"⚡ الأداء: {perf_score}% (متوسط: {performance_test['avg_time']:.2f}s)")
        total_score += perf_score
    else:
        print(f"⚡ الأداء: فشل")
    max_score += 100
    
    # تقييم قاعدة البيانات
    if 'database_stats' in database_test:
        db_score = 100  # إذا عملت فهي 100%
        print(f"💾 تكامل قاعدة البيانات: {db_score}%")
        total_score += db_score
    else:
        print(f"💾 تكامل قاعدة البيانات: فشل")
    max_score += 100
    
    # النتيجة الإجمالية
    overall_score = (total_score / max_score) * 100 if max_score > 0 else 0
    
    print(f"\n🏆 النتيجة الإجمالية: {overall_score:.1f}%")
    
    if overall_score >= 90:
        print(f"🎉 ممتاز! صفحة الهبوط تعمل بشكل مثالي")
        print(f"✨ جميع المتطلبات مطبقة بنجاح")
    elif overall_score >= 75:
        print(f"✅ جيد جداً! صفحة الهبوط تعمل بشكل جيد")
        print(f"🔧 بعض التحسينات الطفيفة مطلوبة")
    elif overall_score >= 60:
        print(f"⚠️ مقبول! صفحة الهبوط تحتاج بعض التحسينات")
    else:
        print(f"❌ يحتاج تحسين! صفحة الهبوط تحتاج إصلاحات جوهرية")
    
    print(f"\n🌐 رابط الصفحة: http://127.0.0.1:8000/")
    print("=" * 70)
    
    return overall_score

def main():
    """الدالة الرئيسية"""
    score = generate_landing_page_report()
    return score >= 75

if __name__ == '__main__':
    main()

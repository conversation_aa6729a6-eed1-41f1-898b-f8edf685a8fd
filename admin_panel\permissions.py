"""
نظام صلاحيات متقدم للوحة الإدارة
"""
from functools import wraps
from django.contrib.auth.decorators import user_passes_test
from django.contrib.admin.views.decorators import staff_member_required
from django.core.exceptions import PermissionDenied
from django.shortcuts import redirect
from django.contrib import messages
from django.http import JsonResponse
from .models import ActivityLog

def is_admin_user(user):
    """التحقق من أن المستخدم مدير"""
    return user.is_authenticated and (user.is_staff or user.is_superuser)

def is_superuser(user):
    """التحقق من أن المستخدم مدير عام"""
    return user.is_authenticated and user.is_superuser

def admin_required(function=None, redirect_url='/admin-panel/login/'):
    """
    Decorator للتحقق من صلاحيات المدير
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if not is_admin_user(request.user):
                if request.is_ajax():
                    return JsonResponse({'error': 'غير مصرح لك بالوصول'}, status=403)
                messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة')
                return redirect('accounts:login')
            
            # تسجيل النشاط
            ActivityLog.log_activity(
                user=request.user,
                action='view',
                description=f'وصول إلى {view_func.__name__}',
                request=request
            )
            
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    
    if function:
        return decorator(function)
    return decorator

def superuser_required(function=None):
    """
    Decorator للتحقق من صلاحيات المدير العام فقط
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if not is_superuser(request.user):
                if request.is_ajax():
                    return JsonResponse({'error': 'تحتاج صلاحيات مدير عام'}, status=403)
                messages.error(request, 'هذه العملية تتطلب صلاحيات مدير عام')
                return redirect('admin_panel:dashboard')
            
            # تسجيل النشاط الحساس
            ActivityLog.log_activity(
                user=request.user,
                action='admin_action',
                description=f'عملية مدير عام: {view_func.__name__}',
                request=request
            )
            
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    
    if function:
        return decorator(function)
    return decorator

def log_admin_action(action_type, description):
    """
    Decorator لتسجيل الأنشطة الإدارية
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            # تنفيذ العملية
            response = view_func(request, *args, **kwargs)
            
            # تسجيل النشاط بعد النجاح
            if hasattr(response, 'status_code') and response.status_code in [200, 302]:
                ActivityLog.log_activity(
                    user=request.user,
                    action=action_type,
                    description=description,
                    request=request
                )
            
            return response
        return _wrapped_view
    return decorator

class AdminPermissionMixin:
    """
    Mixin للتحقق من صلاحيات المدير في Class-based views
    """
    
    def dispatch(self, request, *args, **kwargs):
        if not is_admin_user(request.user):
            messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة')
            return redirect('accounts:login')
        
        # تسجيل النشاط
        ActivityLog.log_activity(
            user=request.user,
            action='view',
            description=f'وصول إلى {self.__class__.__name__}',
            request=request
        )
        
        return super().dispatch(request, *args, **kwargs)

class SuperuserPermissionMixin:
    """
    Mixin للتحقق من صلاحيات المدير العام في Class-based views
    """
    
    def dispatch(self, request, *args, **kwargs):
        if not is_superuser(request.user):
            messages.error(request, 'هذه العملية تتطلب صلاحيات مدير عام')
            return redirect('admin_panel:dashboard')
        
        # تسجيل النشاط الحساس
        ActivityLog.log_activity(
            user=request.user,
            action='admin_action',
            description=f'عملية مدير عام: {self.__class__.__name__}',
            request=request
        )
        
        return super().dispatch(request, *args, **kwargs)

def check_ad_permissions(user, ad):
    """
    التحقق من صلاحيات المستخدم على إعلان معين
    """
    if user.is_superuser:
        return True
    
    if user.is_staff:
        return True
    
    # المستخدم العادي يمكنه فقط تعديل إعلاناته
    return ad.user == user

def check_user_permissions(admin_user, target_user):
    """
    التحقق من صلاحيات المدير لتعديل مستخدم معين
    """
    if admin_user.is_superuser:
        return True
    
    # المدير العادي لا يمكنه تعديل المديرين الآخرين
    if target_user.is_staff or target_user.is_superuser:
        return False
    
    return True

def get_user_permissions(user):
    """
    الحصول على قائمة صلاحيات المستخدم
    """
    permissions = []
    
    if user.is_authenticated:
        permissions.append('authenticated')
    
    if user.is_staff:
        permissions.extend([
            'view_admin_panel',
            'manage_ads',
            'view_users',
            'view_reports',
            'view_statistics'
        ])
    
    if user.is_superuser:
        permissions.extend([
            'manage_users',
            'manage_system_settings',
            'manage_backups',
            'view_activity_logs',
            'manage_categories',
            'delete_any_content',
            'maintenance_mode'
        ])
    
    return permissions

def has_permission(user, permission):
    """
    التحقق من وجود صلاحية معينة للمستخدم
    """
    user_permissions = get_user_permissions(user)
    return permission in user_permissions

def require_permission(permission):
    """
    Decorator للتحقق من صلاحية معينة
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if not has_permission(request.user, permission):
                if request.is_ajax():
                    return JsonResponse({'error': f'تحتاج صلاحية: {permission}'}, status=403)
                messages.error(request, f'ليس لديك صلاحية: {permission}')
                return redirect('admin_panel:dashboard')
            
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator

class PermissionRequiredMixin:
    """
    Mixin للتحقق من صلاحية معينة في Class-based views
    """
    permission_required = None
    
    def dispatch(self, request, *args, **kwargs):
        if self.permission_required and not has_permission(request.user, self.permission_required):
            messages.error(request, f'ليس لديك صلاحية: {self.permission_required}')
            return redirect('admin_panel:dashboard')
        
        return super().dispatch(request, *args, **kwargs)

def track_sensitive_action(action_description):
    """
    Decorator لتتبع العمليات الحساسة
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            # تسجيل بداية العملية
            ActivityLog.log_activity(
                user=request.user,
                action='sensitive_action',
                description=f'بداية: {action_description}',
                request=request
            )
            
            try:
                response = view_func(request, *args, **kwargs)
                
                # تسجيل نجاح العملية
                ActivityLog.log_activity(
                    user=request.user,
                    action='sensitive_action',
                    description=f'نجح: {action_description}',
                    request=request
                )
                
                return response
                
            except Exception as e:
                # تسجيل فشل العملية
                ActivityLog.log_activity(
                    user=request.user,
                    action='sensitive_action',
                    description=f'فشل: {action_description} - {str(e)}',
                    request=request
                )
                raise
                
        return _wrapped_view
    return decorator

# Generated by Django 5.2.4 on 2025-07-04 18:41

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم القسم')),
                ('description', models.TextField(blank=True, verbose_name='وصف القسم')),
                ('icon', models.CharField(blank=True, max_length=50, verbose_name='أيقونة القسم')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'قسم',
                'verbose_name_plural': 'الأقسام',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Advertisement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الإعلان')),
                ('description', models.TextField(verbose_name='وصف الإعلان')),
                ('price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='السعر')),
                ('location', models.CharField(blank=True, max_length=200, verbose_name='الموقع')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('status', models.CharField(choices=[('pending', 'في انتظار المراجعة'), ('approved', 'موافق عليه'), ('rejected', 'مرفوض'), ('expired', 'منتهي الصلاحية')], default='pending', max_length=20, verbose_name='حالة الإعلان')),
                ('is_featured', models.BooleanField(default=False, verbose_name='إعلان مميز')),
                ('views_count', models.PositiveIntegerField(default=0, verbose_name='عدد المشاهدات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('expires_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ انتهاء الصلاحية')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='advertisements', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='advertisements', to='ads.category', verbose_name='القسم')),
            ],
            options={
                'verbose_name': 'إعلان',
                'verbose_name_plural': 'الإعلانات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AdImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='ads/', verbose_name='الصورة')),
                ('is_main', models.BooleanField(default=False, verbose_name='صورة رئيسية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')),
                ('advertisement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='ads.advertisement', verbose_name='الإعلان')),
            ],
            options={
                'verbose_name': 'صورة إعلان',
                'verbose_name_plural': 'صور الإعلانات',
                'ordering': ['-is_main', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='Report',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('report_type', models.CharField(choices=[('spam', 'محتوى مزعج'), ('inappropriate', 'محتوى غير مناسب'), ('fake', 'إعلان وهمي'), ('other', 'أخرى')], max_length=20, verbose_name='نوع التقرير')),
                ('description', models.TextField(blank=True, verbose_name='وصف التقرير')),
                ('is_resolved', models.BooleanField(default=False, verbose_name='تم الحل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التقرير')),
                ('advertisement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reports', to='ads.advertisement', verbose_name='الإعلان')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reports', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'تقرير',
                'verbose_name_plural': 'التقارير',
                'ordering': ['-created_at'],
                'unique_together': {('advertisement', 'user')},
            },
        ),
    ]

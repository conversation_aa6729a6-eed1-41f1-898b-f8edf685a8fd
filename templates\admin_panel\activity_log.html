{% extends 'admin_panel/base.html' %}
{% load static %}

{% block title %}سجل الأنشطة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-history text-info me-2"></i>سجل الأنشطة
                </h2>
                
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-filter me-1"></i>فلترة
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="?action=login">تسجيل دخول</a></li>
                        <li><a class="dropdown-item" href="?action=logout">تسجيل خروج</a></li>
                        <li><a class="dropdown-item" href="?action=create">إنشاء</a></li>
                        <li><a class="dropdown-item" href="?action=update">تحديث</a></li>
                        <li><a class="dropdown-item" href="?action=delete">حذف</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="?">جميع الأنشطة</a></li>
                    </ul>
                    
                    <button type="button" class="btn btn-outline-danger" onclick="clearOldLogs()">
                        <i class="fas fa-trash me-1"></i>مسح السجلات القديمة
                    </button>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-sign-in-alt fa-2x text-success mb-2"></i>
                            <h4>{{ login_count|default:0 }}</h4>
                            <small>تسجيل دخول اليوم</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-plus fa-2x text-primary mb-2"></i>
                            <h4>{{ create_count|default:0 }}</h4>
                            <small>عمليات إنشاء اليوم</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-edit fa-2x text-warning mb-2"></i>
                            <h4>{{ update_count|default:0 }}</h4>
                            <small>عمليات تحديث اليوم</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-trash fa-2x text-danger mb-2"></i>
                            <h4>{{ delete_count|default:0 }}</h4>
                            <small>عمليات حذف اليوم</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول الأنشطة -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>سجل الأنشطة الأخيرة
                    </h5>
                </div>
                <div class="card-body">
                    {% if activities %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الوقت</th>
                                        <th>المستخدم</th>
                                        <th>النشاط</th>
                                        <th>التفاصيل</th>
                                        <th>عنوان IP</th>
                                        <th>المتصفح</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for activity in activities %}
                                    <tr>
                                        <td>
                                            <small class="text-muted">{{ activity.timestamp|date:"Y-m-d H:i:s" }}</small>
                                        </td>
                                        <td>
                                            {% if activity.user %}
                                                <div class="d-flex align-items-center">
                                                    {% if activity.user.profile_picture %}
                                                        <img src="{{ activity.user.profile_picture.url }}" class="rounded-circle me-2" width="30" height="30">
                                                    {% else %}
                                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                                                            <i class="fas fa-user text-white small"></i>
                                                        </div>
                                                    {% endif %}
                                                    <div>
                                                        <div class="fw-bold">{{ activity.user.get_full_name|default:activity.user.username }}</div>
                                                        <small class="text-muted">{{ activity.user.email }}</small>
                                                    </div>
                                                </div>
                                            {% else %}
                                                <span class="text-muted">مجهول</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if activity.action == 'login' %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-sign-in-alt me-1"></i>تسجيل دخول
                                                </span>
                                            {% elif activity.action == 'logout' %}
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-sign-out-alt me-1"></i>تسجيل خروج
                                                </span>
                                            {% elif activity.action == 'create' %}
                                                <span class="badge bg-primary">
                                                    <i class="fas fa-plus me-1"></i>إنشاء
                                                </span>
                                            {% elif activity.action == 'update' %}
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-edit me-1"></i>تحديث
                                                </span>
                                            {% elif activity.action == 'delete' %}
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-trash me-1"></i>حذف
                                                </span>
                                            {% elif activity.action == 'approve' %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>موافقة
                                                </span>
                                            {% elif activity.action == 'reject' %}
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times me-1"></i>رفض
                                                </span>
                                            {% else %}
                                                <span class="badge bg-info">{{ activity.action }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div>{{ activity.description }}</div>
                                            {% if activity.object_type %}
                                                <small class="text-muted">{{ activity.object_type }}: {{ activity.object_id }}</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <code class="small">{{ activity.ip_address|default:"غير محدد" }}</code>
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ activity.user_agent|truncatechars:30|default:"غير محدد" }}</small>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        {% if is_paginated %}
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1">الأولى</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                                    </li>
                                {% endif %}
                                
                                <li class="page-item active">
                                    <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                                </li>
                                
                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                        
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-history fa-5x text-muted mb-4"></i>
                            <h3 class="text-muted mb-3">لا توجد أنشطة مسجلة</h3>
                            <p class="text-muted">
                                لم يتم تسجيل أي أنشطة بعد. ستظهر هنا جميع أنشطة المستخدمين والمديرين.
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- تحليل الأنشطة -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-pie me-2"></i>توزيع الأنشطة
                            </h6>
                        </div>
                        <div class="card-body">
                            <canvas id="activityChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-users me-2"></i>أكثر المستخدمين نشاطاً
                            </h6>
                        </div>
                        <div class="card-body">
                            {% if top_users %}
                                <div class="list-group list-group-flush">
                                    {% for user_activity in top_users %}
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            {% if user_activity.user.profile_picture %}
                                                <img src="{{ user_activity.user.profile_picture.url }}" class="rounded-circle me-2" width="40" height="40">
                                            {% else %}
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                            {% endif %}
                                            <div>
                                                <div class="fw-bold">{{ user_activity.user.get_full_name|default:user_activity.user.username }}</div>
                                                <small class="text-muted">{{ user_activity.user.email }}</small>
                                            </div>
                                        </div>
                                        <span class="badge bg-primary rounded-pill">{{ user_activity.activity_count }}</span>
                                    </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <p class="text-muted text-center">لا توجد بيانات</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// رسم بياني لتوزيع الأنشطة
const ctx = document.getElementById('activityChart').getContext('2d');
const activityChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['تسجيل دخول', 'إنشاء', 'تحديث', 'حذف', 'أخرى'],
        datasets: [{
            data: [
                {{ login_count|default:0 }},
                {{ create_count|default:0 }},
                {{ update_count|default:0 }},
                {{ delete_count|default:0 }},
                {{ other_count|default:0 }}
            ],
            backgroundColor: [
                '#28a745',
                '#007bff',
                '#ffc107',
                '#dc3545',
                '#6c757d'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

function clearOldLogs() {
    if (confirm('هل تريد حذف السجلات الأقدم من 30 يوماً؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        fetch('/admin-panel/clear-old-logs/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`تم حذف ${data.deleted_count} سجل قديم بنجاح!`);
                location.reload();
            } else {
                alert('حدث خطأ في حذف السجلات القديمة');
            }
        });
    }
}

// تحديث تلقائي كل 30 ثانية
setInterval(function() {
    location.reload();
}, 30000);
</script>
{% endblock %}

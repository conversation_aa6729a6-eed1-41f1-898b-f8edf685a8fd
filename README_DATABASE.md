# 🗄️ قاعدة بيانات موقع الإعلانات المبوبة

## 📋 نظرة عامة

تحتوي هذه الحزمة على قاعدة بيانات MySQL كاملة لموقع الإعلانات المبوبة مع جميع الجداول والبيانات التجريبية.

## 📁 الملفات المتضمنة

| الملف | الوصف |
|-------|--------|
| `classified_ads_database.sql` | ملف قاعدة البيانات الكاملة |
| `import_database.bat` | أداة استيراد تلقائية (Windows) |
| `update_passwords.py` | تحديث كلمات المرور بعد الاستيراد |
| `DATABASE_IMPORT_GUIDE.md` | دليل الاستيراد التفصيلي |
| `README_DATABASE.md` | هذا الملف |

## 🚀 الاستيراد السريع

### الطريقة الأسهل (Windows):
1. تأكد من تشغيل XAMPP
2. ضع جميع الملفات في مجلد المشروع
3. انقر نقراً مزدوجاً على `import_database.bat`
4. اتبع التعليمات على الشاشة

### الطريقة اليدوية:
```bash
# في phpMyAdmin أو MySQL Command Line
mysql -u root -p < classified_ads_database.sql
```

## 📊 محتويات قاعدة البيانات

### 🏗️ الهيكل العام
- **اسم قاعدة البيانات:** `classified_ads_db`
- **الترميز:** `utf8mb4_unicode_ci`
- **عدد الجداول:** 13 جدول
- **حجم البيانات:** ~50 سجل

### 📋 الجداول الرئيسية

#### 1. جدول المستخدمين (`accounts_customuser`)
```sql
- id (Primary Key)
- username (Unique)
- email
- first_name, last_name
- phone, address
- profile_image
- is_verified, is_staff, is_superuser
- created_at, updated_at
```

#### 2. جدول الأقسام (`ads_category`)
```sql
- id (Primary Key)
- name (Unique)
- description
- icon
- is_active
- created_at, updated_at
```

#### 3. جدول الإعلانات (`ads_advertisement`)
```sql
- id (Primary Key)
- title
- description
- price
- location
- phone, email
- status (pending, approved, rejected, expired)
- is_featured
- views_count
- category_id (Foreign Key)
- user_id (Foreign Key)
- created_at, updated_at, expires_at
```

#### 4. جدول صور الإعلانات (`ads_adimage`)
```sql
- id (Primary Key)
- image
- is_main
- advertisement_id (Foreign Key)
- created_at
```

#### 5. جدول التقارير (`ads_report`)
```sql
- id (Primary Key)
- report_type
- description
- is_resolved
- advertisement_id (Foreign Key)
- user_id (Foreign Key)
- created_at
```

### ⚙️ جداول النظام
- `django_content_type` - أنواع المحتوى
- `auth_permission` - الصلاحيات
- `auth_group` - المجموعات
- `django_session` - الجلسات
- `django_admin_log` - سجل الإدارة
- `django_migrations` - الهجرات
- جداول ربط الصلاحيات والمجموعات

## 👥 البيانات المدرجة

### 🔑 المستخدمون (4 مستخدمين)

| المستخدم | النوع | البريد الإلكتروني | كلمة المرور |
|----------|-------|------------------|-------------|
| admin | مدير | <EMAIL> | admin123 |
| user1 | عادي | <EMAIL> | password123 |
| user2 | عادي | <EMAIL> | password123 |
| user3 | عادي | <EMAIL> | password123 |

### 📂 الأقسام (8 أقسام)

1. 🏠 **عقارات** - شقق، فيلات، أراضي
2. 🚗 **سيارات** - سيارات جديدة ومستعملة
3. 💼 **وظائف** - فرص عمل
4. 🎓 **دورات تدريبية** - دورات ودروس
5. 💻 **إلكترونيات** - أجهزة تقنية
6. 🛋️ **أثاث ومنزل** - أثاث وأدوات منزلية
7. 🔧 **خدمات** - خدمات متنوعة
8. 👕 **أزياء وموضة** - ملابس وإكسسوارات

### 📢 الإعلانات (5 إعلانات)

1. **شقة للبيع في الرياض** ⭐ - 450,000 ريال
2. **سيارة تويوتا كامري 2020** ⭐ - 85,000 ريال
3. **مطلوب مطور ويب** - وظيفة
4. **لابتوب ديل للبيع** - 2,500 ريال
5. **فيلا للإيجار في جدة** ⭐ - 8,000 ريال/شهر

## 🔧 إعداد Django

### 1. تحديث settings.py
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'classified_ads_db',
        'USER': 'root',
        'PASSWORD': '',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'charset': 'utf8mb4',
        },
    }
}
```

### 2. تحديث كلمات المرور
```bash
python update_passwords.py
```

### 3. تشغيل الموقع
```bash
python manage.py runserver
```

## 🔍 الفهارس المحسنة

تم إنشاء فهارس محسنة للأداء:

### فهارس الإعلانات
- `idx_ads_status` - حالة الإعلان
- `idx_ads_category` - القسم
- `idx_ads_user` - المستخدم
- `idx_ads_created` - تاريخ الإنشاء
- `idx_ads_featured` - الإعلانات المميزة
- `idx_ads_price` - السعر
- `idx_ads_location` - الموقع

### فهارس مركبة
- `idx_ads_status_category` - حالة + قسم
- `idx_ads_status_featured` - حالة + مميز
- `idx_ads_status_created` - حالة + تاريخ

### فهارس أخرى
- فهارس المستخدمين والأقسام
- فهارس الجلسات والصلاحيات

## ✅ التحقق من نجاح الاستيراد

### 1. فحص الجداول
```sql
USE classified_ads_db;
SHOW TABLES;
-- يجب أن يظهر 13 جدول
```

### 2. فحص البيانات
```sql
SELECT COUNT(*) FROM accounts_customuser;  -- 4
SELECT COUNT(*) FROM ads_category;         -- 8
SELECT COUNT(*) FROM ads_advertisement;    -- 5
```

### 3. اختبار تسجيل الدخول
- الذهاب إلى: `http://127.0.0.1:8000/admin-panel/`
- تسجيل الدخول: admin / admin123

## 🌐 الروابط

- **الموقع الرئيسي:** http://127.0.0.1:8000/
- **لوحة الإدارة:** http://127.0.0.1:8000/admin-panel/
- **إدارة Django:** http://127.0.0.1:8000/admin/
- **phpMyAdmin:** http://localhost/phpmyadmin

## 🚨 ملاحظات أمنية

1. **غير كلمات المرور** في الإنتاج
2. **أنشئ مستخدم قاعدة بيانات منفصل** للإنتاج
3. **فعل SSL** للاتصالات الآمنة
4. **راجع الصلاحيات** بانتظام

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

#### خطأ الاتصال بقاعدة البيانات
```
Solution: تأكد من تشغيل MySQL في XAMPP
```

#### خطأ الترميز
```
Solution: تأكد من استخدام utf8mb4_unicode_ci
```

#### كلمات المرور لا تعمل
```
Solution: شغل update_passwords.py
```

## 📞 الدعم

للحصول على المساعدة:
1. راجع `DATABASE_IMPORT_GUIDE.md`
2. تحقق من ملفات السجل
3. تأكد من إعدادات XAMPP
4. راجع إعدادات Django

---

**تم إنشاء قاعدة البيانات بواسطة:** نظام إدارة الإعلانات المبوبة  
**الإصدار:** 1.0  
**التاريخ:** 2024

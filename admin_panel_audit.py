#!/usr/bin/env python
"""
فحص شامل للوحة الإدارة - تحديد المشاكل والنواقص
"""
import os
import django
import requests
from urllib.parse import urljoin

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

def test_admin_urls():
    """اختبار جميع روابط لوحة الإدارة"""
    print("🔍 فحص روابط لوحة الإدارة")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:8000"
    admin_urls = [
        "/admin-panel/",
        "/admin-panel/users/",
        "/admin-panel/users/1/",
        "/admin-panel/ads/",
        "/admin-panel/ads/1/",
        "/admin-panel/pending-ads/",
        "/admin-panel/categories/",
        "/admin-panel/categories/create/",
        "/admin-panel/reports/",
        "/admin-panel/statistics/",
    ]
    
    working_urls = []
    broken_urls = []
    
    for url_path in admin_urls:
        full_url = urljoin(base_url, url_path)
        try:
            response = requests.get(full_url, timeout=5)
            if response.status_code == 200:
                working_urls.append(url_path)
                print(f"✅ {url_path}")
            elif response.status_code == 404:
                broken_urls.append((url_path, "404 - صفحة غير موجودة"))
                print(f"❌ {url_path} - 404")
            elif response.status_code == 500:
                broken_urls.append((url_path, "500 - خطأ في الخادم"))
                print(f"❌ {url_path} - 500")
            else:
                broken_urls.append((url_path, f"{response.status_code}"))
                print(f"⚠️ {url_path} - {response.status_code}")
        except requests.exceptions.RequestException as e:
            broken_urls.append((url_path, f"خطأ في الاتصال: {str(e)}"))
            print(f"❌ {url_path} - خطأ في الاتصال")
    
    return working_urls, broken_urls

def check_templates():
    """فحص القوالب المطلوبة"""
    print(f"\n📄 فحص القوالب")
    print("-" * 30)
    
    required_templates = [
        "admin_panel/base.html",
        "admin_panel/dashboard.html",
        "admin_panel/user_list.html",
        "admin_panel/user_detail.html",
        "admin_panel/ad_list.html",
        "admin_panel/ad_detail.html",
        "admin_panel/pending_ads.html",
        "admin_panel/category_list.html",
        "admin_panel/category_form.html",
        "admin_panel/statistics.html",
        # قوالب مفقودة محتملة
        "admin_panel/report_list.html",
        "admin_panel/report_detail.html",
        "admin_panel/user_edit.html",
        "admin_panel/ad_edit.html",
        "admin_panel/settings.html",
        "admin_panel/backup.html",
        "admin_panel/activity_log.html",
    ]
    
    existing_templates = []
    missing_templates = []
    
    for template in required_templates:
        template_path = os.path.join("templates", template)
        if os.path.exists(template_path):
            existing_templates.append(template)
            print(f"✅ {template}")
        else:
            missing_templates.append(template)
            print(f"❌ {template} - مفقود")
    
    return existing_templates, missing_templates

def check_views():
    """فحص الـ views المطلوبة"""
    print(f"\n🎯 فحص Views")
    print("-" * 30)
    
    from admin_panel import views
    
    required_views = [
        'DashboardView',
        'UserListView',
        'UserDetailView',
        'AdminAdListView',
        'AdminAdDetailView',
        'PendingAdsView',
        'CategoryListView',
        'CategoryCreateView',
        'StatisticsView',
        # views مفقودة محتملة
        'UserEditView',
        'UserDeleteView',
        'AdEditView',
        'AdDeleteView',
        'ReportListView',
        'ReportDetailView',
        'SettingsView',
        'BackupView',
        'ActivityLogView',
    ]
    
    existing_views = []
    missing_views = []
    
    for view_name in required_views:
        if hasattr(views, view_name):
            existing_views.append(view_name)
            print(f"✅ {view_name}")
        else:
            missing_views.append(view_name)
            print(f"❌ {view_name} - مفقود")
    
    return existing_views, missing_views

def check_database_data():
    """فحص البيانات في قاعدة البيانات"""
    print(f"\n💾 فحص البيانات")
    print("-" * 30)
    
    try:
        from ads.models import Advertisement, Category, Report, Notification
        from accounts.models import CustomUser
        
        data_stats = {
            'users': CustomUser.objects.count(),
            'ads': Advertisement.objects.count(),
            'categories': Category.objects.count(),
            'reports': Report.objects.count(),
            'notifications': Notification.objects.count(),
            'pending_ads': Advertisement.objects.filter(status='pending').count(),
            'approved_ads': Advertisement.objects.filter(status='approved').count(),
        }
        
        print(f"👥 المستخدمون: {data_stats['users']}")
        print(f"📢 الإعلانات: {data_stats['ads']}")
        print(f"📂 الأقسام: {data_stats['categories']}")
        print(f"🚩 التقارير: {data_stats['reports']}")
        print(f"🔔 الإشعارات: {data_stats['notifications']}")
        print(f"⏳ الإعلانات المعلقة: {data_stats['pending_ads']}")
        print(f"✅ الإعلانات المعتمدة: {data_stats['approved_ads']}")
        
        return data_stats
        
    except Exception as e:
        print(f"❌ خطأ في فحص البيانات: {e}")
        return {}

def identify_missing_features():
    """تحديد الميزات المفقودة"""
    print(f"\n🚀 الميزات المفقودة المطلوبة")
    print("-" * 30)
    
    missing_features = [
        "📊 رسوم بيانية تفاعلية (Charts)",
        "🔔 نظام إشعارات فوري للمديرين",
        "📈 تقارير متقدمة وتحليلات ذكية",
        "⚙️ صفحة إعدادات النظام",
        "💾 نظام النسخ الاحتياطي",
        "🔒 سجل الأنشطة ومراقبة الأمان",
        "✏️ تحرير مجمع للإعلانات",
        "📤 استيراد/تصدير البيانات",
        "🔍 بحث متقدم وفلترة ذكية",
        "👤 إدارة الصلاحيات والأدوار",
        "🎨 إعدادات شخصية للمديرين",
        "📱 تطبيق جوال للإدارة",
        "🔄 تحديثات تلقائية للبيانات",
        "📧 نظام إرسال رسائل جماعية",
        "🏷️ نظام العلامات والتصنيفات",
    ]
    
    for feature in missing_features:
        print(f"❌ {feature}")
    
    return missing_features

def generate_development_plan():
    """إنشاء خطة التطوير"""
    print(f"\n📋 خطة التطوير المقترحة")
    print("=" * 50)
    
    development_phases = [
        {
            "phase": "المرحلة الأولى - الإصلاحات الأساسية",
            "tasks": [
                "إصلاح القوالب المفقودة",
                "إصلاح الروابط المكسورة",
                "إضافة views المفقودة",
                "تحسين التصميم الأساسي"
            ]
        },
        {
            "phase": "المرحلة الثانية - الميزات الأساسية",
            "tasks": [
                "صفحات تعديل وحذف المستخدمين",
                "صفحات تعديل وحذف الإعلانات",
                "نظام إدارة التقارير",
                "صفحة إعدادات النظام"
            ]
        },
        {
            "phase": "المرحلة الثالثة - الميزات المتقدمة",
            "tasks": [
                "رسوم بيانية تفاعلية",
                "نظام إشعارات فوري",
                "تقارير متقدمة",
                "سجل الأنشطة"
            ]
        },
        {
            "phase": "المرحلة الرابعة - التحسينات النهائية",
            "tasks": [
                "تحسين تجربة المستخدم",
                "إضافة اختصارات لوحة المفاتيح",
                "تحسين الأداء",
                "اختبارات شاملة"
            ]
        }
    ]
    
    for i, phase in enumerate(development_phases, 1):
        print(f"\n{i}. {phase['phase']}")
        for task in phase['tasks']:
            print(f"   - {task}")
    
    return development_phases

def main():
    """الدالة الرئيسية"""
    print("🔍 فحص شامل للوحة الإدارة")
    print("=" * 60)
    
    # فحص الروابط
    working_urls, broken_urls = test_admin_urls()
    
    # فحص القوالب
    existing_templates, missing_templates = check_templates()
    
    # فحص الـ views
    existing_views, missing_views = check_views()
    
    # فحص البيانات
    data_stats = check_database_data()
    
    # تحديد الميزات المفقودة
    missing_features = identify_missing_features()
    
    # إنشاء خطة التطوير
    development_plan = generate_development_plan()
    
    # تقرير نهائي
    print(f"\n" + "=" * 60)
    print("📊 ملخص الفحص")
    print("=" * 60)
    print(f"✅ الروابط العاملة: {len(working_urls)}")
    print(f"❌ الروابط المكسورة: {len(broken_urls)}")
    print(f"✅ القوالب الموجودة: {len(existing_templates)}")
    print(f"❌ القوالب المفقودة: {len(missing_templates)}")
    print(f"✅ الـ Views الموجودة: {len(existing_views)}")
    print(f"❌ الـ Views المفقودة: {len(missing_views)}")
    print(f"🚀 الميزات المطلوبة: {len(missing_features)}")
    
    if broken_urls or missing_templates or missing_views:
        print(f"\n⚠️ يحتاج النظام إلى تطوير وإصلاحات")
    else:
        print(f"\n✅ النظام في حالة جيدة")
    
    print("=" * 60)
    
    return {
        'working_urls': working_urls,
        'broken_urls': broken_urls,
        'existing_templates': existing_templates,
        'missing_templates': missing_templates,
        'existing_views': existing_views,
        'missing_views': missing_views,
        'data_stats': data_stats,
        'missing_features': missing_features,
        'development_plan': development_plan
    }

if __name__ == '__main__':
    main()

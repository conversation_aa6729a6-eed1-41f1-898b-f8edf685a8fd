#!/usr/bin/env python
"""
Script to add sample advertisements (optional)
This is only for demonstration purposes
"""
import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

def create_sample_user():
    """إنشاء مستخدم تجريبي لنشر الإعلانات"""
    try:
        from accounts.models import CustomUser
        
        # التحقق من وجود مستخدم تجريبي
        if CustomUser.objects.filter(username='demo_user').exists():
            user = CustomUser.objects.get(username='demo_user')
            print(f"✅ المستخدم التجريبي موجود: {user.username}")
        else:
            # إنشاء مستخدم تجريبي
            user = CustomUser.objects.create_user(
                username='demo_user',
                email='<EMAIL>',
                password='demo123',
                first_name='مستخدم',
                last_name='تجريبي',
                phone='**********',
                is_verified=True
            )
            print(f"✅ تم إنشاء المستخدم التجريبي: {user.username}")
        
        return user
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدم: {e}")
        return None

def add_sample_ads():
    """إضافة إعلانات تجريبية"""
    print("📢 إضافة إعلانات تجريبية...")
    print("=" * 50)
    
    # تأكيد من المستخدم
    print("⚠️ تحذير: سيتم إضافة إعلانات تجريبية للعرض فقط")
    print("هذه الإعلانات ليست حقيقية وهي لأغراض التوضيح")
    
    response = input("\nهل تريد إضافة إعلانات تجريبية؟ (y/N): ")
    
    if response.lower() != 'y':
        print("❌ تم إلغاء العملية")
        return False
    
    try:
        from ads.models import Advertisement, Category
        
        # إنشاء مستخدم تجريبي
        user = create_sample_user()
        if not user:
            return False
        
        # الحصول على الأقسام
        categories = Category.objects.all()
        if not categories.exists():
            print("❌ لا توجد أقسام متاحة")
            return False
        
        # إعلانات تجريبية
        sample_ads = [
            {
                'title': 'شقة للبيع في الرياض - حي النرجس',
                'description': 'شقة مميزة للبيع في حي النرجس، 3 غرف نوم، صالة، مطبخ، 2 حمام. الشقة في الدور الثاني، مساحة 120 متر مربع. قريبة من الخدمات والمدارس.',
                'category': 'عقارات',
                'price': 450000,
                'location': 'الرياض - حي النرجس',
                'phone': '**********',
                'is_featured': True
            },
            {
                'title': 'سيارة تويوتا كامري 2020 للبيع',
                'description': 'سيارة تويوتا كامري موديل 2020، لون أبيض، ماشية 45000 كم، حالة ممتازة، سيرفس منتظم، بدون حوادث.',
                'category': 'سيارات',
                'price': 85000,
                'location': 'جدة',
                'phone': '0507654321',
                'is_featured': True
            },
            {
                'title': 'مطلوب مطور ويب - دوام كامل',
                'description': 'مطلوب مطور ويب خبرة لا تقل عن سنتين، إتقان HTML, CSS, JavaScript, Python. راتب مجزي + مزايا.',
                'category': 'وظائف',
                'price': None,
                'location': 'الرياض',
                'phone': '0551234567',
                'is_featured': False
            },
            {
                'title': 'لابتوب ديل للبيع - حالة ممتازة',
                'description': 'لابتوب ديل Inspiron 15، معالج Intel i5، ذاكرة 8GB، قرص صلب 512GB SSD، كارت شاشة منفصل.',
                'category': 'إلكترونيات',
                'price': 2500,
                'location': 'الدمام',
                'phone': '0561234567',
                'is_featured': False
            },
            {
                'title': 'طقم صالة مودرن للبيع',
                'description': 'طقم صالة مودرن مكون من كنبة 3 مقاعد + 2 كرسي + طاولة وسط، لون بيج، حالة ممتازة.',
                'category': 'أثاث ومنزل',
                'price': 3500,
                'location': 'الرياض',
                'phone': '0571234567',
                'is_featured': False
            }
        ]
        
        created_count = 0
        
        for ad_data in sample_ads:
            # البحث عن القسم
            try:
                category = categories.get(name=ad_data['category'])
            except Category.DoesNotExist:
                category = categories.first()  # استخدام أول قسم متاح
            
            # إنشاء الإعلان
            ad = Advertisement.objects.create(
                title=ad_data['title'],
                description=ad_data['description'],
                category=category,
                user=user,
                price=ad_data['price'],
                location=ad_data['location'],
                phone=ad_data['phone'],
                is_featured=ad_data['is_featured'],
                status='approved'  # معتمد مباشرة
            )
            
            created_count += 1
            print(f"✅ تم إنشاء الإعلان: {ad.title}")
        
        print(f"\n✅ تم إنشاء {created_count} إعلان تجريبي")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة الإعلانات: {e}")
        return False

def show_current_status():
    """عرض حالة الموقع الحالية"""
    print("\n📊 حالة الموقع الحالية:")
    print("-" * 30)
    
    try:
        from ads.models import Advertisement, Category
        from accounts.models import CustomUser
        
        users_count = CustomUser.objects.count()
        categories_count = Category.objects.count()
        ads_count = Advertisement.objects.count()
        featured_ads_count = Advertisement.objects.filter(is_featured=True).count()
        
        print(f"👥 المستخدمون: {users_count}")
        print(f"📂 الأقسام: {categories_count}")
        print(f"📢 الإعلانات: {ads_count}")
        print(f"⭐ الإعلانات المميزة: {featured_ads_count}")
        
        if ads_count > 0:
            print(f"\n📋 الإعلانات الموجودة:")
            for ad in Advertisement.objects.all()[:5]:
                status_icon = "⭐" if ad.is_featured else "📢"
                print(f"   {status_icon} {ad.title} - {ad.category.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في عرض الحالة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("📢 إدارة الإعلانات التجريبية")
    print("=" * 50)
    
    # عرض الحالة الحالية
    show_current_status()
    
    # إضافة إعلانات تجريبية
    if add_sample_ads():
        print("\n" + "=" * 50)
        print("🎉 تم إضافة الإعلانات التجريبية بنجاح!")
        print("=" * 50)
        
        # عرض الحالة الجديدة
        show_current_status()
        
        print("\n🌐 يمكنك الآن:")
        print("- زيارة الموقع: http://127.0.0.1:8000/")
        print("- تصفح الإعلانات: http://127.0.0.1:8000/ads/")
        print("- إدارة الإعلانات من لوحة الإدارة")
        
        print("\n📝 ملاحظة:")
        print("هذه إعلانات تجريبية لأغراض العرض فقط")
        print("يمكنك حذفها لاحقاً من لوحة الإدارة")
    
    return True

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ تم إلغاء العملية بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()

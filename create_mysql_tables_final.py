#!/usr/bin/env python
"""
Create all tables in MySQL database
"""
import os
import sys
import subprocess
import mysql.connector
from mysql.connector import Error

def create_mysql_tables():
    """إنشاء جداول MySQL"""
    print("🏗️ إنشاء جداول MySQL")
    print("=" * 40)
    
    # تعيين متغير البيئة لاستخدام MySQL
    os.environ['USE_MYSQL'] = 'true'
    
    try:
        # تطبيق الهجرات
        print("📦 تطبيق الهجرات على MySQL...")
        result = subprocess.run([
            'python', 'manage.py', 'migrate'
        ], capture_output=True, text=True, env=os.environ)
        
        if result.returncode == 0:
            print("✅ تم إنشاء جميع الجداول في MySQL!")
            print(result.stdout)
            return True
        else:
            print(f"❌ فشل في تطبيق الهجرات:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False

def verify_mysql_tables():
    """التحقق من الجداول في MySQL"""
    print("\n🔍 التحقق من الجداول...")
    
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='classified_ads_db',
            port=3306
        )
        
        cursor = connection.cursor()
        
        # عرض الجداول
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        print(f"📁 عدد الجداول: {len(tables)}")
        
        if tables:
            print("\n📋 الجداول الموجودة:")
            for table in tables:
                table_name = table[0]
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                count = cursor.fetchone()[0]
                
                # تحديد أيقونة الجدول
                if table_name.startswith('accounts_'):
                    icon = "👥"
                elif table_name.startswith('ads_'):
                    icon = "📢"
                elif table_name.startswith('auth_'):
                    icon = "🔐"
                elif table_name.startswith('django_'):
                    icon = "⚙️"
                else:
                    icon = "📊"
                
                print(f"   {icon} {table_name}: {count} سجل")
        
        return True
        
    except Error as e:
        print(f"❌ خطأ في التحقق: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def create_admin_user():
    """إنشاء مستخدم إداري"""
    print("\n👤 إنشاء مستخدم إداري...")
    
    try:
        # تعيين متغير البيئة
        os.environ['USE_MYSQL'] = 'true'
        
        # Setup Django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
        import django
        django.setup()
        
        from accounts.models import CustomUser
        
        # إنشاء المستخدم الإداري
        admin_user, created = CustomUser.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'مدير',
                'last_name': 'النظام',
                'is_staff': True,
                'is_superuser': True,
                'is_verified': True
            }
        )
        
        if created:
            admin_user.set_password('admin123')
            admin_user.save()
            print("✅ تم إنشاء المستخدم الإداري: admin / admin123")
        else:
            print("✅ المستخدم الإداري موجود بالفعل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدم الإداري: {e}")
        return False

def create_sample_data():
    """إنشاء البيانات التجريبية"""
    print("\n📊 إنشاء البيانات التجريبية...")
    
    try:
        # تعيين متغير البيئة
        os.environ['USE_MYSQL'] = 'true'
        
        # Setup Django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
        import django
        django.setup()
        
        from ads.models import Category, Advertisement
        from accounts.models import CustomUser
        
        # إنشاء الأقسام
        categories_data = [
            {'name': 'عقارات', 'description': 'شقق، فيلات، أراضي للبيع والإيجار', 'icon': 'fas fa-home'},
            {'name': 'سيارات', 'description': 'سيارات جديدة ومستعملة للبيع', 'icon': 'fas fa-car'},
            {'name': 'وظائف', 'description': 'فرص عمل في جميع المجالات', 'icon': 'fas fa-briefcase'},
            {'name': 'دورات تدريبية', 'description': 'دورات ودروس في مختلف المجالات', 'icon': 'fas fa-graduation-cap'},
            {'name': 'إلكترونيات', 'description': 'أجهزة إلكترونية ومعدات تقنية', 'icon': 'fas fa-laptop'},
            {'name': 'أثاث ومنزل', 'description': 'أثاث وأدوات منزلية', 'icon': 'fas fa-couch'},
            {'name': 'خدمات', 'description': 'خدمات متنوعة', 'icon': 'fas fa-tools'},
            {'name': 'أزياء وموضة', 'description': 'ملابس وإكسسوارات', 'icon': 'fas fa-tshirt'},
        ]
        
        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={
                    'description': cat_data['description'],
                    'icon': cat_data['icon'],
                    'is_active': True
                }
            )
            if created:
                print(f"✅ تم إنشاء القسم: {category.name}")
        
        # إنشاء مستخدمين تجريبيين
        users_data = [
            {'username': 'user1', 'email': '<EMAIL>', 'first_name': 'أحمد', 'last_name': 'محمد'},
            {'username': 'user2', 'email': '<EMAIL>', 'first_name': 'فاطمة', 'last_name': 'علي'},
            {'username': 'user3', 'email': '<EMAIL>', 'first_name': 'خالد', 'last_name': 'السعد'},
        ]
        
        for user_data in users_data:
            user, created = CustomUser.objects.get_or_create(
                username=user_data['username'],
                defaults={
                    'email': user_data['email'],
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name'],
                    'is_verified': True
                }
            )
            if created:
                user.set_password('password123')
                user.save()
                print(f"✅ تم إنشاء المستخدم: {user.username}")
        
        # إنشاء إعلانات تجريبية
        if Category.objects.exists() and CustomUser.objects.filter(username='user1').exists():
            real_estate = Category.objects.get(name='عقارات')
            cars = Category.objects.get(name='سيارات')
            user1 = CustomUser.objects.get(username='user1')
            user2 = CustomUser.objects.get(username='user2')
            
            ads_data = [
                {
                    'title': 'شقة للبيع في الرياض',
                    'description': 'شقة مميزة للبيع في حي الملز، 3 غرف نوم، 2 حمام، صالة واسعة، مطبخ مجهز.',
                    'category': real_estate,
                    'user': user1,
                    'price': 450000,
                    'location': 'الرياض - حي الملز',
                    'phone': '0501234567',
                    'status': 'approved',
                    'is_featured': True
                },
                {
                    'title': 'سيارة تويوتا كامري 2020',
                    'description': 'سيارة تويوتا كامري موديل 2020، حالة ممتازة، قطعت 45000 كم فقط.',
                    'category': cars,
                    'user': user2,
                    'price': 85000,
                    'location': 'جدة',
                    'phone': '0507654321',
                    'status': 'approved',
                    'is_featured': True
                }
            ]
            
            for ad_data in ads_data:
                ad, created = Advertisement.objects.get_or_create(
                    title=ad_data['title'],
                    defaults=ad_data
                )
                if created:
                    print(f"✅ تم إنشاء الإعلان: {ad.title}")
        
        print("✅ تم إنشاء جميع البيانات التجريبية!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 إعداد جداول MySQL الكامل")
    print("=" * 50)
    
    # 1. إنشاء الجداول
    if not create_mysql_tables():
        print("❌ فشل في إنشاء الجداول")
        return False
    
    # 2. التحقق من الجداول
    if not verify_mysql_tables():
        print("❌ فشل في التحقق من الجداول")
        return False
    
    # 3. إنشاء مستخدم إداري
    if not create_admin_user():
        print("❌ فشل في إنشاء المستخدم الإداري")
        return False
    
    # 4. إنشاء البيانات التجريبية
    if not create_sample_data():
        print("❌ فشل في إنشاء البيانات التجريبية")
        return False
    
    # 5. فحص نهائي
    print("\n🔍 فحص نهائي...")
    verify_mysql_tables()
    
    print("\n" + "=" * 50)
    print("🎉 تم إعداد قاعدة بيانات MySQL بالكامل!")
    print("=" * 50)
    
    print("\n📋 ما تم إنجازه:")
    print("✅ إنشاء جميع الجداول في MySQL")
    print("✅ إنشاء مستخدم إداري (admin/admin123)")
    print("✅ إنشاء 8 أقسام للإعلانات")
    print("✅ إنشاء 3 مستخدمين تجريبيين")
    print("✅ إنشاء إعلانات تجريبية")
    
    print("\n🔗 معلومات قاعدة البيانات:")
    print("- النوع: MySQL")
    print("- الاسم: classified_ads_db")
    print("- المستخدم: root")
    print("- كلمة المرور: (فارغة)")
    print("- الخادم: localhost:3306")
    print("- الترميز: utf8mb4")
    
    print("\n🌐 لتشغيل الموقع:")
    print("python manage.py runserver")
    
    print("\n🔑 حسابات الدخول:")
    print("- المدير: admin / admin123")
    print("- مستخدم: user1 / password123")
    
    return True

if __name__ == '__main__':
    main()

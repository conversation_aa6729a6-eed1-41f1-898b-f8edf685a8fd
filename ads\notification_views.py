"""
Views for notifications
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from .models import Notification

@login_required
def notification_list(request):
    """عرض قائمة الإشعارات للمستخدم"""
    notifications = Notification.objects.filter(
        user=request.user
    ).order_by('-created_at')
    
    # تحديد الإشعارات غير المقروءة
    unread_count = notifications.filter(is_read=False).count()
    
    context = {
        'notifications': notifications,
        'unread_count': unread_count,
    }
    
    return render(request, 'ads/notifications.html', context)

@login_required
@require_POST
def mark_notification_read(request, notification_id):
    """تحديد إشعار كمقروء"""
    notification = get_object_or_404(
        Notification, 
        id=notification_id, 
        user=request.user
    )
    
    notification.is_read = True
    notification.save()
    
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({'success': True})
    
    messages.success(request, 'تم تحديد الإشعار كمقروء')
    return redirect('ads:notifications')

@login_required
@require_POST
def mark_all_notifications_read(request):
    """تحديد جميع الإشعارات كمقروءة"""
    updated_count = Notification.objects.filter(
        user=request.user,
        is_read=False
    ).update(is_read=True)
    
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'success': True,
            'updated_count': updated_count
        })
    
    messages.success(request, f'تم تحديد {updated_count} إشعار كمقروء')
    return redirect('ads:notifications')

@login_required
def notification_detail(request, notification_id):
    """عرض تفاصيل إشعار"""
    notification = get_object_or_404(
        Notification, 
        id=notification_id, 
        user=request.user
    )
    
    # تحديد الإشعار كمقروء
    if not notification.is_read:
        notification.is_read = True
        notification.save()
    
    context = {
        'notification': notification,
    }
    
    return render(request, 'ads/notification_detail.html', context)

@login_required
def get_unread_notifications_count(request):
    """الحصول على عدد الإشعارات غير المقروءة (AJAX)"""
    count = Notification.objects.filter(
        user=request.user,
        is_read=False
    ).count()
    
    return JsonResponse({'unread_count': count})

@login_required
def get_recent_notifications(request):
    """الحصول على أحدث الإشعارات (AJAX)"""
    notifications = Notification.objects.filter(
        user=request.user
    ).order_by('-created_at')[:5]
    
    notifications_data = []
    for notification in notifications:
        notifications_data.append({
            'id': notification.id,
            'title': notification.title,
            'message': notification.message[:100] + '...' if len(notification.message) > 100 else notification.message,
            'type': notification.type,
            'is_read': notification.is_read,
            'created_at': notification.created_at.strftime('%Y-%m-%d %H:%M'),
            'advertisement_id': notification.advertisement.id if notification.advertisement else None,
        })
    
    return JsonResponse({
        'notifications': notifications_data,
        'unread_count': notifications.filter(is_read=False).count()
    })

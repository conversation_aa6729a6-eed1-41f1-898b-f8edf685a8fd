#!/usr/bin/env python
"""
اختبار شامل للوحة الإدارة المطورة
"""
import os
import django
import requests
from urllib.parse import urljoin

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

def test_all_admin_urls():
    """اختبار جميع روابط لوحة الإدارة الجديدة"""
    print("🔍 اختبار جميع روابط لوحة الإدارة")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:8000"
    admin_urls = [
        # الصفحات الأساسية
        "/admin-panel/",
        "/admin-panel/users/",
        "/admin-panel/users/1/",
        "/admin-panel/ads/",
        "/admin-panel/ads/1/",
        "/admin-panel/pending-ads/",
        "/admin-panel/categories/",
        "/admin-panel/categories/create/",
        "/admin-panel/reports/",
        "/admin-panel/statistics/",
        
        # الصفحات الجديدة
        "/admin-panel/users/1/edit/",
        "/admin-panel/ads/1/edit/",
        "/admin-panel/settings/",
        "/admin-panel/activity-log/",
        "/admin-panel/backup/",
    ]
    
    working_urls = []
    broken_urls = []
    
    for url_path in admin_urls:
        full_url = urljoin(base_url, url_path)
        try:
            response = requests.get(full_url, timeout=5)
            if response.status_code == 200:
                working_urls.append(url_path)
                print(f"✅ {url_path}")
            elif response.status_code == 404:
                broken_urls.append((url_path, "404 - صفحة غير موجودة"))
                print(f"❌ {url_path} - 404")
            elif response.status_code == 500:
                broken_urls.append((url_path, "500 - خطأ في الخادم"))
                print(f"❌ {url_path} - 500")
            else:
                broken_urls.append((url_path, f"{response.status_code}"))
                print(f"⚠️ {url_path} - {response.status_code}")
        except requests.exceptions.RequestException as e:
            broken_urls.append((url_path, f"خطأ في الاتصال: {str(e)}"))
            print(f"❌ {url_path} - خطأ في الاتصال")
    
    return working_urls, broken_urls

def test_database_models():
    """اختبار النماذج الجديدة في قاعدة البيانات"""
    print(f"\n💾 اختبار النماذج الجديدة")
    print("-" * 30)
    
    try:
        from admin_panel.models import ActivityLog, SystemSettings
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # اختبار ActivityLog
        activity_count = ActivityLog.objects.count()
        print(f"✅ ActivityLog: {activity_count} سجل")
        
        # اختبار SystemSettings
        settings_count = SystemSettings.objects.count()
        print(f"✅ SystemSettings: {settings_count} إعداد")
        
        # اختبار إنشاء سجل نشاط جديد
        admin_user = User.objects.get(username='admin')
        test_log = ActivityLog.log_activity(
            user=admin_user,
            action='view',
            description='اختبار سجل الأنشطة',
            object_type='test',
            object_id=1
        )
        print(f"✅ تم إنشاء سجل نشاط تجريبي: {test_log.id}")
        
        # اختبار إعداد النظام
        test_setting = SystemSettings.set_setting(
            'test_setting',
            'test_value',
            'إعداد تجريبي'
        )
        print(f"✅ تم إنشاء إعداد تجريبي: {test_setting.key}")
        
        # تنظيف البيانات التجريبية
        test_log.delete()
        test_setting.delete()
        print(f"✅ تم تنظيف البيانات التجريبية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النماذج: {e}")
        return False

def test_admin_features():
    """اختبار الميزات الإدارية"""
    print(f"\n🎯 اختبار الميزات الإدارية")
    print("-" * 30)
    
    try:
        from ads.models import Advertisement, Category, Report, Notification
        from accounts.models import CustomUser
        from admin_panel.models import ActivityLog, SystemSettings
        
        # إحصائيات عامة
        users_count = CustomUser.objects.count()
        ads_count = Advertisement.objects.count()
        categories_count = Category.objects.count()
        reports_count = Report.objects.count()
        notifications_count = Notification.objects.count()
        activities_count = ActivityLog.objects.count()
        settings_count = SystemSettings.objects.count()
        
        print(f"👥 المستخدمون: {users_count}")
        print(f"📢 الإعلانات: {ads_count}")
        print(f"📂 الأقسام: {categories_count}")
        print(f"🚩 التقارير: {reports_count}")
        print(f"🔔 الإشعارات: {notifications_count}")
        print(f"📋 سجلات الأنشطة: {activities_count}")
        print(f"⚙️ إعدادات النظام: {settings_count}")
        
        # اختبار الإعلانات المعلقة
        pending_ads = Advertisement.objects.filter(status='pending').count()
        approved_ads = Advertisement.objects.filter(status='approved').count()
        
        print(f"⏳ الإعلانات المعلقة: {pending_ads}")
        print(f"✅ الإعلانات المعتمدة: {approved_ads}")
        
        # اختبار الإشعارات غير المقروءة
        unread_notifications = Notification.objects.filter(is_read=False).count()
        print(f"🔔 الإشعارات غير المقروءة: {unread_notifications}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الميزات: {e}")
        return False

def test_system_settings():
    """اختبار إعدادات النظام"""
    print(f"\n⚙️ اختبار إعدادات النظام")
    print("-" * 30)
    
    try:
        from admin_panel.models import SystemSettings
        
        # اختبار الإعدادات الأساسية
        essential_settings = [
            'site_name',
            'site_description',
            'contact_email',
            'auto_approve',
            'max_images',
            'enable_captcha',
            'email_notifications',
            'auto_backup'
        ]
        
        for setting_key in essential_settings:
            value = SystemSettings.get_setting(setting_key)
            if value is not None:
                print(f"✅ {setting_key}: {value[:50]}...")
            else:
                print(f"❌ {setting_key}: غير موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإعدادات: {e}")
        return False

def test_activity_logging():
    """اختبار تسجيل الأنشطة"""
    print(f"\n📋 اختبار تسجيل الأنشطة")
    print("-" * 30)
    
    try:
        from admin_panel.models import ActivityLog
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        admin_user = User.objects.get(username='admin')
        
        # إحصائيات الأنشطة
        total_activities = ActivityLog.objects.count()
        login_activities = ActivityLog.objects.filter(action='login').count()
        create_activities = ActivityLog.objects.filter(action='create').count()
        update_activities = ActivityLog.objects.filter(action='update').count()
        
        print(f"📊 إجمالي الأنشطة: {total_activities}")
        print(f"🔑 أنشطة تسجيل الدخول: {login_activities}")
        print(f"➕ أنشطة الإنشاء: {create_activities}")
        print(f"✏️ أنشطة التحديث: {update_activities}")
        
        # أحدث الأنشطة
        recent_activities = ActivityLog.objects.order_by('-timestamp')[:5]
        print(f"\n📋 أحدث الأنشطة:")
        for activity in recent_activities:
            user_name = activity.user.get_full_name() if activity.user else 'مجهول'
            print(f"   - {user_name}: {activity.get_action_display()} - {activity.description[:30]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الأنشطة: {e}")
        return False

def generate_final_report():
    """إنشاء التقرير النهائي"""
    print(f"\n📊 التقرير النهائي")
    print("=" * 50)
    
    # إحصائيات شاملة
    try:
        from ads.models import Advertisement, Category, Report, Notification
        from accounts.models import CustomUser
        from admin_panel.models import ActivityLog, SystemSettings
        
        stats = {
            'users': CustomUser.objects.count(),
            'ads': Advertisement.objects.count(),
            'categories': Category.objects.count(),
            'reports': Report.objects.count(),
            'notifications': Notification.objects.count(),
            'activities': ActivityLog.objects.count(),
            'settings': SystemSettings.objects.count(),
            'pending_ads': Advertisement.objects.filter(status='pending').count(),
            'approved_ads': Advertisement.objects.filter(status='approved').count(),
        }
        
        print(f"📈 إحصائيات النظام:")
        print(f"   👥 المستخدمون: {stats['users']}")
        print(f"   📢 الإعلانات: {stats['ads']} (معتمد: {stats['approved_ads']}, معلق: {stats['pending_ads']})")
        print(f"   📂 الأقسام: {stats['categories']}")
        print(f"   🚩 التقارير: {stats['reports']}")
        print(f"   🔔 الإشعارات: {stats['notifications']}")
        print(f"   📋 سجلات الأنشطة: {stats['activities']}")
        print(f"   ⚙️ إعدادات النظام: {stats['settings']}")
        
        return stats
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التقرير: {e}")
        return {}

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل للوحة الإدارة المطورة")
    print("=" * 60)
    
    all_tests_passed = True
    
    # اختبار الروابط
    working_urls, broken_urls = test_all_admin_urls()
    if broken_urls:
        all_tests_passed = False
    
    # اختبار النماذج
    if not test_database_models():
        all_tests_passed = False
    
    # اختبار الميزات
    if not test_admin_features():
        all_tests_passed = False
    
    # اختبار الإعدادات
    if not test_system_settings():
        all_tests_passed = False
    
    # اختبار الأنشطة
    if not test_activity_logging():
        all_tests_passed = False
    
    # التقرير النهائي
    stats = generate_final_report()
    
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ لوحة الإدارة تعمل بشكل مثالي")
        
        print(f"\n📊 ملخص النتائج:")
        print(f"✅ الروابط العاملة: {len(working_urls)}")
        print(f"❌ الروابط المكسورة: {len(broken_urls)}")
        
        print(f"\n🌐 الروابط المتاحة:")
        print(f"- لوحة التحكم: http://127.0.0.1:8000/admin-panel/")
        print(f"- إعدادات النظام: http://127.0.0.1:8000/admin-panel/settings/")
        print(f"- سجل الأنشطة: http://127.0.0.1:8000/admin-panel/activity-log/")
        print(f"- النسخ الاحتياطية: http://127.0.0.1:8000/admin-panel/backup/")
        
        print(f"\n🔑 بيانات الدخول:")
        print(f"- المدير: admin / admin123")
        
    else:
        print("❌ بعض الاختبارات فشلت")
        if broken_urls:
            print(f"\n🔗 الروابط المكسورة:")
            for url, error in broken_urls:
                print(f"   - {url}: {error}")
    
    print("=" * 60)
    
    return all_tests_passed

if __name__ == '__main__':
    main()

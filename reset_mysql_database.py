#!/usr/bin/env python
"""
Script to reset and recreate MySQL database completely
"""
import os
import sys
import mysql.connector
from mysql.connector import Error

def reset_mysql_database():
    """إعادة تعيين قاعدة بيانات MySQL بالكامل"""
    try:
        # الاتصال بـ MySQL
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            port=3306
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            print("🗑️ حذف قاعدة البيانات الموجودة...")
            cursor.execute("DROP DATABASE IF EXISTS classified_ads_db")
            
            print("🏗️ إنشاء قاعدة بيانات جديدة...")
            cursor.execute("""
                CREATE DATABASE classified_ads_db 
                CHARACTER SET utf8mb4 
                COLLATE utf8mb4_unicode_ci
            """)
            
            print("✅ تم إعادة تعيين قاعدة البيانات MySQL بنجاح!")
            return True
            
    except Error as e:
        print(f"❌ خطأ في MySQL: {e}")
        return False
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def create_fresh_tables():
    """إنشاء جداول جديدة"""
    print("\n🔧 إنشاء جداول جديدة...")
    
    # تعيين متغير البيئة لاستخدام MySQL
    os.environ['USE_MYSQL'] = 'true'
    
    # Setup Django environment
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
    import django
    django.setup()
    
    from django.core.management import execute_from_command_line
    
    try:
        # تطبيق جميع الهجرات
        print("📦 تطبيق جميع الهجرات...")
        execute_from_command_line(['manage.py', 'migrate'])
        
        print("✅ تم إنشاء جميع الجداول بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False

def verify_tables():
    """التحقق من الجداول المنشأة"""
    print("\n🔍 التحقق من الجداول...")
    
    try:
        # Setup Django environment
        os.environ['USE_MYSQL'] = 'true'
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
        import django
        django.setup()
        
        from django.db import connection
        
        with connection.cursor() as cursor:
            # عرض الجداول الموجودة
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            print(f"📁 عدد الجداول المنشأة: {len(tables)}")
            
            if tables:
                print("\n📋 الجداول الموجودة:")
                for table in tables:
                    table_name = table[0]
                    cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                    count = cursor.fetchone()[0]
                    print(f"   ✅ {table_name}: {count} سجل")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في التحقق من الجداول: {e}")
        return False

def create_admin_user():
    """إنشاء مستخدم إداري"""
    print("\n👤 إنشاء مستخدم إداري...")
    
    try:
        os.environ['USE_MYSQL'] = 'true'
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
        import django
        django.setup()
        
        from accounts.models import CustomUser
        
        # إنشاء المستخدم الإداري
        admin_user, created = CustomUser.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'مدير',
                'last_name': 'النظام',
                'is_staff': True,
                'is_superuser': True,
                'is_verified': True
            }
        )
        
        if created:
            admin_user.set_password('admin123')
            admin_user.save()
            print("✅ تم إنشاء المستخدم الإداري: admin / admin123")
        else:
            print("✅ المستخدم الإداري موجود بالفعل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدم الإداري: {e}")
        return False

def create_sample_categories():
    """إنشاء أقسام تجريبية"""
    print("\n📂 إنشاء أقسام تجريبية...")
    
    try:
        os.environ['USE_MYSQL'] = 'true'
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
        import django
        django.setup()
        
        from ads.models import Category
        
        categories_data = [
            {'name': 'عقارات', 'description': 'شقق، فيلات، أراضي للبيع والإيجار', 'icon': 'fas fa-home'},
            {'name': 'سيارات', 'description': 'سيارات جديدة ومستعملة للبيع', 'icon': 'fas fa-car'},
            {'name': 'وظائف', 'description': 'فرص عمل في جميع المجالات', 'icon': 'fas fa-briefcase'},
            {'name': 'دورات تدريبية', 'description': 'دورات ودروس في مختلف المجالات', 'icon': 'fas fa-graduation-cap'},
            {'name': 'إلكترونيات', 'description': 'أجهزة إلكترونية ومعدات تقنية', 'icon': 'fas fa-laptop'},
            {'name': 'أثاث ومنزل', 'description': 'أثاث وأدوات منزلية', 'icon': 'fas fa-couch'},
            {'name': 'خدمات', 'description': 'خدمات متنوعة', 'icon': 'fas fa-tools'},
            {'name': 'أزياء وموضة', 'description': 'ملابس وإكسسوارات', 'icon': 'fas fa-tshirt'},
        ]
        
        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={
                    'description': cat_data['description'],
                    'icon': cat_data['icon'],
                    'is_active': True
                }
            )
            if created:
                print(f"✅ تم إنشاء القسم: {category.name}")
        
        print(f"✅ تم إنشاء {len(categories_data)} أقسام")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الأقسام: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔄 إعادة تعيين وإنشاء قاعدة بيانات MySQL")
    print("=" * 50)
    
    # تحذير للمستخدم
    print("⚠️ تحذير: سيتم حذف جميع البيانات الموجودة!")
    response = input("هل تريد المتابعة؟ (y/N): ")
    
    if response.lower() != 'y':
        print("❌ تم إلغاء العملية")
        return
    
    # 1. إعادة تعيين قاعدة البيانات
    print("\n1️⃣ إعادة تعيين قاعدة البيانات...")
    if not reset_mysql_database():
        return False
    
    # 2. إنشاء جداول جديدة
    print("\n2️⃣ إنشاء جداول جديدة...")
    if not create_fresh_tables():
        return False
    
    # 3. التحقق من الجداول
    print("\n3️⃣ التحقق من الجداول...")
    if not verify_tables():
        return False
    
    # 4. إنشاء مستخدم إداري
    print("\n4️⃣ إنشاء مستخدم إداري...")
    create_admin_user()
    
    # 5. إنشاء أقسام تجريبية
    print("\n5️⃣ إنشاء أقسام تجريبية...")
    create_sample_categories()
    
    # 6. فحص نهائي
    print("\n6️⃣ فحص نهائي...")
    verify_tables()
    
    print("\n" + "=" * 50)
    print("🎉 تم إعداد قاعدة بيانات MySQL بنجاح!")
    print("=" * 50)
    
    print("\n📋 ما تم إنجازه:")
    print("✅ إعادة تعيين قاعدة البيانات")
    print("✅ إنشاء جميع الجداول")
    print("✅ إنشاء مستخدم إداري (admin/admin123)")
    print("✅ إنشاء أقسام تجريبية")
    
    print("\n🌐 لتشغيل الموقع مع MySQL:")
    print("set USE_MYSQL=true && python manage.py runserver")
    
    return True

if __name__ == '__main__':
    main()

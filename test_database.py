#!/usr/bin/env python
"""
Test database functionality
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

from django.db import connection

def test_database():
    """اختبار قاعدة البيانات"""
    print("🧪 اختبار قاعدة البيانات")
    print("=" * 40)
    
    try:
        # اختبار الاتصال
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result:
                print("✅ الاتصال بقاعدة البيانات يعمل")
            
            # فحص الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"📁 عدد الجداول: {len(tables)}")
            
            # اختبار النماذج
            from accounts.models import CustomUser
            from ads.models import Category, Advertisement
            
            users_count = CustomUser.objects.count()
            categories_count = Category.objects.count()
            ads_count = Advertisement.objects.count()
            
            print(f"👥 المستخدمون: {users_count}")
            print(f"📂 الأقسام: {categories_count}")
            print(f"📢 الإعلانات: {ads_count}")
            
            # اختبار المستخدم الإداري
            try:
                admin = CustomUser.objects.get(username='admin')
                print(f"👑 المدير: {admin.username} (موجود)")
            except CustomUser.DoesNotExist:
                print("❌ المستخدم الإداري غير موجود")
            
            print("\n✅ جميع الاختبارات نجحت!")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == '__main__':
    test_database()

"""
Signals for ads app
"""
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from .models import Advertisement, Notification

@receiver(pre_save, sender=Advertisement)
def track_status_change(sender, instance, **kwargs):
    """تتبع تغيير حالة الإعلان"""
    if instance.pk:  # إذا كان الإعلان موجود (تحديث وليس إنشاء)
        try:
            old_instance = Advertisement.objects.get(pk=instance.pk)
            instance._old_status = old_instance.status
        except Advertisement.DoesNotExist:
            instance._old_status = None
    else:
        instance._old_status = None

@receiver(post_save, sender=Advertisement)
def send_status_notification(sender, instance, created, **kwargs):
    """إرسال إشعار عند تغيير حالة الإعلان"""
    
    if created:
        # إشعار للمستخدم بأن إعلانه تم إرساله للمراجعة
        Notification.objects.create(
            user=instance.user,
            advertisement=instance,
            type='general',
            title='تم استلام إعلانك',
            message=f'تم استلام إعلانك "{instance.title}" وهو الآن قيد المراجعة. سنقوم بإشعارك فور الموافقة عليه.'
        )
    else:
        # التحقق من تغيير الحالة
        old_status = getattr(instance, '_old_status', None)
        current_status = instance.status
        
        if old_status and old_status != current_status:
            # تحديد نوع الإشعار والرسالة
            if current_status == 'approved':
                notification_type = 'ad_approved'
                title = 'تم اعتماد إعلانك'
                message = f'مبروك! تم اعتماد إعلانك "{instance.title}" وهو الآن متاح للجمهور.'
                
            elif current_status == 'rejected':
                notification_type = 'ad_rejected'
                title = 'تم رفض إعلانك'
                message = f'نأسف، تم رفض إعلانك "{instance.title}". يرجى مراجعة شروط النشر وإعادة المحاولة.'
                
            elif current_status == 'expired':
                notification_type = 'ad_expired'
                title = 'انتهت صلاحية إعلانك'
                message = f'انتهت صلاحية إعلانك "{instance.title}". يمكنك تجديده من لوحة التحكم.'
                
            else:
                return  # لا نرسل إشعار للحالات الأخرى
            
            # إنشاء الإشعار
            Notification.objects.create(
                user=instance.user,
                advertisement=instance,
                type=notification_type,
                title=title,
                message=message
            )

def create_notification(user, ad, notification_type, title, message):
    """دالة مساعدة لإنشاء إشعار"""
    return Notification.objects.create(
        user=user,
        advertisement=ad,
        type=notification_type,
        title=title,
        message=message
    )

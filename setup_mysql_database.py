#!/usr/bin/env python
"""
Script to setup MySQL database for classified ads website
"""
import os
import sys
import subprocess
import mysql.connector
from mysql.connector import Error

def check_mysql_connection():
    """التحقق من الاتصال بـ MySQL"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password=''  # أضف كلمة المرور إذا كانت موجودة
        )
        if connection.is_connected():
            print("✅ تم الاتصال بـ MySQL بنجاح")
            return connection
    except Error as e:
        print(f"❌ فشل الاتصال بـ MySQL: {e}")
        return None

def create_database(connection):
    """إنشاء قاعدة البيانات"""
    try:
        cursor = connection.cursor()
        
        # إنشاء قاعدة البيانات
        cursor.execute("""
            CREATE DATABASE IF NOT EXISTS classified_ads_db 
            CHARACTER SET utf8mb4 
            COLLATE utf8mb4_unicode_ci
        """)
        
        print("✅ تم إنشاء قاعدة البيانات classified_ads_db")
        
        # التحقق من إنشاء قاعدة البيانات
        cursor.execute("SHOW DATABASES LIKE 'classified_ads_db'")
        result = cursor.fetchone()
        
        if result:
            print("✅ تم التأكد من وجود قاعدة البيانات")
            return True
        else:
            print("❌ فشل في إنشاء قاعدة البيانات")
            return False
            
    except Error as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
        return False
    finally:
        if cursor:
            cursor.close()

def run_django_migrations():
    """تشغيل هجرات Django"""
    try:
        print("\n📦 إنشاء ملفات الهجرة...")
        result = subprocess.run(['python', 'manage.py', 'makemigrations'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم إنشاء ملفات الهجرة")
        else:
            print(f"❌ فشل في إنشاء ملفات الهجرة: {result.stderr}")
            return False
        
        print("\n🔄 تطبيق الهجرات...")
        result = subprocess.run(['python', 'manage.py', 'migrate'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم تطبيق الهجرات بنجاح")
            return True
        else:
            print(f"❌ فشل في تطبيق الهجرات: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل الهجرات: {e}")
        return False

def create_superuser():
    """إنشاء مستخدم إداري"""
    try:
        print("\n👤 إنشاء مستخدم إداري...")
        
        # التحقق من وجود مستخدم إداري
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
        import django
        django.setup()
        
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        if User.objects.filter(username='admin').exists():
            print("✅ المستخدم الإداري موجود بالفعل")
            return True
        
        # إنشاء مستخدم إداري جديد
        admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123'
        )
        print("✅ تم إنشاء المستخدم الإداري: admin / admin123")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء المستخدم الإداري: {e}")
        return False

def load_sample_data():
    """تحميل البيانات التجريبية"""
    try:
        print("\n📊 تحميل البيانات التجريبية...")
        result = subprocess.run(['python', 'create_sample_data.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم تحميل البيانات التجريبية")
            return True
        else:
            print(f"⚠️ تحذير: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"⚠️ تحذير في تحميل البيانات التجريبية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إعداد قاعدة بيانات MySQL لموقع الإعلانات")
    print("=" * 60)
    
    # 1. التحقق من الاتصال بـ MySQL
    print("\n1️⃣ التحقق من الاتصال بـ MySQL...")
    connection = check_mysql_connection()
    if not connection:
        print("\n❌ فشل الإعداد: لا يمكن الاتصال بـ MySQL")
        print("يرجى التأكد من:")
        print("- تشغيل خدمة MySQL")
        print("- صحة إعدادات الاتصال")
        return False
    
    # 2. إنشاء قاعدة البيانات
    print("\n2️⃣ إنشاء قاعدة البيانات...")
    if not create_database(connection):
        connection.close()
        return False
    
    connection.close()
    
    # 3. تطبيق هجرات Django
    print("\n3️⃣ تطبيق هجرات Django...")
    if not run_django_migrations():
        return False
    
    # 4. إنشاء مستخدم إداري
    print("\n4️⃣ إنشاء مستخدم إداري...")
    create_superuser()
    
    # 5. تحميل البيانات التجريبية
    print("\n5️⃣ تحميل البيانات التجريبية...")
    load_sample_data()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("🎉 تم إعداد قاعدة البيانات بنجاح!")
    print("=" * 60)
    print("\n📋 معلومات قاعدة البيانات:")
    print("- النوع: MySQL")
    print("- الاسم: classified_ads_db")
    print("- المستخدم: root")
    print("- الخادم: localhost:3306")
    print("\n🔑 حساب المدير:")
    print("- اسم المستخدم: admin")
    print("- كلمة المرور: admin123")
    print("\n🌐 لتشغيل الموقع:")
    print("python manage.py runserver")
    
    return True

if __name__ == '__main__':
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⚠️ تم إلغاء العملية بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)

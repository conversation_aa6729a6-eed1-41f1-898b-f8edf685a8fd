from django.contrib import admin
from .models import Category, Advertisement, AdImage, Report

class AdImageInline(admin.TabularInline):
    """إدارة صور الإعلانات داخل الإعلان"""
    model = AdImage
    extra = 1
    max_num = 5

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    """إدارة أقسام الإعلانات"""
    list_display = ['name', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    prepopulated_fields = {'name': ('name',)}

@admin.register(Advertisement)
class AdvertisementAdmin(admin.ModelAdmin):
    """إدارة الإعلانات"""
    list_display = ['title', 'category', 'user', 'status', 'price', 'is_featured', 'views_count', 'created_at']
    list_filter = ['status', 'category', 'is_featured', 'created_at']
    search_fields = ['title', 'description', 'user__username']
    readonly_fields = ['views_count', 'created_at', 'updated_at']
    inlines = [AdImageInline]

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('title', 'description', 'category', 'user')
        }),
        ('تفاصيل الإعلان', {
            'fields': ('price', 'location', 'phone', 'email')
        }),
        ('إعدادات الإعلان', {
            'fields': ('status', 'is_featured', 'expires_at')
        }),
        ('إحصائيات', {
            'fields': ('views_count', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(AdImage)
class AdImageAdmin(admin.ModelAdmin):
    """إدارة صور الإعلانات"""
    list_display = ['advertisement', 'is_main', 'created_at']
    list_filter = ['is_main', 'created_at']
    search_fields = ['advertisement__title']

@admin.register(Report)
class ReportAdmin(admin.ModelAdmin):
    """إدارة التقارير"""
    list_display = ['advertisement', 'user', 'report_type', 'is_resolved', 'created_at']
    list_filter = ['report_type', 'is_resolved', 'created_at']
    search_fields = ['advertisement__title', 'user__username', 'description']
    readonly_fields = ['created_at']

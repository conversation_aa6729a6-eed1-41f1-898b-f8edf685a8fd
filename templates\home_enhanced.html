{% extends 'base_landing.html' %}
{% load static %}

{% block title %}الرئيسية - أفضل موقع للإعلانات المبوبة في المملكة{% endblock %}

{% block extra_css %}
<link href="{% static 'css/enhanced-landing.css' %}" rel="stylesheet">
<style>
/* Additional specific styles */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden;
}

.floating-element {
    position: absolute;
    opacity: 0.1;
    animation: float 8s ease-in-out infinite;
    color: white;
}

.floating-element:nth-child(1) { 
    top: 20%; 
    left: 10%; 
    animation-delay: 0s; 
    font-size: 3rem;
}

.floating-element:nth-child(2) { 
    top: 60%; 
    right: 15%; 
    animation-delay: 3s; 
    font-size: 2.5rem;
}

.floating-element:nth-child(3) { 
    bottom: 30%; 
    left: 20%; 
    animation-delay: 6s; 
    font-size: 3.5rem;
}

.floating-element:nth-child(4) { 
    top: 40%; 
    right: 30%; 
    animation-delay: 2s; 
    font-size: 2rem;
}

.floating-element:nth-child(5) { 
    bottom: 50%; 
    left: 60%; 
    animation-delay: 4s; 
    font-size: 2.8rem;
}

@keyframes float {
    0%, 100% { 
        transform: translateY(0px) rotate(0deg); 
        opacity: 0.1;
    }
    25% { 
        transform: translateY(-20px) rotate(5deg); 
        opacity: 0.15;
    }
    50% { 
        transform: translateY(-40px) rotate(-5deg); 
        opacity: 0.2;
    }
    75% { 
        transform: translateY(-20px) rotate(3deg); 
        opacity: 0.15;
    }
}

/* Enhanced badge styles */
.featured-badge {
    position: absolute;
    top: var(--space-3);
    right: var(--space-3);
    background: var(--gradient-secondary);
    color: white;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    z-index: 2;
    box-shadow: var(--shadow-md);
}

.urgent-badge {
    background: var(--gradient-primary);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Stats animation */
.stat-number {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    display: block;
    margin-bottom: var(--space-2);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    font-weight: 500;
}

/* Testimonial enhancements */
.testimonial-card {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    box-shadow: var(--shadow-lg);
    text-align: center;
    transition: var(--transition-normal);
    border: 1px solid var(--neutral-200);
}

.testimonial-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

.testimonial-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto var(--space-4);
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: var(--font-size-2xl);
    box-shadow: var(--shadow-lg);
}

.testimonial-text {
    font-size: var(--font-size-lg);
    line-height: var(--leading-relaxed);
    color: var(--neutral-600);
    margin-bottom: var(--space-4);
    font-style: italic;
}

.testimonial-name {
    font-weight: 600;
    color: var(--neutral-800);
    margin-bottom: var(--space-2);
}

.testimonial-rating {
    color: var(--warning-500);
    font-size: var(--font-size-lg);
}

/* CTA Section */
.cta-section {
    background: var(--gradient-hero);
    color: white;
    padding: var(--space-20) 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="10" height="10" patternUnits="userSpaceOnUse"><circle cx="5" cy="5" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23dots)"/></svg>');
}

.cta-content {
    position: relative;
    z-index: 2;
}

.cta-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--space-4);
}

.cta-subtitle {
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-8);
    opacity: 0.9;
}

/* Features section */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-16);
}

.feature-item {
    text-align: center;
    padding: var(--space-6);
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--gradient-soft);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-4);
    font-size: var(--font-size-3xl);
    color: var(--primary-600);
    box-shadow: var(--shadow-md);
}

.feature-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--space-2);
    color: var(--neutral-800);
}

.feature-description {
    color: var(--neutral-600);
    line-height: var(--leading-relaxed);
}
</style>
{% endblock %}

{% block content %}
<!-- Enhanced Hero Section -->
<section class="hero-enhanced">
    <!-- Floating Elements -->
    <div class="floating-elements">
        <div class="floating-element">
            <i class="fas fa-home"></i>
        </div>
        <div class="floating-element">
            <i class="fas fa-car"></i>
        </div>
        <div class="floating-element">
            <i class="fas fa-mobile-alt"></i>
        </div>
        <div class="floating-element">
            <i class="fas fa-laptop"></i>
        </div>
        <div class="floating-element">
            <i class="fas fa-briefcase"></i>
        </div>
    </div>
    
    <div class="container-responsive">
        <div class="hero-content-enhanced">
            <h1 class="hero-title-enhanced">أفضل موقع للإعلانات المبوبة</h1>
            <p class="hero-subtitle-enhanced">اكتشف آلاف الإعلانات المتنوعة أو انشر إعلانك مجاناً واصل لملايين المشترين في جميع أنحاء المملكة</p>
            
            <!-- Enhanced Search -->
            <div class="search-enhanced">
                <form method="GET" action="{% url 'ads:search' %}" class="search-form-enhanced">
                    <input type="text" name="q" class="search-input-enhanced" placeholder="ابحث عن أي شيء... سيارات، عقارات، وظائف، إلكترونيات" aria-label="البحث في الإعلانات">
                    <button type="submit" class="search-btn-enhanced">
                        <i class="fas fa-search me-2"></i>ابحث الآن
                    </button>
                </form>
            </div>
            
            <!-- Enhanced CTA Buttons -->
            <div class="cta-buttons-enhanced">
                <a href="{% url 'ads:create' %}" class="cta-btn-enhanced cta-btn-primary">
                    <i class="fas fa-plus"></i>أضف إعلانك مجاناً
                </a>
                <a href="{% url 'ads:list' %}" class="cta-btn-enhanced cta-btn-secondary">
                    <i class="fas fa-search"></i>تصفح الإعلانات
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Categories Section with Horizontal Scrolling -->
<section class="section-enhanced" style="background: var(--neutral-50);">
    <div class="container-responsive">
        <h2 class="section-title-enhanced">تصفح حسب الأقسام</h2>
        <p class="section-subtitle-enhanced">اختر من بين مجموعة واسعة من الأقسام المتنوعة</p>
        
        <div class="categories-container" style="position: relative;">
            <div class="categories-horizontal">
                {% if categories %}
                    {% for category in categories %}
                    <div class="category-item-horizontal">
                        <div class="category-icon-horizontal">
                            <i class="{{ category.icon|default:'fas fa-tag' }}"></i>
                        </div>
                        <h3 class="category-title-horizontal">{{ category.name }}</h3>
                        <p class="category-count-horizontal">
                            {{ category.ads_count|default:0 }} إعلان
                        </p>
                        <a href="{% url 'ads:search' %}?category={{ category.id }}" style="display: block; margin-top: 12px; color: var(--primary-600); font-weight: 500; text-decoration: none;">
                            تصفح الآن →
                        </a>
                    </div>
                    {% endfor %}
                {% else %}
                    <!-- Default Categories -->
                    <div class="category-item-horizontal">
                        <div class="category-icon-horizontal"><i class="fas fa-car"></i></div>
                        <h3 class="category-title-horizontal">سيارات ومركبات</h3>
                        <p class="category-count-horizontal">1,250 إعلان</p>
                        <a href="{% url 'ads:list' %}" style="display: block; margin-top: 12px; color: var(--primary-600); font-weight: 500; text-decoration: none;">تصفح الآن →</a>
                    </div>
                    <div class="category-item-horizontal">
                        <div class="category-icon-horizontal"><i class="fas fa-home"></i></div>
                        <h3 class="category-title-horizontal">عقارات</h3>
                        <p class="category-count-horizontal">890 إعلان</p>
                        <a href="{% url 'ads:list' %}" style="display: block; margin-top: 12px; color: var(--primary-600); font-weight: 500; text-decoration: none;">تصفح الآن →</a>
                    </div>
                    <div class="category-item-horizontal">
                        <div class="category-icon-horizontal"><i class="fas fa-laptop"></i></div>
                        <h3 class="category-title-horizontal">إلكترونيات</h3>
                        <p class="category-count-horizontal">675 إعلان</p>
                        <a href="{% url 'ads:list' %}" style="display: block; margin-top: 12px; color: var(--primary-600); font-weight: 500; text-decoration: none;">تصفح الآن →</a>
                    </div>
                    <div class="category-item-horizontal">
                        <div class="category-icon-horizontal"><i class="fas fa-briefcase"></i></div>
                        <h3 class="category-title-horizontal">وظائف</h3>
                        <p class="category-count-horizontal">420 إعلان</p>
                        <a href="{% url 'ads:list' %}" style="display: block; margin-top: 12px; color: var(--primary-600); font-weight: 500; text-decoration: none;">تصفح الآن →</a>
                    </div>
                    <div class="category-item-horizontal">
                        <div class="category-icon-horizontal"><i class="fas fa-tshirt"></i></div>
                        <h3 class="category-title-horizontal">أزياء وموضة</h3>
                        <p class="category-count-horizontal">320 إعلان</p>
                        <a href="{% url 'ads:list' %}" style="display: block; margin-top: 12px; color: var(--primary-600); font-weight: 500; text-decoration: none;">تصفح الآن →</a>
                    </div>
                    <div class="category-item-horizontal">
                        <div class="category-icon-horizontal"><i class="fas fa-couch"></i></div>
                        <h3 class="category-title-horizontal">أثاث ومنزل</h3>
                        <p class="category-count-horizontal">280 إعلان</p>
                        <a href="{% url 'ads:list' %}" style="display: block; margin-top: 12px; color: var(--primary-600); font-weight: 500; text-decoration: none;">تصفح الآن →</a>
                    </div>
                    <div class="category-item-horizontal">
                        <div class="category-icon-horizontal"><i class="fas fa-gamepad"></i></div>
                        <h3 class="category-title-horizontal">ألعاب ورياضة</h3>
                        <p class="category-count-horizontal">195 إعلان</p>
                        <a href="{% url 'ads:list' %}" style="display: block; margin-top: 12px; color: var(--primary-600); font-weight: 500; text-decoration: none;">تصفح الآن →</a>
                    </div>
                    <div class="category-item-horizontal">
                        <div class="category-icon-horizontal"><i class="fas fa-tools"></i></div>
                        <h3 class="category-title-horizontal">خدمات</h3>
                        <p class="category-count-horizontal">150 إعلان</p>
                        <a href="{% url 'ads:list' %}" style="display: block; margin-top: 12px; color: var(--primary-600); font-weight: 500; text-decoration: none;">تصفح الآن →</a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Featured Ads Section with Slider -->
<section class="section-enhanced">
    <div class="container-responsive">
        <h2 class="section-title-enhanced">الإعلانات المميزة</h2>
        <p class="section-subtitle-enhanced">أفضل الإعلانات المختارة بعناية لك</p>

        {% if featured_ads %}
            <div class="enhanced-slider featured-slider">
                {% for ad in featured_ads %}
                <div class="slide-item">
                    <div class="card-enhanced card-compact">
                        <div class="featured-badge">
                            <i class="fas fa-star me-1"></i>مميز
                        </div>
                        {% if ad.images.exists %}
                            <img src="{{ ad.images.first.image.url }}" class="card-image-enhanced" alt="{{ ad.title }}">
                        {% else %}
                            <div class="card-image-enhanced" style="display: flex; align-items: center; justify-content: center; background: var(--gradient-soft);">
                                <i class="fas fa-image fa-3x" style="color: var(--neutral-400);"></i>
                            </div>
                        {% endif %}
                        <div class="card-content-enhanced">
                            <h3 class="card-title-enhanced">{{ ad.title|truncatechars:35 }}</h3>
                            <p class="card-description-enhanced">{{ ad.description|truncatechars:60 }}</p>
                            <div class="card-footer-enhanced">
                                {% if ad.price %}
                                    <span class="card-price-enhanced">{{ ad.price|floatformat:0 }} ريال</span>
                                {% else %}
                                    <span style="color: var(--neutral-500); font-size: var(--font-size-sm);">السعر غير محدد</span>
                                {% endif %}
                                <span class="card-time-enhanced">{{ ad.created_at|timesince }}</span>
                            </div>
                            <a href="{% url 'ads:detail' ad.pk %}" class="card-btn-enhanced">
                                <i class="fas fa-eye me-1"></i>عرض التفاصيل
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <!-- Sample Featured Ads -->
            <div class="enhanced-slider featured-slider">
                <div class="slide-item">
                    <div class="card-enhanced card-compact">
                        <div class="featured-badge">
                            <i class="fas fa-star me-1"></i>مميز
                        </div>
                        <div class="card-image-enhanced" style="display: flex; align-items: center; justify-content: center; background: var(--gradient-primary);">
                            <i class="fas fa-car fa-4x text-white"></i>
                        </div>
                        <div class="card-content-enhanced">
                            <h3 class="card-title-enhanced">سيارة تويوتا كامري 2022</h3>
                            <p class="card-description-enhanced">سيارة بحالة ممتازة، قليلة الاستخدام</p>
                            <div class="card-footer-enhanced">
                                <span class="card-price-enhanced">85,000 ريال</span>
                                <span class="card-time-enhanced">منذ يومين</span>
                            </div>
                            <a href="{% url 'ads:create' %}" class="card-btn-enhanced">
                                <i class="fas fa-plus me-1"></i>أضف إعلانك هنا
                            </a>
                        </div>
                    </div>
                </div>
                <div class="slide-item">
                    <div class="card-enhanced card-compact">
                        <div class="featured-badge">
                            <i class="fas fa-star me-1"></i>مميز
                        </div>
                        <div class="card-image-enhanced" style="display: flex; align-items: center; justify-content: center; background: var(--gradient-secondary);">
                            <i class="fas fa-home fa-4x text-white"></i>
                        </div>
                        <div class="card-content-enhanced">
                            <h3 class="card-title-enhanced">شقة للإيجار - الرياض</h3>
                            <p class="card-description-enhanced">شقة 3 غرف في موقع مميز</p>
                            <div class="card-footer-enhanced">
                                <span class="card-price-enhanced">2,500 ريال/شهر</span>
                                <span class="card-time-enhanced">منذ 3 أيام</span>
                            </div>
                            <a href="{% url 'ads:create' %}" class="card-btn-enhanced">
                                <i class="fas fa-plus me-1"></i>أضف إعلانك هنا
                            </a>
                        </div>
                    </div>
                </div>
                <div class="slide-item">
                    <div class="card-enhanced card-compact">
                        <div class="featured-badge">
                            <i class="fas fa-star me-1"></i>مميز
                        </div>
                        <div class="card-image-enhanced" style="display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, var(--success-500), var(--primary-500));">
                            <i class="fas fa-laptop fa-4x text-white"></i>
                        </div>
                        <div class="card-content-enhanced">
                            <h3 class="card-title-enhanced">لابتوب MacBook Pro</h3>
                            <p class="card-description-enhanced">جهاز بحالة ممتازة مع الضمان</p>
                            <div class="card-footer-enhanced">
                                <span class="card-price-enhanced">4,200 ريال</span>
                                <span class="card-time-enhanced">منذ أسبوع</span>
                            </div>
                            <a href="{% url 'ads:create' %}" class="card-btn-enhanced">
                                <i class="fas fa-plus me-1"></i>أضف إعلانك هنا
                            </a>
                        </div>
                    </div>
                </div>
                <div class="slide-item">
                    <div class="card-enhanced card-compact">
                        <div class="featured-badge urgent-badge">
                            <i class="fas fa-bolt me-1"></i>عاجل
                        </div>
                        <div class="card-image-enhanced" style="display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, var(--warning-500), var(--error-500));">
                            <i class="fas fa-briefcase fa-4x text-white"></i>
                        </div>
                        <div class="card-content-enhanced">
                            <h3 class="card-title-enhanced">وظيفة مطور ويب</h3>
                            <p class="card-description-enhanced">فرصة عمل ممتازة في شركة تقنية</p>
                            <div class="card-footer-enhanced">
                                <span class="card-price-enhanced">8,000 ريال/شهر</span>
                                <span class="card-time-enhanced">منذ ساعة</span>
                            </div>
                            <a href="{% url 'ads:create' %}" class="card-btn-enhanced">
                                <i class="fas fa-plus me-1"></i>أضف إعلانك هنا
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        <div style="text-align: center; margin-top: var(--space-12);">
            <a href="{% url 'ads:list' %}" style="background: var(--gradient-primary); color: white; padding: var(--space-4) var(--space-8); border-radius: var(--radius-full); text-decoration: none; font-weight: 600; display: inline-flex; align-items: center; gap: var(--space-2); box-shadow: var(--shadow-lg); transition: var(--transition-normal);" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='var(--shadow-xl)'" onmouseout="this.style.transform=''; this.style.boxShadow='var(--shadow-lg)'">
                <i class="fas fa-eye"></i>عرض جميع الإعلانات
            </a>
        </div>
    </div>
</section>

<!-- Enhanced Statistics Section -->
<section class="section-enhanced" style="background: var(--gradient-hero); color: white;">
    <div class="container-responsive">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--space-8); text-align: center;">
            <div>
                <span class="stat-number" data-count="{{ stats.total_ads|default:1250 }}">0</span>
                <div class="stat-label">إعلان نشط</div>
            </div>
            <div>
                <span class="stat-number" data-count="{{ stats.total_users|default:5680 }}">0</span>
                <div class="stat-label">مستخدم مسجل</div>
            </div>
            <div>
                <span class="stat-number" data-count="{{ stats.total_categories|default:12 }}">0</span>
                <div class="stat-label">قسم متنوع</div>
            </div>
            <div>
                <span class="stat-number" data-count="{{ stats.featured_ads_count|default:89 }}">0</span>
                <div class="stat-label">إعلان مميز</div>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Testimonials Section -->
<section class="section-enhanced" style="background: var(--neutral-50);">
    <div class="container-responsive">
        <h2 class="section-title-enhanced">ماذا يقول عملاؤنا</h2>
        <p class="section-subtitle-enhanced">تجارب حقيقية من مستخدمين راضين عن خدماتنا</p>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: var(--space-8); margin-top: var(--space-12);">
            <div class="testimonial-card">
                <div class="testimonial-avatar">أ</div>
                <h5 class="testimonial-name">أحمد محمد</h5>
                <p class="testimonial-text">"موقع رائع ساعدني في بيع سيارتي بسرعة وبسعر ممتاز. التعامل سهل والواجهة واضحة."</p>
                <div class="testimonial-rating">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                </div>
            </div>
            <div class="testimonial-card">
                <div class="testimonial-avatar" style="background: var(--gradient-secondary);">ف</div>
                <h5 class="testimonial-name">فاطمة العلي</h5>
                <p class="testimonial-text">"وجدت الشقة المثالية لعائلتي من خلال هذا الموقع. خدمة ممتازة وإعلانات موثوقة."</p>
                <div class="testimonial-rating">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                </div>
            </div>
            <div class="testimonial-card">
                <div class="testimonial-avatar" style="background: linear-gradient(135deg, var(--success-500), var(--primary-500));">م</div>
                <h5 class="testimonial-name">محمد السعد</h5>
                <p class="testimonial-text">"أفضل موقع للإعلانات المبوبة في المملكة. سهولة في الاستخدام ونتائج سريعة."</p>
                <div class="testimonial-rating">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced CTA Section -->
<section class="cta-section">
    <div class="container-responsive">
        <div class="cta-content">
            <h2 class="cta-title">جاهز لنشر إعلانك؟</h2>
            <p class="cta-subtitle">انضم لآلاف المستخدمين الذين يثقون بنا في بيع وشراء منتجاتهم</p>
            <div class="cta-buttons-enhanced">
                <a href="{% url 'ads:create' %}" class="cta-btn-enhanced cta-btn-primary">
                    <i class="fas fa-plus"></i>أضف إعلانك مجاناً
                </a>
                <a href="{% url 'accounts:register' %}" class="cta-btn-enhanced cta-btn-secondary">
                    <i class="fas fa-user-plus"></i>إنشاء حساب جديد
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Features Section -->
<section class="section-enhanced">
    <div class="container-responsive">
        <h2 class="section-title-enhanced">لماذا تختارنا؟</h2>
        <p class="section-subtitle-enhanced">نقدم لك أفضل تجربة في عالم الإعلانات المبوبة</p>

        <div class="features-grid">
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h4 class="feature-title">آمن وموثوق</h4>
                <p class="feature-description">جميع الإعلانات تخضع للمراجعة لضمان الجودة والأمان</p>
            </div>
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <h4 class="feature-title">سريع وسهل</h4>
                <p class="feature-description">انشر إعلانك في دقائق واحصل على نتائج فورية</p>
            </div>
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h4 class="feature-title">مجتمع كبير</h4>
                <p class="feature-description">آلاف المستخدمين النشطين يومياً في جميع أنحاء المملكة</p>
            </div>
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <h4 class="feature-title">متوافق مع الجوال</h4>
                <p class="feature-description">تصميم متجاوب يعمل بسلاسة على جميع الأجهزة</p>
            </div>
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h4 class="feature-title">بحث متقدم</h4>
                <p class="feature-description">أدوات بحث قوية للعثور على ما تريد بسرعة</p>
            </div>
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-headset"></i>
                </div>
                <h4 class="feature-title">دعم فني 24/7</h4>
                <p class="feature-description">فريق دعم متخصص لمساعدتك في أي وقت</p>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/enhanced-sliders.js' %}"></script>
<script>
// Enhanced landing page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize counter animation for statistics
    function animateCounters() {
        const counters = document.querySelectorAll('.stat-number');
        const speed = 200;

        const animateCounter = (counter) => {
            const target = parseInt(counter.getAttribute('data-count'));
            const count = parseInt(counter.innerText);
            const increment = target / speed;

            if (count < target) {
                counter.innerText = Math.ceil(count + increment);
                setTimeout(() => animateCounter(counter), 1);
            } else {
                counter.innerText = target.toLocaleString('ar-SA');
            }
        };

        // Intersection Observer for triggering animation
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const counter = entry.target;
                    if (!counter.classList.contains('animated')) {
                        counter.classList.add('animated');
                        animateCounter(counter);
                    }
                }
            });
        }, { threshold: 0.5 });

        counters.forEach(counter => observer.observe(counter));
    }

    // Initialize scroll animations
    function initScrollAnimations() {
        const animatedElements = document.querySelectorAll('.card-enhanced, .feature-item, .testimonial-card');

        const scrollObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 100);
                }
            });
        }, { threshold: 0.1 });

        animatedElements.forEach(element => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(30px)';
            element.style.transition = 'all 0.6s ease';
            scrollObserver.observe(element);
        });
    }

    // Enhanced search functionality
    function enhanceSearch() {
        const searchInput = document.querySelector('.search-input-enhanced');
        const searchForm = document.querySelector('.search-form-enhanced');

        if (!searchInput) return;

        // Search suggestions
        const suggestions = [
            'سيارات للبيع',
            'شقق للإيجار',
            'هواتف ذكية',
            'وظائف في الرياض',
            'أثاث مستعمل',
            'لابتوب للبيع',
            'عقارات جدة',
            'دراجات نارية'
        ];

        // Create suggestions dropdown
        const suggestionsContainer = document.createElement('div');
        suggestionsContainer.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-xl);
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
            margin-top: 8px;
        `;

        searchInput.parentElement.style.position = 'relative';
        searchInput.parentElement.appendChild(suggestionsContainer);

        searchInput.addEventListener('focus', () => {
            showSuggestions();
        });

        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.toLowerCase();
            if (query.length > 0) {
                const filtered = suggestions.filter(s => s.includes(query));
                showSuggestions(filtered);
            } else {
                showSuggestions();
            }
        });

        searchInput.addEventListener('blur', () => {
            setTimeout(() => {
                suggestionsContainer.style.display = 'none';
            }, 200);
        });

        function showSuggestions(items = suggestions) {
            suggestionsContainer.innerHTML = '';
            items.slice(0, 5).forEach(item => {
                const div = document.createElement('div');
                div.textContent = item;
                div.style.cssText = `
                    padding: 12px 16px;
                    cursor: pointer;
                    border-bottom: 1px solid var(--neutral-200);
                    transition: background 0.2s;
                    color: var(--neutral-700);
                `;
                div.addEventListener('mouseenter', () => {
                    div.style.background = 'var(--neutral-50)';
                });
                div.addEventListener('mouseleave', () => {
                    div.style.background = 'white';
                });
                div.addEventListener('click', () => {
                    searchInput.value = item;
                    suggestionsContainer.style.display = 'none';
                    searchForm.submit();
                });
                suggestionsContainer.appendChild(div);
            });
            suggestionsContainer.style.display = items.length > 0 ? 'block' : 'none';
        }
    }

    // Enhanced button interactions
    function enhanceButtons() {
        const buttons = document.querySelectorAll('.cta-btn-enhanced, .card-btn-enhanced');

        buttons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px) scale(1.02)';
            });

            button.addEventListener('mouseleave', function() {
                this.style.transform = '';
            });

            button.addEventListener('mousedown', function() {
                this.style.transform = 'translateY(-1px) scale(0.98)';
            });

            button.addEventListener('mouseup', function() {
                this.style.transform = 'translateY(-3px) scale(1.02)';
            });
        });
    }

    // Smooth scrolling for anchor links
    function initSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    // Initialize all enhancements
    animateCounters();
    initScrollAnimations();
    enhanceSearch();
    enhanceButtons();
    initSmoothScrolling();

    // Performance optimization
    setTimeout(() => {
        // Preload critical images
        const criticalImages = document.querySelectorAll('img[src]');
        criticalImages.forEach(img => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'image';
            link.href = img.src;
            document.head.appendChild(link);
        });
    }, 1000);
});

// Loading animation
window.addEventListener('load', function() {
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';

    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});
</script>
{% endblock %}

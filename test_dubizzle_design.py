#!/usr/bin/env python
"""
اختبار شامل لتصميم Dubizzle الجديد
"""
import os
import django
import requests
import time
from urllib.parse import urljoin

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

from ads.models import Advertisement, Category
from django.contrib.auth import get_user_model

User = get_user_model()

def test_dubizzle_homepage():
    """اختبار الصفحة الرئيسية بتصميم Dubizzle"""
    print("🏠 اختبار الصفحة الرئيسية - تصميم Dubizzle")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:8000"
    
    try:
        start_time = time.time()
        response = requests.get(base_url, timeout=10)
        load_time = time.time() - start_time
        
        if response.status_code == 200:
            print(f"✅ الصفحة تحملت بنجاح")
            print(f"⏱️ وقت التحميل: {load_time:.2f} ثانية")
            
            content = response.text
            
            # فحص عناصر تصميم Dubizzle
            dubizzle_elements = [
                'dubizzle-header',
                'dubizzle-logo',
                'dubizzle-search',
                'dubizzle-nav',
                'dubizzle-hero',
                'categories-grid',
                'featured-section',
                'cta-section'
            ]
            
            found_elements = []
            missing_elements = []
            
            for element in dubizzle_elements:
                if element in content:
                    found_elements.append(element)
                    print(f"✅ {element}: موجود")
                else:
                    missing_elements.append(element)
                    print(f"❌ {element}: مفقود")
            
            # فحص نظام الألوان
            dubizzle_colors = [
                '--dubizzle-primary',
                '--dubizzle-primary-dark',
                '--dubizzle-secondary',
                '--neutral-50',
                '--neutral-800'
            ]
            
            found_colors = sum(1 for color in dubizzle_colors if color in content)
            color_score = (found_colors / len(dubizzle_colors)) * 100
            
            print(f"\n🎨 نظام ألوان Dubizzle: {color_score:.1f}% ({found_colors}/{len(dubizzle_colors)})")
            
            # فحص الاستجابة
            responsive_elements = [
                'container-responsive',
                'grid-template-columns',
                '@media',
                'mobile-first'
            ]
            
            found_responsive = sum(1 for element in responsive_elements if element in content)
            responsive_score = (found_responsive / len(responsive_elements)) * 100
            
            print(f"📱 التصميم المتجاوب: {responsive_score:.1f}% ({found_responsive}/{len(responsive_elements)})")
            
            return {
                'success': True,
                'load_time': load_time,
                'elements_score': (len(found_elements) / len(dubizzle_elements)) * 100,
                'color_score': color_score,
                'responsive_score': responsive_score,
                'found_elements': len(found_elements),
                'total_elements': len(dubizzle_elements)
            }
            
        else:
            print(f"❌ فشل في تحميل الصفحة: {response.status_code}")
            return {'success': False, 'error': f'HTTP {response.status_code}'}
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return {'success': False, 'error': str(e)}

def test_ads_list_page():
    """اختبار صفحة قائمة الإعلانات"""
    print(f"\n📋 اختبار صفحة قائمة الإعلانات")
    print("-" * 40)
    
    base_url = "http://127.0.0.1:8000/ads/"
    
    try:
        start_time = time.time()
        response = requests.get(base_url, timeout=10)
        load_time = time.time() - start_time
        
        if response.status_code == 200:
            print(f"✅ صفحة الإعلانات تحملت بنجاح")
            print(f"⏱️ وقت التحميل: {load_time:.2f} ثانية")
            
            content = response.text
            
            # فحص عناصر صفحة الإعلانات
            list_elements = [
                'filters-sidebar',
                'ads-results',
                'ads-grid',
                'pagination',
                'sort-controls',
                'view-toggle',
                'filter-group'
            ]
            
            found_list_elements = []
            for element in list_elements:
                if element in content:
                    found_list_elements.append(element)
                    print(f"✅ {element}: موجود")
                else:
                    print(f"❌ {element}: مفقود")
            
            list_score = (len(found_list_elements) / len(list_elements)) * 100
            print(f"\n📊 نقاط صفحة الإعلانات: {list_score:.1f}%")
            
            return {
                'success': True,
                'load_time': load_time,
                'score': list_score,
                'found_elements': len(found_list_elements),
                'total_elements': len(list_elements)
            }
            
        else:
            print(f"❌ فشل في تحميل صفحة الإعلانات: {response.status_code}")
            return {'success': False, 'error': f'HTTP {response.status_code}'}
            
    except Exception as e:
        print(f"❌ خطأ في اختبار صفحة الإعلانات: {e}")
        return {'success': False, 'error': str(e)}

def test_ad_detail_page():
    """اختبار صفحة تفاصيل الإعلان"""
    print(f"\n🔍 اختبار صفحة تفاصيل الإعلان")
    print("-" * 35)
    
    try:
        # الحصول على أول إعلان متاح
        ad = Advertisement.objects.filter(status='approved').first()
        if not ad:
            print("❌ لا توجد إعلانات متاحة للاختبار")
            return {'success': False, 'error': 'No ads available'}
        
        detail_url = f"http://127.0.0.1:8000/ads/{ad.id}/"
        
        start_time = time.time()
        response = requests.get(detail_url, timeout=10)
        load_time = time.time() - start_time
        
        if response.status_code == 200:
            print(f"✅ صفحة تفاصيل الإعلان تحملت بنجاح")
            print(f"⏱️ وقت التحميل: {load_time:.2f} ثانية")
            print(f"📋 الإعلان المختبر: {ad.title[:30]}...")
            
            content = response.text
            
            # فحص عناصر صفحة التفاصيل
            detail_elements = [
                'image-gallery',
                'ad-content',
                'ad-sidebar',
                'contact-card',
                'similar-ads',
                'breadcrumb',
                'ad-meta',
                'seller-avatar'
            ]
            
            found_detail_elements = []
            for element in detail_elements:
                if element in content:
                    found_detail_elements.append(element)
                    print(f"✅ {element}: موجود")
                else:
                    print(f"❌ {element}: مفقود")
            
            detail_score = (len(found_detail_elements) / len(detail_elements)) * 100
            print(f"\n📊 نقاط صفحة التفاصيل: {detail_score:.1f}%")
            
            return {
                'success': True,
                'load_time': load_time,
                'score': detail_score,
                'found_elements': len(found_detail_elements),
                'total_elements': len(detail_elements),
                'ad_title': ad.title
            }
            
        else:
            print(f"❌ فشل في تحميل صفحة التفاصيل: {response.status_code}")
            return {'success': False, 'error': f'HTTP {response.status_code}'}
            
    except Exception as e:
        print(f"❌ خطأ في اختبار صفحة التفاصيل: {e}")
        return {'success': False, 'error': str(e)}

def test_responsive_design():
    """اختبار التصميم المتجاوب"""
    print(f"\n📱 اختبار التصميم المتجاوب")
    print("-" * 30)
    
    base_url = "http://127.0.0.1:8000"
    
    device_types = {
        'Mobile': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
        'Tablet': 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
        'Desktop': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    results = {}
    
    for device_type, user_agent in device_types.items():
        try:
            headers = {'User-Agent': user_agent}
            start_time = time.time()
            response = requests.get(base_url, headers=headers, timeout=10)
            load_time = time.time() - start_time
            
            if response.status_code == 200:
                content = response.text
                
                # فحص العناصر المتجاوبة
                responsive_elements = [
                    '@media',
                    'grid-template-columns',
                    'flex-direction: column',
                    'mobile-first',
                    'container-responsive'
                ]
                
                found_responsive = sum(1 for element in responsive_elements if element in content)
                responsive_score = (found_responsive / len(responsive_elements)) * 100
                
                results[device_type] = {
                    'load_time': load_time,
                    'responsive_score': responsive_score,
                    'status': 'success'
                }
                
                print(f"📱 {device_type}: {load_time:.2f}s - {responsive_score:.1f}%")
            else:
                print(f"❌ {device_type}: خطأ {response.status_code}")
                results[device_type] = {'status': 'error', 'code': response.status_code}
                
        except Exception:
            print(f"❌ {device_type}: خطأ في الاتصال")
            results[device_type] = {'status': 'error'}
    
    return results

def test_database_integration():
    """اختبار تكامل قاعدة البيانات"""
    print(f"\n💾 اختبار تكامل قاعدة البيانات")
    print("-" * 35)
    
    try:
        # إحصائيات قاعدة البيانات
        stats = {
            'users': User.objects.count(),
            'categories': Category.objects.filter(is_active=True).count(),
            'total_ads': Advertisement.objects.count(),
            'approved_ads': Advertisement.objects.filter(status='approved').count(),
            'featured_ads': Advertisement.objects.filter(status='approved', is_featured=True).count(),
            'urgent_ads': Advertisement.objects.filter(status='approved', is_urgent=True).count(),
        }
        
        print(f"📊 إحصائيات قاعدة البيانات:")
        for key, value in stats.items():
            print(f"   {key}: {value}")
        
        # فحص الأقسام مع الأيقونات
        categories_with_icons = Category.objects.filter(is_active=True, icon__isnull=False).exclude(icon='').count()
        print(f"   أقسام بأيقونات: {categories_with_icons}")
        
        # فحص الإعلانات مع الصور
        ads_with_images = Advertisement.objects.filter(status='approved', images__isnull=False).distinct().count()
        print(f"   إعلانات بصور: {ads_with_images}")
        
        return {
            'database_stats': stats,
            'categories_with_icons': categories_with_icons,
            'ads_with_images': ads_with_images
        }
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return {'error': str(e)}

def test_performance():
    """اختبار الأداء"""
    print(f"\n⚡ اختبار الأداء")
    print("-" * 20)
    
    base_url = "http://127.0.0.1:8000"
    
    load_times = []
    
    for i in range(5):
        try:
            start_time = time.time()
            response = requests.get(base_url, timeout=10)
            load_time = time.time() - start_time
            
            if response.status_code == 200:
                load_times.append(load_time)
                print(f"✅ اختبار {i+1}: {load_time:.2f} ثانية")
            else:
                print(f"❌ اختبار {i+1}: فشل")
                
        except Exception:
            print(f"❌ اختبار {i+1}: خطأ")
    
    if load_times:
        avg_time = sum(load_times) / len(load_times)
        min_time = min(load_times)
        max_time = max(load_times)
        
        print(f"\n📊 نتائج الأداء:")
        print(f"   متوسط وقت التحميل: {avg_time:.2f} ثانية")
        print(f"   أسرع تحميل: {min_time:.2f} ثانية")
        print(f"   أبطأ تحميل: {max_time:.2f} ثانية")
        
        # تقييم الأداء
        if avg_time < 0.5:
            print(f"🚀 الأداء ممتاز!")
        elif avg_time < 1:
            print(f"✅ الأداء جيد جداً")
        elif avg_time < 2:
            print(f"⚠️ الأداء مقبول")
        else:
            print(f"❌ الأداء يحتاج تحسين")
        
        return {
            'avg_time': avg_time,
            'min_time': min_time,
            'max_time': max_time,
            'tests_count': len(load_times)
        }
    
    return {'error': 'فشل في جميع الاختبارات'}

def generate_dubizzle_report():
    """إنشاء تقرير شامل لتصميم Dubizzle"""
    print("🧪 اختبار شامل لتصميم Dubizzle الجديد")
    print("=" * 70)
    
    # تشغيل جميع الاختبارات
    homepage_test = test_dubizzle_homepage()
    list_test = test_ads_list_page()
    detail_test = test_ad_detail_page()
    responsive_test = test_responsive_design()
    database_test = test_database_integration()
    performance_test = test_performance()
    
    print(f"\n📊 التقرير النهائي لتصميم Dubizzle")
    print("=" * 70)
    
    # حساب النتيجة الإجمالية
    scores = []
    
    # تقييم الصفحة الرئيسية
    if homepage_test.get('success'):
        overall_homepage = (homepage_test.get('elements_score', 0) + 
                           homepage_test.get('color_score', 0) + 
                           homepage_test.get('responsive_score', 0)) / 3
        scores.append(overall_homepage)
        print(f"🏠 الصفحة الرئيسية: {overall_homepage:.1f}%")
        print(f"   - العناصر: {homepage_test.get('elements_score', 0):.1f}%")
        print(f"   - الألوان: {homepage_test.get('color_score', 0):.1f}%")
        print(f"   - الاستجابة: {homepage_test.get('responsive_score', 0):.1f}%")
    
    # تقييم صفحة الإعلانات
    if list_test.get('success'):
        scores.append(list_test['score'])
        print(f"📋 صفحة الإعلانات: {list_test['score']:.1f}%")
    
    # تقييم صفحة التفاصيل
    if detail_test.get('success'):
        scores.append(detail_test['score'])
        print(f"🔍 صفحة التفاصيل: {detail_test['score']:.1f}%")
    
    # تقييم التصميم المتجاوب
    if responsive_test:
        responsive_scores = [result.get('responsive_score', 0) for result in responsive_test.values() 
                           if result.get('status') == 'success']
        if responsive_scores:
            avg_responsive = sum(responsive_scores) / len(responsive_scores)
            scores.append(avg_responsive)
            print(f"📱 التصميم المتجاوب: {avg_responsive:.1f}%")
    
    # تقييم قاعدة البيانات
    if 'database_stats' in database_test:
        db_score = 100  # إذا عملت فهي 100%
        scores.append(db_score)
        print(f"💾 قاعدة البيانات: {db_score}%")
        stats = database_test['database_stats']
        print(f"   - الإعلانات: {stats['approved_ads']}")
        print(f"   - الأقسام: {stats['categories']}")
        print(f"   - المستخدمين: {stats['users']}")
    
    # تقييم الأداء
    if 'avg_time' in performance_test:
        if performance_test['avg_time'] < 0.5:
            perf_score = 100
        elif performance_test['avg_time'] < 1:
            perf_score = 90
        elif performance_test['avg_time'] < 2:
            perf_score = 75
        else:
            perf_score = 50
        scores.append(perf_score)
        print(f"⚡ الأداء: {perf_score}% (متوسط: {performance_test['avg_time']:.2f}s)")
    
    # النتيجة الإجمالية
    if scores:
        overall_score = sum(scores) / len(scores)
        print(f"\n🏆 النتيجة الإجمالية: {overall_score:.1f}%")
        
        if overall_score >= 95:
            print(f"🎉 ممتاز! تصميم Dubizzle يعمل بشكل مثالي")
            print(f"✨ جميع الميزات مطبقة بنجاح")
        elif overall_score >= 85:
            print(f"🌟 ممتاز! تصميم Dubizzle محسن بشكل رائع")
            print(f"🔧 بعض التحسينات الطفيفة مطلوبة")
        elif overall_score >= 75:
            print(f"✅ جيد جداً! التصميم محسن بشكل جيد")
        else:
            print(f"⚠️ يحتاج تحسين! بعض الميزات تحتاج إصلاح")
    
    print(f"\n🌐 روابط الاختبار:")
    print(f"   الصفحة الرئيسية: http://127.0.0.1:8000/")
    print(f"   قائمة الإعلانات: http://127.0.0.1:8000/ads/")
    if detail_test.get('success'):
        print(f"   تفاصيل إعلان: http://127.0.0.1:8000/ads/1/")
    print("=" * 70)
    
    return overall_score if scores else 0

def main():
    """الدالة الرئيسية"""
    score = generate_dubizzle_report()
    return score >= 85

if __name__ == '__main__':
    main()

#!/usr/bin/env python
"""
Test the advertisement approval system
"""
import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

def test_new_ad_status():
    """اختبار أن الإعلانات الجديدة تبدأ بحالة pending"""
    print("🧪 اختبار نظام الموافقة على الإعلانات")
    print("=" * 50)
    
    try:
        from ads.models import Advertisement, Category, Notification
        from accounts.models import CustomUser
        
        # الحصول على مستخدم ومقسم
        user = CustomUser.objects.get(username='admin')
        category = Category.objects.first()
        
        # إنشاء إعلان جديد
        ad = Advertisement.objects.create(
            title='إعلان اختبار نظام الموافقة',
            description='هذا إعلان لاختبار نظام الموافقة التلقائي',
            category=category,
            user=user,
            price=1000,
            location='الرياض',
            phone='**********'
        )
        
        print(f"✅ تم إنشاء إعلان جديد: {ad.title}")
        print(f"📊 حالة الإعلان: {ad.status}")
        
        # التحقق من الحالة
        if ad.status == 'pending':
            print("✅ الإعلان بدأ بحالة 'pending' كما هو مطلوب")
        else:
            print(f"❌ الإعلان بدأ بحالة '{ad.status}' بدلاً من 'pending'")
            return False
        
        # التحقق من الإشعار
        notifications = Notification.objects.filter(
            user=user,
            advertisement=ad,
            type='general'
        )
        
        if notifications.exists():
            notification = notifications.first()
            print(f"✅ تم إنشاء إشعار: {notification.title}")
            print(f"📝 رسالة الإشعار: {notification.message}")
        else:
            print("❌ لم يتم إنشاء إشعار للمستخدم")
        
        # اختبار تغيير الحالة إلى approved
        print(f"\n🔄 اختبار تغيير الحالة إلى 'approved'...")
        ad.status = 'approved'
        ad.save()
        
        # التحقق من إشعار الموافقة
        approval_notifications = Notification.objects.filter(
            user=user,
            advertisement=ad,
            type='ad_approved'
        )
        
        if approval_notifications.exists():
            approval_notification = approval_notifications.first()
            print(f"✅ تم إنشاء إشعار الموافقة: {approval_notification.title}")
        else:
            print("❌ لم يتم إنشاء إشعار الموافقة")
        
        # اختبار تغيير الحالة إلى rejected
        print(f"\n🔄 اختبار تغيير الحالة إلى 'rejected'...")
        ad.status = 'rejected'
        ad.save()
        
        # التحقق من إشعار الرفض
        rejection_notifications = Notification.objects.filter(
            user=user,
            advertisement=ad,
            type='ad_rejected'
        )
        
        if rejection_notifications.exists():
            rejection_notification = rejection_notifications.first()
            print(f"✅ تم إنشاء إشعار الرفض: {rejection_notification.title}")
        else:
            print("❌ لم يتم إنشاء إشعار الرفض")
        
        # عرض جميع الإشعارات
        print(f"\n📋 جميع الإشعارات للمستخدم:")
        all_notifications = Notification.objects.filter(user=user).order_by('-created_at')
        for notif in all_notifications[:5]:  # أحدث 5 إشعارات
            read_status = "مقروء" if notif.is_read else "غير مقروء"
            print(f"   - {notif.title} ({notif.type}) - {read_status}")
        
        # تنظيف
        ad.delete()
        print(f"\n🗑️ تم حذف الإعلان التجريبي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pending_ads_view():
    """اختبار عرض الإعلانات المعلقة"""
    print(f"\n📊 اختبار عرض الإعلانات المعلقة")
    print("-" * 30)
    
    try:
        from ads.models import Advertisement
        
        # عد الإعلانات المعلقة
        pending_ads = Advertisement.objects.filter(status='pending')
        pending_count = pending_ads.count()
        
        print(f"📢 عدد الإعلانات المعلقة: {pending_count}")
        
        if pending_count > 0:
            print(f"📋 الإعلانات المعلقة:")
            for ad in pending_ads[:5]:  # أول 5 إعلانات
                print(f"   - {ad.title} (بواسطة: {ad.user.username})")
        
        # عد الإعلانات المعتمدة
        approved_ads = Advertisement.objects.filter(status='approved')
        approved_count = approved_ads.count()
        
        print(f"✅ عدد الإعلانات المعتمدة: {approved_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار العرض: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔔 اختبار نظام الموافقة والإشعارات")
    print("=" * 60)
    
    # اختبار إنشاء إعلان جديد
    if test_new_ad_status():
        # اختبار عرض الإعلانات المعلقة
        if test_pending_ads_view():
            print("\n" + "=" * 60)
            print("🎉 جميع الاختبارات نجحت!")
            print("=" * 60)
            
            print("\n✅ ما تم اختباره:")
            print("- الإعلانات الجديدة تبدأ بحالة 'pending'")
            print("- إرسال إشعار عند إنشاء إعلان")
            print("- إرسال إشعار عند الموافقة")
            print("- إرسال إشعار عند الرفض")
            print("- عرض الإعلانات المعلقة")
            
            print("\n🌐 الروابط المتاحة:")
            print("- الإعلانات المعلقة: http://127.0.0.1:8000/admin-panel/pending-ads/")
            print("- الإشعارات: http://127.0.0.1:8000/ads/notifications/")
            print("- لوحة الإدارة: http://127.0.0.1:8000/admin-panel/")
    
    return True

if __name__ == '__main__':
    main()

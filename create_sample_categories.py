#!/usr/bin/env python
"""
إنشاء بيانات تجريبية للأقسام مع الأيقونات
"""
import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

from ads.models import Category

def create_sample_categories():
    """إنشاء أقسام تجريبية مع أيقونات"""
    
    categories_data = [
        {
            'name': 'سيارات ومركبات',
            'description': 'سيارات جديدة ومستعملة، دراجات نارية، قطع غيار، وجميع أنواع المركبات',
            'icon': 'fas fa-car'
        },
        {
            'name': 'عقارات',
            'description': 'شقق، فلل، أراضي، مكاتب تجارية للبيع والإيجار',
            'icon': 'fas fa-home'
        },
        {
            'name': 'إلكترونيات',
            'description': 'هواتف ذكية، أجهزة كمبيوتر، أجهزة منزلية، وجميع الإلكترونيات',
            'icon': 'fas fa-laptop'
        },
        {
            'name': 'وظائف',
            'description': 'فرص عمل في جميع التخصصات والمجالات',
            'icon': 'fas fa-briefcase'
        },
        {
            'name': 'أزياء وموضة',
            'description': 'ملابس، أحذية، حقائب، إكسسوارات للرجال والنساء',
            'icon': 'fas fa-tshirt'
        },
        {
            'name': 'أثاث ومنزل',
            'description': 'أثاث، ديكور، أدوات منزلية، وجميع مستلزمات المنزل',
            'icon': 'fas fa-couch'
        },
        {
            'name': 'ألعاب ورياضة',
            'description': 'ألعاب فيديو، معدات رياضية، ألعاب أطفال',
            'icon': 'fas fa-gamepad'
        },
        {
            'name': 'خدمات',
            'description': 'خدمات متنوعة ومهنية في جميع المجالات',
            'icon': 'fas fa-tools'
        },
        {
            'name': 'كتب وتعليم',
            'description': 'كتب، مواد تعليمية، دورات تدريبية',
            'icon': 'fas fa-book'
        },
        {
            'name': 'حيوانات أليفة',
            'description': 'حيوانات أليفة، مستلزمات الحيوانات، خدمات بيطرية',
            'icon': 'fas fa-paw'
        },
        {
            'name': 'طعام ومشروبات',
            'description': 'مطاعم، كافيهات، خدمات توصيل طعام',
            'icon': 'fas fa-utensils'
        },
        {
            'name': 'صحة وجمال',
            'description': 'منتجات العناية، مستحضرات تجميل، خدمات صحية',
            'icon': 'fas fa-heart'
        }
    ]
    
    print("🔧 إنشاء الأقسام التجريبية...")
    
    created_count = 0
    updated_count = 0
    
    for category_data in categories_data:
        category, created = Category.objects.get_or_create(
            name=category_data['name'],
            defaults={
                'description': category_data['description'],
                'icon': category_data['icon'],
                'is_active': True
            }
        )
        
        if created:
            created_count += 1
            print(f"✅ تم إنشاء القسم: {category.name}")
        else:
            # تحديث البيانات الموجودة
            category.description = category_data['description']
            category.icon = category_data['icon']
            category.is_active = True
            category.save()
            updated_count += 1
            print(f"🔄 تم تحديث القسم: {category.name}")
    
    print(f"\n📊 النتائج:")
    print(f"   ✅ أقسام جديدة: {created_count}")
    print(f"   🔄 أقسام محدثة: {updated_count}")
    print(f"   📁 إجمالي الأقسام: {Category.objects.filter(is_active=True).count()}")
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء بيانات تجريبية للأقسام")
    print("=" * 50)
    
    try:
        success = create_sample_categories()
        if success:
            print("\n🎉 تم إنشاء الأقسام بنجاح!")
            print("🌐 يمكنك الآن زيارة الصفحة الرئيسية لرؤية الأقسام الجديدة")
        else:
            print("\n❌ فشل في إنشاء الأقسام")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        return False
    
    return True

if __name__ == '__main__':
    main()

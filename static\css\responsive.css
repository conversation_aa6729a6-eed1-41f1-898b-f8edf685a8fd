/**
 * نظام التصميم المتجاوب الشامل
 * Comprehensive Responsive Design System
 * Mobile-First Approach with Advanced Breakpoints
 */

/* ===== CSS VARIABLES ===== */
:root {
    /* Breakpoints */
    --mobile-small: 320px;
    --mobile-medium: 375px;
    --mobile-large: 425px;
    --tablet-small: 768px;
    --tablet-large: 1024px;
    --desktop-small: 1200px;
    --desktop-large: 1440px;
    --desktop-xl: 1920px;
    
    /* Spacing System */
    --spacing-xs: 0.25rem;    /* 4px */
    --spacing-sm: 0.5rem;     /* 8px */
    --spacing-md: 1rem;       /* 16px */
    --spacing-lg: 1.5rem;     /* 24px */
    --spacing-xl: 2rem;       /* 32px */
    --spacing-2xl: 3rem;      /* 48px */
    --spacing-3xl: 4rem;      /* 64px */
    
    /* Typography Scale */
    --text-xs: 0.75rem;       /* 12px */
    --text-sm: 0.875rem;      /* 14px */
    --text-base: 1rem;        /* 16px */
    --text-lg: 1.125rem;      /* 18px */
    --text-xl: 1.25rem;       /* 20px */
    --text-2xl: 1.5rem;       /* 24px */
    --text-3xl: 1.875rem;     /* 30px */
    --text-4xl: 2.25rem;      /* 36px */
    --text-5xl: 3rem;         /* 48px */
    
    /* Touch Target Sizes */
    --touch-target-min: 44px;
    --touch-target-comfortable: 48px;
    --touch-target-large: 56px;
    
    /* Container Widths */
    --container-sm: 540px;
    --container-md: 720px;
    --container-lg: 960px;
    --container-xl: 1140px;
    --container-xxl: 1320px;
}

/* ===== BASE STYLES (Mobile First) ===== */
* {
    box-sizing: border-box;
}

html {
    font-size: 16px;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    font-size: var(--text-base);
    overflow-x: hidden;
}

/* ===== CONTAINER SYSTEM ===== */
.container-responsive {
    width: 100%;
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
    margin-left: auto;
    margin-right: auto;
}

/* ===== GRID SYSTEM ===== */
.grid-responsive {
    display: grid;
    gap: var(--spacing-md);
    width: 100%;
}

.grid-1 { grid-template-columns: 1fr; }
.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

/* ===== FLEXBOX UTILITIES ===== */
.flex-responsive {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-center { justify-content: center; align-items: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }
.flex-start { justify-content: flex-start; }
.flex-end { justify-content: flex-end; }

/* ===== TYPOGRAPHY RESPONSIVE ===== */
.text-responsive {
    font-size: var(--text-sm);
    line-height: 1.5;
}

.heading-responsive {
    font-size: var(--text-xl);
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: var(--spacing-md);
}

.title-responsive {
    font-size: var(--text-2xl);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-lg);
}

/* ===== BUTTON SYSTEM ===== */
.btn-responsive {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: var(--touch-target-min);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--text-sm);
    font-weight: 500;
    text-decoration: none;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.btn-responsive:hover,
.btn-responsive:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-responsive:active {
    transform: translateY(0);
}

/* Button Sizes */
.btn-sm {
    min-height: 36px;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--text-xs);
}

.btn-lg {
    min-height: var(--touch-target-large);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--text-lg);
}

/* ===== CARD SYSTEM ===== */
.card-responsive {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.card-responsive:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.card-header {
    padding: var(--spacing-md);
    border-bottom: 1px solid #e5e7eb;
}

.card-body {
    padding: var(--spacing-md);
}

.card-footer {
    padding: var(--spacing-md);
    border-top: 1px solid #e5e7eb;
    background: #f9fafb;
}

/* ===== IMAGE SYSTEM ===== */
.img-responsive {
    max-width: 100%;
    height: auto;
    display: block;
}

.img-cover {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.img-contain {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* ===== NAVIGATION RESPONSIVE ===== */
.nav-responsive {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.nav-item {
    min-height: var(--touch-target-min);
    display: flex;
    align-items: center;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.2s ease;
    min-height: var(--touch-target-min);
    width: 100%;
}

/* ===== FORM ELEMENTS ===== */
.form-responsive {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.form-control {
    min-height: var(--touch-target-min);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--text-base);
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ===== UTILITY CLASSES ===== */
.hidden-mobile { display: none; }
.visible-mobile { display: block; }
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* Spacing Utilities */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-md); }
.m-4 { margin: var(--spacing-lg); }
.m-5 { margin: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

/* ===== MOBILE SMALL (320px+) ===== */
@media (min-width: 320px) {
    .container-responsive {
        max-width: 100%;
    }
    
    .text-responsive {
        font-size: var(--text-sm);
    }
    
    .heading-responsive {
        font-size: var(--text-lg);
    }
    
    .title-responsive {
        font-size: var(--text-xl);
    }
}

/* ===== MOBILE MEDIUM (375px+) ===== */
@media (min-width: 375px) {
    .container-responsive {
        padding-left: var(--spacing-lg);
        padding-right: var(--spacing-lg);
    }
    
    .grid-responsive {
        gap: var(--spacing-lg);
    }
    
    .text-responsive {
        font-size: var(--text-base);
    }
}

/* ===== MOBILE LARGE (425px+) ===== */
@media (min-width: 425px) {
    .heading-responsive {
        font-size: var(--text-xl);
    }
    
    .title-responsive {
        font-size: var(--text-2xl);
    }
    
    .btn-responsive {
        font-size: var(--text-base);
    }
}

/* ===== TABLET SMALL (768px+) ===== */
@media (min-width: 768px) {
    .container-responsive {
        max-width: var(--container-md);
        padding-left: var(--spacing-xl);
        padding-right: var(--spacing-xl);
    }
    
    .grid-responsive {
        gap: var(--spacing-xl);
    }
    
    .nav-responsive {
        flex-direction: row;
        align-items: center;
    }
    
    .hidden-mobile {
        display: block;
    }
    
    .visible-mobile {
        display: none;
    }
    
    .text-responsive {
        font-size: var(--text-lg);
    }
    
    .heading-responsive {
        font-size: var(--text-2xl);
    }
    
    .title-responsive {
        font-size: var(--text-3xl);
    }
    
    .form-responsive {
        flex-direction: row;
        align-items: end;
    }
    
    .form-group {
        flex: 1;
    }
}

/* ===== TABLET LARGE (1024px+) ===== */
@media (min-width: 1024px) {
    .container-responsive {
        max-width: var(--container-lg);
    }
    
    .grid-responsive {
        gap: var(--spacing-2xl);
    }
    
    .heading-responsive {
        font-size: var(--text-3xl);
    }
    
    .title-responsive {
        font-size: var(--text-4xl);
    }
}

/* ===== DESKTOP SMALL (1200px+) ===== */
@media (min-width: 1200px) {
    .container-responsive {
        max-width: var(--container-xl);
    }
    
    .title-responsive {
        font-size: var(--text-5xl);
    }
}

/* ===== DESKTOP LARGE (1440px+) ===== */
@media (min-width: 1440px) {
    .container-responsive {
        max-width: var(--container-xxl);
    }
    
    .grid-responsive {
        gap: var(--spacing-3xl);
    }
}

/* ===== DESKTOP XL (1920px+) ===== */
@media (min-width: 1920px) {
    .container-responsive {
        max-width: 1600px;
    }
    
    html {
        font-size: 18px;
    }
}

{% extends 'base.html' %}

{% block title %}إعلاناتي{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-bullhorn me-2"></i>إعلاناتي</h2>
    <a href="{% url 'ads:create' %}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>إضافة إعلان جديد
    </a>
</div>

{% if ads %}
    <div class="row">
        {% for ad in ads %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                {% if ad.images.first %}
                    <img src="{{ ad.images.first.image.url }}" class="card-img-top" style="height: 200px; object-fit: cover;" alt="{{ ad.title }}">
                {% else %}
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                        <i class="fas fa-image fa-3x text-muted"></i>
                    </div>
                {% endif %}
                
                <div class="card-body d-flex flex-column">
                    <h5 class="card-title">{{ ad.title }}</h5>
                    <p class="card-text flex-grow-1">{{ ad.description|truncatewords:15 }}</p>
                    
                    <div class="mb-2">
                        {% if ad.status == 'approved' %}
                            <span class="badge bg-success">موافق عليه</span>
                        {% elif ad.status == 'pending' %}
                            <span class="badge bg-warning">في الانتظار</span>
                        {% elif ad.status == 'rejected' %}
                            <span class="badge bg-danger">مرفوض</span>
                        {% elif ad.status == 'expired' %}
                            <span class="badge bg-secondary">منتهي الصلاحية</span>
                        {% endif %}
                        
                        {% if ad.is_featured %}
                            <span class="badge bg-info">مميز</span>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-eye me-1"></i>{{ ad.views_count }} مشاهدة
                        </small>
                        <small class="text-muted">{{ ad.created_at|date:"Y/m/d" }}</small>
                    </div>
                    
                    {% if ad.price %}
                        <div class="mt-2">
                            <span class="text-primary fw-bold">{{ ad.price }} ريال</span>
                        </div>
                    {% endif %}
                </div>
                
                <div class="card-footer bg-transparent">
                    <div class="btn-group w-100" role="group">
                        <a href="{{ ad.get_absolute_url }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>عرض
                        </a>
                        <a href="{% url 'ads:edit' ad.pk %}" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-edit me-1"></i>تعديل
                        </a>
                        <a href="{% url 'ads:delete' ad.pk %}" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-trash me-1"></i>حذف
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="Page navigation">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1">الأولى</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                </li>
            {% endif %}
            
            <li class="page-item active">
                <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
            </li>
            
            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
    
{% else %}
    <div class="text-center py-5">
        <i class="fas fa-bullhorn fa-5x text-muted mb-3"></i>
        <h4>لا توجد إعلانات</h4>
        <p class="text-muted">لم تقم بإضافة أي إعلانات بعد</p>
        <a href="{% url 'ads:create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>أضف إعلانك الأول
        </a>
    </div>
{% endif %}
{% endblock %}

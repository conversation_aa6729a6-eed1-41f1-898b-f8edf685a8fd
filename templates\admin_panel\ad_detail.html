{% extends 'admin_panel/base.html' %}
{% load static %}

{% block title %}تفاصيل الإعلان - {{ object.title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>
                        <i class="fas fa-bullhorn me-2"></i>تفاصيل الإعلان
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'admin_panel:dashboard' %}">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'admin_panel:ad_list' %}">الإعلانات</a></li>
                            <li class="breadcrumb-item active">{{ object.title|truncatechars:30 }}</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'admin_panel:ad_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>العودة للقائمة
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- تفاصيل الإعلان -->
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>معلومات الإعلان
                            </h5>
                            <div>
                                {% if object.status == 'approved' %}
                                    <span class="badge bg-success">معتمد</span>
                                {% elif object.status == 'pending' %}
                                    <span class="badge bg-warning">في الانتظار</span>
                                {% elif object.status == 'rejected' %}
                                    <span class="badge bg-danger">مرفوض</span>
                                {% elif object.status == 'expired' %}
                                    <span class="badge bg-secondary">منتهي الصلاحية</span>
                                {% endif %}
                                
                                {% if object.is_featured %}
                                    <span class="badge bg-primary">مميز</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- صور الإعلان -->
                            {% if object.images.exists %}
                            <div class="mb-4">
                                <h6>صور الإعلان:</h6>
                                <div class="row">
                                    {% for image in object.images.all %}
                                    <div class="col-md-4 mb-3">
                                        <img src="{{ image.image.url }}" class="img-fluid rounded" alt="صورة الإعلان" style="max-height: 200px; object-fit: cover;">
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% else %}
                            <div class="mb-4 text-center">
                                <div class="bg-light p-4 rounded">
                                    <i class="fas fa-image fa-3x text-muted mb-2"></i>
                                    <p class="text-muted">لا توجد صور لهذا الإعلان</p>
                                </div>
                            </div>
                            {% endif %}

                            <!-- معلومات أساسية -->
                            <table class="table table-borderless">
                                <tr>
                                    <td width="150"><strong>العنوان:</strong></td>
                                    <td>{{ object.title }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الوصف:</strong></td>
                                    <td>{{ object.description|linebreaks }}</td>
                                </tr>
                                <tr>
                                    <td><strong>القسم:</strong></td>
                                    <td>
                                        <span class="badge bg-info">{{ object.category.name }}</span>
                                    </td>
                                </tr>
                                {% if object.price %}
                                <tr>
                                    <td><strong>السعر:</strong></td>
                                    <td class="text-success fw-bold">{{ object.price|floatformat:0 }} ريال</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td><strong>الموقع:</strong></td>
                                    <td>
                                        <i class="fas fa-map-marker-alt text-muted me-1"></i>
                                        {{ object.location|default:"غير محدد" }}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>رقم الهاتف:</strong></td>
                                    <td>
                                        <i class="fas fa-phone text-muted me-1"></i>
                                        {{ object.phone|default:"غير محدد" }}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ الإنشاء:</strong></td>
                                    <td>
                                        <i class="fas fa-calendar text-muted me-1"></i>
                                        {{ object.created_at|date:"Y-m-d H:i" }}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>آخر تحديث:</strong></td>
                                    <td>
                                        <i class="fas fa-clock text-muted me-1"></i>
                                        {{ object.updated_at|date:"Y-m-d H:i" }}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>عدد المشاهدات:</strong></td>
                                    <td>
                                        <i class="fas fa-eye text-muted me-1"></i>
                                        {{ object.views_count }}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- معلومات المستخدم والإجراءات -->
                <div class="col-lg-4 mb-4">
                    <!-- معلومات المستخدم -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-user me-2"></i>معلومات المعلن
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                {% if object.user.profile_picture %}
                                    <img src="{{ object.user.profile_picture.url }}" class="rounded-circle" width="60" height="60" alt="صورة المستخدم">
                                {% else %}
                                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                        <i class="fas fa-user fa-2x text-white"></i>
                                    </div>
                                {% endif %}
                            </div>
                            
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>الاسم:</strong></td>
                                    <td>{{ object.user.get_full_name|default:object.user.username }}</td>
                                </tr>
                                <tr>
                                    <td><strong>البريد:</strong></td>
                                    <td>{{ object.user.email|default:"غير محدد" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الهاتف:</strong></td>
                                    <td>{{ object.user.phone|default:"غير محدد" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>إعلاناته:</strong></td>
                                    <td>{{ object.user.advertisements.count }}</td>
                                </tr>
                                <tr>
                                    <td><strong>حالة الحساب:</strong></td>
                                    <td>
                                        {% if object.user.is_active %}
                                            <span class="badge bg-success">نشط</span>
                                        {% else %}
                                            <span class="badge bg-danger">غير نشط</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                            
                            <div class="d-grid">
                                <a href="{% url 'admin_panel:user_detail' object.user.pk %}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>عرض الملف الشخصي
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- إجراءات الإدارة -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-cogs me-2"></i>إجراءات الإدارة
                            </h6>
                        </div>
                        <div class="card-body">
                            <!-- أزرار الموافقة والرفض -->
                            {% if object.status == 'pending' %}
                            <div class="row mb-3">
                                <div class="col-6">
                                    <a href="{% url 'admin_panel:approve_advertisement' object.id %}" class="btn btn-success w-100">
                                        <i class="fas fa-check me-1"></i>اعتماد
                                    </a>
                                </div>
                                <div class="col-6">
                                    <a href="{% url 'admin_panel:reject_advertisement' object.id %}" class="btn btn-danger w-100">
                                        <i class="fas fa-times me-1"></i>رفض
                                    </a>
                                </div>
                            </div>
                            {% endif %}

                            <!-- أزرار التمييز والعاجل -->
                            <div class="row mb-3">
                                <div class="col-6">
                                    <button type="button" class="btn {% if object.is_featured %}btn-warning{% else %}btn-outline-warning{% endif %} w-100"
                                            onclick="toggleFeatured({{ object.id }})">
                                        <i class="fas fa-star me-1"></i>
                                        {% if object.is_featured %}مميز{% else %}تمييز{% endif %}
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button type="button" class="btn {% if object.is_urgent %}btn-danger{% else %}btn-outline-danger{% endif %} w-100"
                                            onclick="toggleUrgent({{ object.id }})">
                                        <i class="fas fa-exclamation me-1"></i>
                                        {% if object.is_urgent %}عاجل{% else %}جعل عاجل{% endif %}
                                    </button>
                                </div>
                            </div>

                            <!-- أزرار التعديل والحذف -->
                            <div class="row mb-3">
                                <div class="col-6">
                                    <a href="{% url 'admin_panel:ad_edit' object.pk %}" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-edit me-1"></i>تعديل
                                    </a>
                                </div>
                                <div class="col-6">
                                    <a href="{% url 'admin_panel:delete_advertisement' object.id %}" class="btn btn-outline-danger w-100">
                                        <i class="fas fa-trash me-1"></i>حذف
                                    </a>
                                </div>
                            </div>

                            <a href="{% url 'ads:detail' object.pk %}" class="btn btn-outline-info w-100 mb-2" target="_blank">
                                <i class="fas fa-external-link-alt me-1"></i>معاينة في الموقع
                            </a>

                            {% if object.status == 'approved' %}
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-1"></i>
                                هذا الإعلان معتمد ومتاح للجمهور
                            </div>
                            {% elif object.status == 'rejected' %}
                            <div class="alert alert-danger">
                                <i class="fas fa-times-circle me-1"></i>
                                هذا الإعلان مرفوض
                            </div>
                            {% elif object.status == 'pending' %}
                            <div class="alert alert-warning">
                                <i class="fas fa-clock me-1"></i>
                                هذا الإعلان في انتظار المراجعة
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- التقارير إن وجدت -->
            {% if object.reports.exists %}
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-flag me-2 text-danger"></i>التقارير المرسلة ({{ object.reports.count }})
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>المبلغ</th>
                                            <th>السبب</th>
                                            <th>الوصف</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for report in object.reports.all %}
                                        <tr>
                                            <td>{{ report.user.get_full_name|default:report.user.username }}</td>
                                            <td>
                                                <span class="badge bg-warning">{{ report.get_reason_display }}</span>
                                            </td>
                                            <td>{{ report.description|truncatechars:50|default:"لا يوجد وصف" }}</td>
                                            <td>{{ report.created_at|date:"Y-m-d H:i" }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function toggleFeatured(adId) {
    fetch(`/admin-panel/ads/${adId}/toggle-featured/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}

function toggleUrgent(adId) {
    fetch(`/admin-panel/ads/${adId}/toggle-urgent/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}
</script>
{% endblock %}

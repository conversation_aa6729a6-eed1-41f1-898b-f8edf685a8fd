#!/usr/bin/env python
"""
اختبار سريع للصفحة الرئيسية
"""
import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

def test_home_view():
    """اختبار view الصفحة الرئيسية"""
    print("🧪 اختبار view الصفحة الرئيسية")
    print("=" * 40)
    
    try:
        from django.test import RequestFactory
        from classified_ads_site.views import HomeView
        from django.contrib.auth.models import AnonymousUser
        
        # إنشاء request تجريبي
        factory = RequestFactory()
        request = factory.get('/')
        request.user = AnonymousUser()
        
        # اختبار الـ view
        view = HomeView()
        view.setup(request)
        
        # الحصول على context
        context = view.get_context_data()
        
        print("✅ View يعمل بنجاح")
        print(f"📊 البيانات المتاحة:")
        print(f"   - الأقسام: {len(context.get('categories', []))}")
        print(f"   - الإعلانات المميزة: {len(context.get('featured_ads', []))}")
        print(f"   - أحدث الإعلانات: {len(context.get('latest_ads', []))}")
        print(f"   - الإحصائيات: {context.get('stats', {})}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الـ view: {e}")
        return False

def test_template_rendering():
    """اختبار عرض القالب"""
    print(f"\n🎨 اختبار عرض القالب")
    print("-" * 30)
    
    try:
        from django.template.loader import get_template
        from django.template import Context
        
        # محاولة تحميل القالب
        template = get_template('home_new.html')
        print("✅ القالب home_new.html موجود")
        
        # اختبار القالب الأساسي
        base_template = get_template('base_landing.html')
        print("✅ القالب base_landing.html موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في القالب: {e}")
        return False

def test_urls():
    """اختبار URLs"""
    print(f"\n🔗 اختبار URLs")
    print("-" * 20)
    
    try:
        from django.urls import reverse
        
        # اختبار الروابط المطلوبة
        urls_to_test = [
            ('home', 'الصفحة الرئيسية'),
            ('ads:list', 'قائمة الإعلانات'),
            ('ads:create', 'إنشاء إعلان'),
            ('accounts:login', 'تسجيل الدخول'),
            ('accounts:register', 'التسجيل'),
            ('accounts:my_ads', 'إعلاناتي'),
        ]
        
        working_urls = []
        broken_urls = []
        
        for url_name, description in urls_to_test:
            try:
                url = reverse(url_name)
                working_urls.append((url_name, url))
                print(f"✅ {description}: {url}")
            except Exception as e:
                broken_urls.append((url_name, str(e)))
                print(f"❌ {description}: خطأ")
        
        return len(broken_urls) == 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار URLs: {e}")
        return False

def test_models():
    """اختبار النماذج"""
    print(f"\n💾 اختبار النماذج")
    print("-" * 20)
    
    try:
        from ads.models import Advertisement, Category
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # إحصائيات النماذج
        print(f"👥 المستخدمون: {User.objects.count()}")
        print(f"📂 الأقسام: {Category.objects.count()}")
        print(f"📢 الإعلانات: {Advertisement.objects.count()}")
        print(f"⭐ الإعلانات المميزة: {Advertisement.objects.filter(is_featured=True).count()}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في النماذج: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح واختبار الصفحة الرئيسية")
    print("=" * 50)
    
    tests = [
        test_home_view,
        test_template_rendering,
        test_urls,
        test_models
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test in tests:
        if test():
            passed_tests += 1
    
    print(f"\n📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت! الصفحة الرئيسية جاهزة")
        print("🌐 يمكنك الآن زيارة: http://127.0.0.1:8000/")
    else:
        print("⚠️ بعض الاختبارات فشلت، يرجى مراجعة الأخطاء أعلاه")
    
    return passed_tests == total_tests

if __name__ == '__main__':
    main()

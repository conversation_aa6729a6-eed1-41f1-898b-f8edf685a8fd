#!/usr/bin/env python
"""
Script to connect Django app to MySQL database
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

from django.db import connection
from django.core.management import execute_from_command_line

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("🔗 اختبار الاتصال بقاعدة البيانات MySQL...")
    
    try:
        with connection.cursor() as cursor:
            # اختبار الاتصال
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
            if result:
                print("✅ تم الاتصال بقاعدة البيانات بنجاح!")
                
                # عرض معلومات قاعدة البيانات
                cursor.execute("SELECT DATABASE()")
                db_name = cursor.fetchone()[0]
                print(f"📊 قاعدة البيانات الحالية: {db_name}")
                
                # عرض الجداول
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                print(f"📁 عدد الجداول: {len(tables)}")
                
                return True
            else:
                print("❌ فشل في الاتصال بقاعدة البيانات")
                return False
                
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def verify_tables():
    """التحقق من الجداول"""
    print("\n🔍 التحقق من الجداول...")
    
    try:
        with connection.cursor() as cursor:
            # فحص الجداول الأساسية
            essential_tables = [
                'accounts_customuser',
                'ads_category', 
                'ads_advertisement',
                'ads_adimage',
                'ads_report'
            ]
            
            print("📋 الجداول الأساسية:")
            for table_name in essential_tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                    count = cursor.fetchone()[0]
                    print(f"   ✅ {table_name}: {count} سجل")
                except Exception as e:
                    print(f"   ❌ {table_name}: غير موجود - {e}")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في فحص الجداول: {e}")
        return False

def test_models():
    """اختبار النماذج"""
    print("\n🧪 اختبار النماذج...")
    
    try:
        from accounts.models import CustomUser
        from ads.models import Category, Advertisement
        
        # اختبار نموذج المستخدمين
        users_count = CustomUser.objects.count()
        print(f"👥 المستخدمون: {users_count}")
        
        # اختبار نموذج الأقسام
        categories_count = Category.objects.count()
        print(f"📂 الأقسام: {categories_count}")
        
        # اختبار نموذج الإعلانات
        ads_count = Advertisement.objects.count()
        print(f"📢 الإعلانات: {ads_count}")
        
        # عرض المستخدم الإداري
        try:
            admin = CustomUser.objects.get(username='admin')
            print(f"👑 المدير: {admin.username} ({admin.email})")
        except CustomUser.DoesNotExist:
            print("❌ المستخدم الإداري غير موجود")
        
        # عرض الأقسام
        print("\n📋 الأقسام المتاحة:")
        for category in Category.objects.all()[:5]:  # أول 5 أقسام
            print(f"   - {category.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النماذج: {e}")
        return False

def update_user_passwords():
    """تحديث كلمات مرور المستخدمين"""
    print("\n🔐 تحديث كلمات المرور...")
    
    try:
        from accounts.models import CustomUser
        
        # تحديث كلمة مرور المدير
        try:
            admin = CustomUser.objects.get(username='admin')
            admin.set_password('admin123')
            admin.save()
            print("✅ تم تحديث كلمة مرور المدير")
        except CustomUser.DoesNotExist:
            print("❌ المستخدم الإداري غير موجود")
        
        # تحديث كلمات مرور المستخدمين التجريبيين
        test_users = ['user1', 'user2', 'user3']
        for username in test_users:
            try:
                user = CustomUser.objects.get(username=username)
                user.set_password('password123')
                user.save()
                print(f"✅ تم تحديث كلمة مرور {username}")
            except CustomUser.DoesNotExist:
                print(f"❌ المستخدم {username} غير موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث كلمات المرور: {e}")
        return False

def test_authentication():
    """اختبار المصادقة"""
    print("\n🔑 اختبار المصادقة...")
    
    try:
        from django.contrib.auth import authenticate
        
        # اختبار تسجيل دخول المدير
        admin_user = authenticate(username='admin', password='admin123')
        if admin_user:
            print("✅ تسجيل دخول المدير يعمل")
        else:
            print("❌ فشل تسجيل دخول المدير")
        
        # اختبار تسجيل دخول مستخدم تجريبي
        test_user = authenticate(username='user1', password='password123')
        if test_user:
            print("✅ تسجيل دخول المستخدم التجريبي يعمل")
        else:
            print("❌ فشل تسجيل دخول المستخدم التجريبي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المصادقة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 ربط التطبيق بقاعدة بيانات MySQL")
    print("=" * 60)
    
    # 1. اختبار الاتصال
    if not test_database_connection():
        print("\n❌ فشل في الاتصال بقاعدة البيانات")
        print("تأكد من:")
        print("- تشغيل خدمة MySQL")
        print("- استيراد ملف classified_ads_database.sql")
        print("- صحة إعدادات قاعدة البيانات")
        return False
    
    # 2. التحقق من الجداول
    if not verify_tables():
        print("\n❌ مشكلة في الجداول")
        return False
    
    # 3. اختبار النماذج
    if not test_models():
        print("\n❌ مشكلة في النماذج")
        return False
    
    # 4. تحديث كلمات المرور
    if not update_user_passwords():
        print("\n❌ مشكلة في تحديث كلمات المرور")
        return False
    
    # 5. اختبار المصادقة
    if not test_authentication():
        print("\n❌ مشكلة في المصادقة")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 تم ربط التطبيق بقاعدة البيانات بنجاح!")
    print("=" * 60)
    
    print("\n📊 معلومات الاتصال:")
    print("- قاعدة البيانات: classified_ads_db")
    print("- النوع: MySQL")
    print("- الخادم: localhost:3306")
    print("- المستخدم: root")
    
    print("\n👥 المستخدمون المتاحون:")
    print("- المدير: admin / admin123")
    print("- مستخدم تجريبي: user1 / password123")
    print("- مستخدم تجريبي: user2 / password123")
    print("- مستخدم تجريبي: user3 / password123")
    
    print("\n🌐 لتشغيل الموقع:")
    print("python manage.py runserver")
    
    print("\n🔗 الروابط:")
    print("- الموقع الرئيسي: http://127.0.0.1:8000/")
    print("- لوحة الإدارة: http://127.0.0.1:8000/admin-panel/")
    print("- إدارة Django: http://127.0.0.1:8000/admin/")
    
    return True

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ تم إلغاء العملية بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        print("\nتحقق من:")
        print("- تشغيل خدمة MySQL في XAMPP")
        print("- استيراد قاعدة البيانات")
        print("- إعدادات Django")

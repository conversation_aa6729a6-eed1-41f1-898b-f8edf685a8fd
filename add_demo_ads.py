#!/usr/bin/env python
"""
إضافة إعلانات تجريبية لعرض كيفية عمل الموقع
"""
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

def add_demo_ads():
    """إضافة إعلانات تجريبية"""
    print("📢 إضافة إعلانات تجريبية للعرض")
    print("=" * 40)
    
    try:
        from ads.models import Advertisement, Category
        from accounts.models import CustomUser
        
        # استخدام المستخدم الإداري
        admin_user = CustomUser.objects.get(username='admin')
        
        # الحصول على الأقسام
        categories = {
            'عقارات': Category.objects.get(name='عقارات'),
            'سيارات': Category.objects.get(name='سيارات'),
            'وظائف': Category.objects.get(name='وظائف'),
            'إلكترونيات': Category.objects.get(name='إلكترونيات'),
        }
        
        # إعلانات تجريبية
        demo_ads = [
            {
                'title': 'شقة للبيع في الرياض',
                'description': 'شقة مميزة للبيع في حي النرجس، 3 غرف نوم، صالة، مطبخ، 2 حمام. مساحة 120 متر مربع.',
                'category': categories['عقارات'],
                'price': 450000,
                'location': 'الرياض - حي النرجس',
                'phone': '0501234567',
                'is_featured': True
            },
            {
                'title': 'سيارة تويوتا كامري 2020',
                'description': 'سيارة تويوتا كامري موديل 2020، لون أبيض، ماشية 45000 كم، حالة ممتازة.',
                'category': categories['سيارات'],
                'price': 85000,
                'location': 'جدة',
                'phone': '0507654321',
                'is_featured': True
            },
            {
                'title': 'مطلوب مطور ويب',
                'description': 'مطلوب مطور ويب خبرة لا تقل عن سنتين، إتقان Python و Django. راتب مجزي.',
                'category': categories['وظائف'],
                'price': None,
                'location': 'الرياض',
                'phone': '0551234567',
                'is_featured': False
            },
            {
                'title': 'لابتوب ديل للبيع',
                'description': 'لابتوب ديل Inspiron 15، معالج Intel i5، ذاكرة 8GB، قرص صلب 512GB SSD.',
                'category': categories['إلكترونيات'],
                'price': 2500,
                'location': 'الدمام',
                'phone': '0561234567',
                'is_featured': False
            }
        ]
        
        # إنشاء الإعلانات
        created_count = 0
        for ad_data in demo_ads:
            ad = Advertisement.objects.create(
                title=ad_data['title'],
                description=ad_data['description'],
                category=ad_data['category'],
                user=admin_user,
                price=ad_data['price'],
                location=ad_data['location'],
                phone=ad_data['phone'],
                is_featured=ad_data['is_featured'],
                status='approved'
            )
            created_count += 1
            print(f"✅ تم إنشاء: {ad.title}")
        
        print(f"\n🎉 تم إنشاء {created_count} إعلان تجريبي")
        
        # عرض الحالة الجديدة
        total_ads = Advertisement.objects.count()
        featured_ads = Advertisement.objects.filter(is_featured=True).count()
        
        print(f"\n📊 الحالة الجديدة:")
        print(f"📢 إجمالي الإعلانات: {total_ads}")
        print(f"⭐ الإعلانات المميزة: {featured_ads}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

if __name__ == '__main__':
    add_demo_ads()

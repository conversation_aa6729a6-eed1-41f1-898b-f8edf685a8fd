{% extends 'admin_panel/base.html' %}
{% load static %}

{% block title %}إدارة التقارير{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-flag text-danger me-2"></i>إدارة التقارير
                    {% if reports %}
                        <span class="badge bg-danger">{{ reports|length }}</span>
                    {% endif %}
                </h2>
                
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-filter me-1"></i>فلترة
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="?status=pending">التقارير المعلقة</a></li>
                        <li><a class="dropdown-item" href="?status=resolved">التقارير المحلولة</a></li>
                        <li><a class="dropdown-item" href="?">جميع التقارير</a></li>
                    </ul>
                </div>
            </div>

            {% if reports %}
                <div class="row">
                    {% for report in reports %}
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card h-100 {% if report.status == 'pending' %}border-danger{% else %}border-success{% endif %}">
                            <div class="card-header {% if report.status == 'pending' %}bg-danger bg-opacity-10{% else %}bg-success bg-opacity-10{% endif %}">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <i class="fas fa-flag me-1"></i>
                                        تقرير #{{ report.id }}
                                    </h6>
                                    {% if report.status == 'pending' %}
                                        <span class="badge bg-danger">معلق</span>
                                    {% else %}
                                        <span class="badge bg-success">محلول</span>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="card-body">
                                <div class="mb-3">
                                    <strong>الإعلان المبلغ عنه:</strong>
                                    <p class="mb-1">{{ report.advertisement.title|truncatechars:50 }}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        بواسطة: {{ report.advertisement.user.get_full_name|default:report.advertisement.user.username }}
                                    </small>
                                </div>
                                
                                <div class="mb-3">
                                    <strong>المبلغ:</strong>
                                    <p class="mb-1">{{ report.user.get_full_name|default:report.user.username }}</p>
                                    <small class="text-muted">{{ report.user.email }}</small>
                                </div>
                                
                                <div class="mb-3">
                                    <strong>سبب التبليغ:</strong>
                                    <span class="badge bg-warning">{{ report.get_reason_display }}</span>
                                </div>
                                
                                {% if report.description %}
                                <div class="mb-3">
                                    <strong>الوصف:</strong>
                                    <p class="text-muted">{{ report.description|truncatechars:100 }}</p>
                                </div>
                                {% endif %}
                                
                                <div class="mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ report.created_at|date:"Y-m-d H:i" }}
                                    </small>
                                </div>
                            </div>
                            
                            <div class="card-footer bg-transparent">
                                <div class="d-grid gap-2">
                                    <a href="{% url 'admin_panel:report_detail' report.pk %}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i>عرض التفاصيل
                                    </a>
                                    
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'ads:detail' report.advertisement.pk %}" class="btn btn-outline-info btn-sm" target="_blank">
                                            <i class="fas fa-external-link-alt me-1"></i>الإعلان
                                        </a>
                                        
                                        {% if report.status == 'pending' %}
                                        <form method="post" action="{% url 'admin_panel:resolve_report' report.pk %}" class="d-inline">
                                            {% csrf_token %}
                                            <button type="submit" class="btn btn-success btn-sm" onclick="return confirm('هل تريد وضع علامة محلول على هذا التقرير؟')">
                                                <i class="fas fa-check me-1"></i>حل
                                            </button>
                                        </form>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1">الأولى</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
            {% else %}
                <div class="text-center py-5">
                    <div class="card border-0 bg-light">
                        <div class="card-body p-5">
                            <i class="fas fa-shield-alt fa-5x text-success mb-4"></i>
                            <h3 class="text-success mb-3">ممتاز! لا توجد تقارير</h3>
                            <p class="text-muted mb-4">
                                لا توجد تقارير حالياً. هذا يعني أن المحتوى نظيف ولا يحتوي على مخالفات.
                            </p>
                            <a href="{% url 'admin_panel:dashboard' %}" class="btn btn-primary">
                                <i class="fas fa-tachometer-alt me-2"></i>العودة للوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-flag fa-2x text-danger mb-2"></i>
                <h4>{{ total_reports|default:0 }}</h4>
                <small>إجمالي التقارير</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                <h4>{{ pending_reports|default:0 }}</h4>
                <small>تقارير معلقة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <h4>{{ resolved_reports|default:0 }}</h4>
                <small>تقارير محلولة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-percentage fa-2x text-info mb-2"></i>
                <h4>{{ resolution_rate|default:0 }}%</h4>
                <small>معدل الحل</small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

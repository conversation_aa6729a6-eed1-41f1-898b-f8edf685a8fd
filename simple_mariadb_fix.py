#!/usr/bin/env python
"""
Simple MariaDB 10.4 Fix
This script provides a simple solution for the RETURNING clause issue
"""

def apply_simple_fix():
    """
    Apply a simple fix by monkey-patching Django's MySQL backend
    """
    print("🔧 تطبيق الإصلاح البسيط لـ MariaDB 10.4...")
    
    try:
        # Import after Django setup
        import os
        import django
        
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
        django.setup()
        
        from django.db.backends.mysql import features
        
        # Simple monkey patch
        original_init = features.DatabaseFeatures.__init__
        
        def new_init(self, connection):
            original_init(self, connection)
            # Force disable RETURNING support
            object.__setattr__(self, 'can_return_columns_from_insert', False)
            object.__setattr__(self, 'can_return_rows_from_bulk_insert', False)
        
        features.DatabaseFeatures.__init__ = new_init
        
        print("✅ تم تطبيق الإصلاح البسيط")
        return True
        
    except Exception as e:
        print(f"❌ فشل الإصلاح: {e}")
        return False

def test_user_creation():
    """
    Test creating a user after the fix
    """
    print("\n🧪 اختبار إنشاء مستخدم...")
    
    try:
        from accounts.models import CustomUser
        from django.db import transaction
        
        test_username = "mariadb_test_user"
        
        # Delete if exists
        CustomUser.objects.filter(username=test_username).delete()
        
        # Create user using transaction
        with transaction.atomic():
            user = CustomUser.objects.create_user(
                username=test_username,
                email="<EMAIL>",
                password="testpass123",
                first_name="اختبار",
                last_name="MariaDB"
            )
        
        print(f"✅ تم إنشاء المستخدم: {user.username}")
        
        # Test authentication
        from django.contrib.auth import authenticate
        auth_user = authenticate(username=test_username, password="testpass123")
        
        if auth_user:
            print("✅ المصادقة تعمل")
        
        # Cleanup
        user.delete()
        print("✅ تم حذف المستخدم التجريبي")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل اختبار المستخدم: {e}")
        return False

def main():
    """
    Main function
    """
    print("🚀 إصلاح MariaDB 10.4 البسيط")
    print("=" * 40)
    
    # Apply the fix
    if not apply_simple_fix():
        return False
    
    # Test the fix
    if not test_user_creation():
        return False
    
    print("\n" + "=" * 40)
    print("🎉 تم الإصلاح بنجاح!")
    print("=" * 40)
    
    print("\n📋 ما تم إصلاحه:")
    print("✅ إزالة دعم RETURNING clause")
    print("✅ إصلاح إنشاء المستخدمين")
    print("✅ التوافق مع MariaDB 10.4")
    
    print("\n🌐 الموقع جاهز للاستخدام!")
    
    return True

if __name__ == '__main__':
    main()

-- إنشاء قاعدة البيانات لموقع الإعلانات المبوبة
CREATE DATABASE IF NOT EXISTS classified_ads_db
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

-- استخدام قاعدة البيانات
USE classified_ads_db;

-- إنشاء مستخدم مخصص (اختياري - للأمان)
-- CREATE USER IF NOT EXISTS 'classified_user'@'localhost' IDENTIFIED BY 'classified_password123';
-- GRANT ALL PRIVILEGES ON classified_ads_db.* TO 'classified_user'@'localhost';
-- FLUSH PRIVILEGES;

-- عرض قواعد البيانات للتأكد من الإنشاء
SHOW DATABASES;

-- عرض معلومات قاعدة البيانات
SELECT
    SCHEMA_NAME as 'اسم قاعدة البيانات',
    DEFAULT_CHARACTER_SET_NAME as 'ترميز الأحرف',
    DEFAULT_COLLATION_NAME as 'ترتيب الأحرف'
FROM information_schema.SCHEMATA
WHERE SCHEMA_NAME = 'classified_ads_db';

-- عرض الجداول (ستكون فارغة قبل تشغيل Django migrations)
SHOW TABLES;

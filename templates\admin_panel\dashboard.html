{% extends 'admin_panel/base.html' %}

{% block title %}لوحة التحكم الرئيسية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>
        لوحة التحكم الرئيسية
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-download me-1"></i>تصدير
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stats-card text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_users }}</h4>
                        <p class="card-text">إجمالي المستخدمين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_ads }}</h4>
                        <p class="card-text">إجمالي الإعلانات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-bullhorn fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333 !important;">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title text-dark">{{ pending_ads }}</h4>
                        <p class="card-text text-dark">في انتظار المراجعة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x opacity-75 text-dark"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); color: #333 !important;">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title text-dark">{{ total_reports }}</h4>
                        <p class="card-text text-dark">التقارير المفتوحة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-flag fa-2x opacity-75 text-dark"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'admin_panel:ad_list' %}?status=pending" class="btn btn-warning w-100">
                            <i class="fas fa-clock me-2"></i>مراجعة الإعلانات
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'admin_panel:user_list' %}" class="btn btn-info w-100">
                            <i class="fas fa-users me-2"></i>إدارة المستخدمين
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'admin_panel:category_create' %}" class="btn btn-success w-100">
                            <i class="fas fa-plus me-2"></i>إضافة قسم
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'admin_panel:report_list' %}" class="btn btn-danger w-100">
                            <i class="fas fa-flag me-2"></i>التقارير
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Ads -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-bullhorn me-2"></i>أحدث الإعلانات</h5>
                <a href="{% url 'admin_panel:ad_list' %}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if recent_ads %}
                    {% for ad in recent_ads %}
                    <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                        <div>
                            <h6 class="mb-1">{{ ad.title|truncatechars:30 }}</h6>
                            <small class="text-muted">{{ ad.user.username }} - {{ ad.created_at|timesince }}</small>
                        </div>
                        <div>
                            {% if ad.status == 'approved' %}
                                <span class="badge bg-success">موافق عليه</span>
                            {% elif ad.status == 'pending' %}
                                <span class="badge bg-warning">في الانتظار</span>
                            {% elif ad.status == 'rejected' %}
                                <span class="badge bg-danger">مرفوض</span>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد إعلانات حديثة</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Recent Users -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-users me-2"></i>أحدث المستخدمين</h5>
                <a href="{% url 'admin_panel:user_list' %}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if recent_users %}
                    {% for user in recent_users %}
                    <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                        <div class="d-flex align-items-center">
                            {% if user.profile_image %}
                                <img src="{{ user.profile_image.url }}" class="rounded-circle me-2" width="30" height="30" alt="{{ user.username }}">
                            {% else %}
                                <i class="fas fa-user-circle fa-2x text-muted me-2"></i>
                            {% endif %}
                            <div>
                                <h6 class="mb-0">{{ user.get_full_name|default:user.username }}</h6>
                                <small class="text-muted">{{ user.date_joined|timesince }}</small>
                            </div>
                        </div>
                        <div>
                            {% if user.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد مستخدمين جدد</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Unresolved Reports -->
{% if unresolved_reports %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-exclamation-triangle me-2 text-warning"></i>تقارير تحتاج إلى مراجعة</h5>
                <a href="{% url 'admin_panel:report_list' %}" class="btn btn-sm btn-outline-danger">عرض الكل</a>
            </div>
            <div class="card-body">
                {% for report in unresolved_reports %}
                <div class="alert alert-warning d-flex justify-content-between align-items-center">
                    <div>
                        <strong>{{ report.get_report_type_display }}</strong> - {{ report.advertisement.title|truncatechars:40 }}
                        <br>
                        <small class="text-muted">بواسطة {{ report.user.username }} - {{ report.created_at|timesince }}</small>
                    </div>
                    <div>
                        <a href="{% url 'admin_panel:report_detail' report.pk %}" class="btn btn-sm btn-outline-primary">مراجعة</a>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

#!/usr/bin/env python
"""
Script to migrate data from SQLite to MySQL manually
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

import mysql.connector
from mysql.connector import Error

def setup_mysql_database():
    """إعداد قاعدة بيانات MySQL"""
    try:
        # الاتصال بـ MySQL
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            port=3306
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # إنشاء قاعدة البيانات
            cursor.execute("""
                CREATE DATABASE IF NOT EXISTS classified_ads_db 
                CHARACTER SET utf8mb4 
                COLLATE utf8mb4_unicode_ci
            """)
            
            print("✅ تم إنشاء قاعدة البيانات MySQL")
            return True
            
    except Error as e:
        print(f"❌ خطأ في MySQL: {e}")
        return False
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def migrate_data():
    """نقل البيانات من SQLite إلى MySQL"""
    print("🚀 بدء نقل البيانات من SQLite إلى MySQL...")
    
    # 1. إعداد قاعدة بيانات MySQL
    if not setup_mysql_database():
        return False
    
    # 2. تعيين متغير البيئة لاستخدام MySQL
    os.environ['USE_MYSQL'] = 'true'
    
    # 3. إعادة تحميل إعدادات Django
    from django.conf import settings
    from django.core.management import execute_from_command_line
    
    try:
        # تطبيق الهجرات على MySQL
        print("📦 تطبيق الهجرات على MySQL...")
        execute_from_command_line(['manage.py', 'migrate', '--run-syncdb'])
        print("✅ تم تطبيق الهجرات على MySQL")
        
        # إنشاء البيانات التجريبية مباشرة في MySQL
        print("📊 إنشاء البيانات التجريبية في MySQL...")
        create_sample_data_mysql()
        
        print("🎉 تم نقل البيانات إلى MySQL بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نقل البيانات: {e}")
        return False

def create_sample_data_mysql():
    """إنشاء البيانات التجريبية في MySQL"""
    from ads.models import Category, Advertisement
    from accounts.models import CustomUser
    
    # إنشاء الأقسام
    categories_data = [
        {'name': 'عقارات', 'description': 'شقق، فيلات، أراضي للبيع والإيجار', 'icon': 'fas fa-home'},
        {'name': 'سيارات', 'description': 'سيارات جديدة ومستعملة للبيع', 'icon': 'fas fa-car'},
        {'name': 'وظائف', 'description': 'فرص عمل في جميع المجالات', 'icon': 'fas fa-briefcase'},
        {'name': 'دورات تدريبية', 'description': 'دورات ودروس في مختلف المجالات', 'icon': 'fas fa-graduation-cap'},
        {'name': 'إلكترونيات', 'description': 'أجهزة إلكترونية ومعدات تقنية', 'icon': 'fas fa-laptop'},
        {'name': 'أثاث ومنزل', 'description': 'أثاث وأدوات منزلية', 'icon': 'fas fa-couch'},
        {'name': 'خدمات', 'description': 'خدمات متنوعة', 'icon': 'fas fa-tools'},
        {'name': 'أزياء وموضة', 'description': 'ملابس وإكسسوارات', 'icon': 'fas fa-tshirt'},
    ]
    
    for cat_data in categories_data:
        category, created = Category.objects.get_or_create(
            name=cat_data['name'],
            defaults={
                'description': cat_data['description'],
                'icon': cat_data['icon'],
                'is_active': True
            }
        )
        if created:
            print(f"✅ تم إنشاء القسم: {category.name}")
    
    # إنشاء المستخدمين
    users_data = [
        {'username': 'user1', 'email': '<EMAIL>', 'first_name': 'أحمد', 'last_name': 'محمد'},
        {'username': 'user2', 'email': '<EMAIL>', 'first_name': 'فاطمة', 'last_name': 'علي'},
        {'username': 'user3', 'email': '<EMAIL>', 'first_name': 'خالد', 'last_name': 'السعد'},
    ]
    
    for user_data in users_data:
        user, created = CustomUser.objects.get_or_create(
            username=user_data['username'],
            defaults={
                'email': user_data['email'],
                'first_name': user_data['first_name'],
                'last_name': user_data['last_name'],
                'is_verified': True
            }
        )
        if created:
            user.set_password('password123')
            user.save()
            print(f"✅ تم إنشاء المستخدم: {user.username}")
    
    # إنشاء المستخدم الإداري
    admin_user, created = CustomUser.objects.get_or_create(
        username='admin',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'مدير',
            'last_name': 'النظام',
            'is_staff': True,
            'is_superuser': True,
            'is_verified': True
        }
    )
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
        print("✅ تم إنشاء المستخدم الإداري: admin")
    
    # إنشاء الإعلانات
    real_estate = Category.objects.get(name='عقارات')
    cars = Category.objects.get(name='سيارات')
    jobs = Category.objects.get(name='وظائف')
    electronics = Category.objects.get(name='إلكترونيات')
    
    user1 = CustomUser.objects.get(username='user1')
    user2 = CustomUser.objects.get(username='user2')
    user3 = CustomUser.objects.get(username='user3')
    
    ads_data = [
        {
            'title': 'شقة للبيع في الرياض',
            'description': 'شقة مميزة للبيع في حي الملز، 3 غرف نوم، 2 حمام، صالة واسعة، مطبخ مجهز.',
            'category': real_estate,
            'user': user1,
            'price': 450000,
            'location': 'الرياض - حي الملز',
            'phone': '0501234567',
            'status': 'approved',
            'is_featured': True
        },
        {
            'title': 'سيارة تويوتا كامري 2020',
            'description': 'سيارة تويوتا كامري موديل 2020، حالة ممتازة، قطعت 45000 كم فقط.',
            'category': cars,
            'user': user2,
            'price': 85000,
            'location': 'جدة',
            'phone': '0507654321',
            'status': 'approved',
            'is_featured': True
        },
        {
            'title': 'مطلوب مطور ويب',
            'description': 'شركة تقنية رائدة تبحث عن مطور ويب خبرة 3 سنوات في Django و React.',
            'category': jobs,
            'user': admin_user,
            'location': 'الرياض',
            'email': '<EMAIL>',
            'status': 'approved'
        },
        {
            'title': 'لابتوب ديل للبيع',
            'description': 'لابتوب ديل Inspiron 15، معالج Intel i7، ذاكرة 16GB، قرص صلب SSD 512GB.',
            'category': electronics,
            'user': user3,
            'price': 2500,
            'location': 'الدمام',
            'phone': '0551234567',
            'status': 'approved'
        }
    ]
    
    for ad_data in ads_data:
        ad, created = Advertisement.objects.get_or_create(
            title=ad_data['title'],
            defaults=ad_data
        )
        if created:
            print(f"✅ تم إنشاء الإعلان: {ad.title}")

def main():
    """الدالة الرئيسية"""
    print("🔄 نقل البيانات من SQLite إلى MySQL")
    print("=" * 50)
    
    success = migrate_data()
    
    if success:
        print("\n🎉 تم نقل البيانات إلى MySQL بنجاح!")
        print("\n📋 معلومات قاعدة البيانات:")
        print("- النوع: MySQL")
        print("- الاسم: classified_ads_db")
        print("- المستخدم: root")
        print("- الخادم: localhost:3306")
        print("\n🔑 حساب المدير:")
        print("- اسم المستخدم: admin")
        print("- كلمة المرور: admin123")
        print("\n🌐 لتشغيل الموقع مع MySQL:")
        print("set USE_MYSQL=true && python manage.py runserver")
    else:
        print("\n❌ فشل في نقل البيانات")

if __name__ == '__main__':
    main()

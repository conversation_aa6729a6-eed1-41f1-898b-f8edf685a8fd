#!/usr/bin/env python
"""
أداة تشخيص شاملة لمشكلة الصور
"""
import os
import django
import requests
from urllib.parse import urljoin

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

from ads.models import Advertisement, AdImage
from django.conf import settings

def diagnose_django_settings():
    """تشخيص إعدادات Django"""
    print("⚙️ تشخيص إعدادات Django")
    print("=" * 40)
    
    issues = []
    
    # فحص MEDIA_ROOT
    print(f"📁 MEDIA_ROOT: {settings.MEDIA_ROOT}")
    if not os.path.exists(settings.MEDIA_ROOT):
        issues.append("MEDIA_ROOT غير موجود")
        print("❌ MEDIA_ROOT غير موجود")
    else:
        print("✅ MEDIA_ROOT موجود")
    
    # فحص MEDIA_URL
    print(f"🌐 MEDIA_URL: {settings.MEDIA_URL}")
    if not settings.MEDIA_URL:
        issues.append("MEDIA_URL غير محدد")
        print("❌ MEDIA_URL غير محدد")
    else:
        print("✅ MEDIA_URL محدد")
    
    # فحص DEBUG
    print(f"🐛 DEBUG: {settings.DEBUG}")
    if not settings.DEBUG:
        issues.append("DEBUG=False قد يؤثر على عرض الملفات الثابتة")
        print("⚠️ DEBUG=False")
    else:
        print("✅ DEBUG=True")
    
    return issues

def diagnose_media_structure():
    """تشخيص هيكل مجلدات media"""
    print(f"\n📁 تشخيص هيكل مجلدات media")
    print("-" * 35)
    
    issues = []
    
    # فحص مجلد media الرئيسي
    if os.path.exists('media'):
        print("✅ مجلد media موجود")
        
        # فحص مجلد ads
        ads_dir = os.path.join('media', 'ads')
        if os.path.exists(ads_dir):
            print("✅ مجلد media/ads موجود")
            
            # عدد الملفات في مجلد ads
            files = os.listdir(ads_dir)
            image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.svg'))]
            print(f"📊 عدد ملفات الصور: {len(image_files)}")
            
            if len(image_files) == 0:
                issues.append("لا توجد ملفات صور في مجلد media/ads")
            
        else:
            issues.append("مجلد media/ads غير موجود")
            print("❌ مجلد media/ads غير موجود")
    else:
        issues.append("مجلد media غير موجود")
        print("❌ مجلد media غير موجود")
    
    return issues

def diagnose_database():
    """تشخيص قاعدة البيانات"""
    print(f"\n💾 تشخيص قاعدة البيانات")
    print("-" * 30)
    
    issues = []
    
    try:
        # إحصائيات الإعلانات
        total_ads = Advertisement.objects.count()
        approved_ads = Advertisement.objects.filter(status='approved').count()
        print(f"📊 إجمالي الإعلانات: {total_ads}")
        print(f"📊 الإعلانات المعتمدة: {approved_ads}")
        
        # إحصائيات الصور
        total_images = AdImage.objects.count()
        print(f"🖼️ إجمالي الصور: {total_images}")
        
        # الإعلانات بدون صور
        ads_without_images = Advertisement.objects.filter(images__isnull=True).distinct().count()
        print(f"❌ إعلانات بدون صور: {ads_without_images}")
        
        # الإعلانات مع صور
        ads_with_images = Advertisement.objects.filter(images__isnull=False).distinct().count()
        print(f"✅ إعلانات مع صور: {ads_with_images}")
        
        if ads_without_images > 0:
            issues.append(f"{ads_without_images} إعلان بدون صور")
        
        # فحص مسارات الصور
        if total_images > 0:
            print(f"\n🔍 فحص مسارات الصور:")
            sample_images = AdImage.objects.all()[:3]
            
            for img in sample_images:
                print(f"   📁 {img.image.name}")
                try:
                    if os.path.exists(img.image.path):
                        file_size = os.path.getsize(img.image.path)
                        print(f"      ✅ موجود ({file_size} بايت)")
                    else:
                        print(f"      ❌ غير موجود")
                        issues.append(f"ملف الصورة غير موجود: {img.image.name}")
                except Exception as e:
                    print(f"      ⚠️ خطأ: {e}")
                    issues.append(f"خطأ في الوصول للصورة: {img.image.name}")
        
    except Exception as e:
        issues.append(f"خطأ في الوصول لقاعدة البيانات: {e}")
        print(f"❌ خطأ في قاعدة البيانات: {e}")
    
    return issues

def diagnose_urls():
    """تشخيص URLs الصور"""
    print(f"\n🌐 تشخيص URLs الصور")
    print("-" * 25)
    
    issues = []
    base_url = "http://127.0.0.1:8000"
    
    try:
        # اختبار الصفحة الرئيسية
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✅ الصفحة الرئيسية تعمل")
        else:
            issues.append(f"الصفحة الرئيسية لا تعمل: {response.status_code}")
            print(f"❌ الصفحة الرئيسية: {response.status_code}")
        
        # اختبار صفحة اختبار الصور
        test_url = urljoin(base_url, '/test-images/')
        response = requests.get(test_url, timeout=5)
        if response.status_code == 200:
            print("✅ صفحة اختبار الصور تعمل")
        else:
            issues.append(f"صفحة اختبار الصور لا تعمل: {response.status_code}")
            print(f"❌ صفحة اختبار الصور: {response.status_code}")
        
        # اختبار URL صورة مباشرة
        sample_images = AdImage.objects.all()[:1]
        if sample_images:
            img = sample_images[0]
            img_url = urljoin(base_url, img.image.url)
            print(f"🔗 اختبار URL الصورة: {img_url}")
            
            try:
                response = requests.get(img_url, timeout=5)
                if response.status_code == 200:
                    print("✅ URL الصورة يعمل")
                else:
                    issues.append(f"URL الصورة لا يعمل: {response.status_code}")
                    print(f"❌ URL الصورة: {response.status_code}")
            except Exception as e:
                issues.append(f"خطأ في الوصول لـ URL الصورة: {e}")
                print(f"❌ خطأ في URL الصورة: {e}")
        
    except Exception as e:
        issues.append(f"خطأ في اختبار URLs: {e}")
        print(f"❌ خطأ في اختبار URLs: {e}")
    
    return issues

def diagnose_templates():
    """تشخيص القوالب"""
    print(f"\n📄 تشخيص القوالب")
    print("-" * 20)
    
    issues = []
    
    # فحص وجود القوالب
    templates_to_check = [
        'templates/home_enhanced.html',
        'templates/ads/list.html',
        'templates/test_images.html'
    ]
    
    for template in templates_to_check:
        if os.path.exists(template):
            print(f"✅ {template} موجود")
            
            # فحص محتوى القالب للبحث عن كود الصور
            try:
                with open(template, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                if 'image.url' in content or 'img' in content:
                    print(f"   ✅ يحتوي على كود عرض الصور")
                else:
                    issues.append(f"{template} لا يحتوي على كود عرض الصور")
                    print(f"   ❌ لا يحتوي على كود عرض الصور")
                    
            except Exception as e:
                issues.append(f"خطأ في قراءة {template}: {e}")
                print(f"   ❌ خطأ في القراءة: {e}")
        else:
            issues.append(f"{template} غير موجود")
            print(f"❌ {template} غير موجود")
    
    return issues

def generate_fix_recommendations(all_issues):
    """إنشاء توصيات الإصلاح"""
    print(f"\n🔧 توصيات الإصلاح")
    print("=" * 25)
    
    if not all_issues:
        print("🎉 لا توجد مشاكل! الصور تعمل بشكل صحيح")
        return
    
    print("المشاكل المكتشفة:")
    for i, issue in enumerate(all_issues, 1):
        print(f"{i}. {issue}")
    
    print(f"\n💡 الحلول المقترحة:")
    
    # حلول مخصصة حسب نوع المشكلة
    if any("MEDIA_ROOT" in issue for issue in all_issues):
        print("• تأكد من إعداد MEDIA_ROOT في settings.py")
        print("• قم بإنشاء مجلد media في جذر المشروع")
    
    if any("بدون صور" in issue for issue in all_issues):
        print("• قم بتشغيل: python fix_images_simple.py")
        print("• أو أضف صور يدوياً من لوحة الإدارة")
    
    if any("URL" in issue for issue in all_issues):
        print("• تأكد من إضافة static files في urls.py")
        print("• تأكد من تشغيل الخادم في وضع DEBUG=True")
    
    if any("غير موجود" in issue for issue in all_issues):
        print("• تأكد من وجود جميع الملفات المطلوبة")
        print("• قم بإعادة تشغيل الخادم")

def main():
    """الدالة الرئيسية"""
    print("🔍 أداة تشخيص شاملة لمشكلة الصور")
    print("=" * 60)
    
    all_issues = []
    
    # تشغيل جميع اختبارات التشخيص
    all_issues.extend(diagnose_django_settings())
    all_issues.extend(diagnose_media_structure())
    all_issues.extend(diagnose_database())
    all_issues.extend(diagnose_urls())
    all_issues.extend(diagnose_templates())
    
    # إنشاء توصيات الإصلاح
    generate_fix_recommendations(all_issues)
    
    print(f"\n📊 ملخص التشخيص:")
    print(f"   🔍 إجمالي المشاكل: {len(all_issues)}")
    
    if len(all_issues) == 0:
        print("   🎉 جميع الفحوصات نجحت!")
        print("   ✅ الصور تعمل بشكل صحيح")
    elif len(all_issues) <= 3:
        print("   ⚠️ مشاكل قليلة تحتاج إصلاح")
    else:
        print("   ❌ مشاكل متعددة تحتاج إصلاح شامل")
    
    print(f"\n🌐 روابط الاختبار:")
    print(f"   الصفحة الرئيسية: http://127.0.0.1:8000/")
    print(f"   صفحة اختبار الصور: http://127.0.0.1:8000/test-images/")
    
    return len(all_issues) == 0

if __name__ == '__main__':
    main()

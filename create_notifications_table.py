#!/usr/bin/env python
"""
Create notifications table manually
"""
import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

def create_notifications_table():
    """إنشاء جدول الإشعارات يدوياً"""
    print("📋 إنشاء جدول الإشعارات")
    print("=" * 40)
    
    try:
        from django.db import connection
        
        with connection.cursor() as cursor:
            # إنشاء جدول الإشعارات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS ads_notification (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    advertisement_id INT NULL,
                    type VARCHAR(20) NOT NULL,
                    title VARCHAR(200) NOT NULL,
                    message TEXT NOT NULL,
                    is_read BOOLEAN DEFAULT FALSE,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES accounts_customuser(id) ON DELETE CASCADE,
                    FOREIGN KEY (advertisement_id) REFERENCES ads_advertisement(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            
            print("✅ تم إنشاء جدول الإشعارات")
            
            # التحقق من الجدول
            cursor.execute("SHOW TABLES LIKE 'ads_notification'")
            result = cursor.fetchone()
            
            if result:
                print("✅ الجدول موجود في قاعدة البيانات")
                
                # عرض هيكل الجدول
                cursor.execute("DESCRIBE ads_notification")
                columns = cursor.fetchall()
                
                print("\n📊 هيكل الجدول:")
                for column in columns:
                    print(f"   - {column[0]}: {column[1]}")
            else:
                print("❌ فشل في إنشاء الجدول")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجدول: {e}")
        return False

def test_notifications():
    """اختبار نظام الإشعارات"""
    print("\n🧪 اختبار نظام الإشعارات")
    print("-" * 30)
    
    try:
        from ads.models import Notification
        from accounts.models import CustomUser
        from ads.models import Advertisement
        
        # الحصول على مستخدم
        admin_user = CustomUser.objects.get(username='admin')
        
        # إنشاء إشعار تجريبي
        notification = Notification.objects.create(
            user=admin_user,
            type='general',
            title='مرحباً بك في نظام الإشعارات',
            message='هذا إشعار تجريبي للتأكد من عمل النظام بشكل صحيح.'
        )
        
        print(f"✅ تم إنشاء إشعار تجريبي: {notification.title}")
        
        # عرض الإشعارات
        notifications = Notification.objects.filter(user=admin_user)
        print(f"📊 عدد الإشعارات: {notifications.count()}")
        
        for notif in notifications:
            read_status = "مقروء" if notif.is_read else "غير مقروء"
            print(f"   - {notif.title} ({read_status})")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإشعارات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔔 إعداد نظام الإشعارات")
    print("=" * 50)
    
    # إنشاء جدول الإشعارات
    if create_notifications_table():
        # اختبار النظام
        if test_notifications():
            print("\n" + "=" * 50)
            print("🎉 تم إعداد نظام الإشعارات بنجاح!")
            print("=" * 50)
            
            print("\n✅ ما تم إنجازه:")
            print("- إنشاء جدول الإشعارات")
            print("- اختبار النظام")
            print("- إنشاء إشعار تجريبي")
            
            print("\n🔔 الميزات المتاحة:")
            print("- إشعارات اعتماد الإعلانات")
            print("- إشعارات رفض الإعلانات")
            print("- إشعارات انتهاء الصلاحية")
            print("- إشعارات عامة")
    
    return True

if __name__ == '__main__':
    main()

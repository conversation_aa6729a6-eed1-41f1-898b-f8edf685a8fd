from django import forms
from django.contrib.auth.forms import UserCreationForm
from .models import CustomUser

class CustomUserCreationForm(UserCreationForm):
    """نموذج إنشاء مستخدم مخصص"""
    email = forms.EmailField(required=True, label="البريد الإلكتروني")
    first_name = forms.CharField(max_length=30, required=False, label="الاسم الأول")
    last_name = forms.CharField(max_length=30, required=False, label="اسم العائلة")
    phone = forms.CharField(max_length=20, required=False, label="رقم الهاتف")
    
    class Meta:
        model = CustomUser
        fields = ('username', 'email', 'first_name', 'last_name', 'phone', 'password1', 'password2')
        labels = {
            'username': 'اسم المستخدم',
            'password1': 'كلمة المرور',
            'password2': 'تأكيد كلمة المرور',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # إضافة CSS classes للحقول
        for field_name, field in self.fields.items():
            field.widget.attrs['class'] = 'form-control'
            if field_name == 'username':
                field.help_text = 'مطلوب. 150 حرف أو أقل. أحرف وأرقام و @/./+/-/_ فقط.'
            elif field_name == 'password1':
                field.help_text = 'كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل.'
            elif field_name == 'password2':
                field.help_text = 'أدخل نفس كلمة المرور للتأكيد.'

class UserProfileForm(forms.ModelForm):
    """نموذج تعديل الملف الشخصي"""
    class Meta:
        model = CustomUser
        fields = ['first_name', 'last_name', 'email', 'phone', 'address', 'profile_image']
        labels = {
            'first_name': 'الاسم الأول',
            'last_name': 'اسم العائلة',
            'email': 'البريد الإلكتروني',
            'phone': 'رقم الهاتف',
            'address': 'العنوان',
            'profile_image': 'صورة الملف الشخصي',
        }
        widgets = {
            'address': forms.Textarea(attrs={'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field_name, field in self.fields.items():
            if field_name != 'profile_image':
                field.widget.attrs['class'] = 'form-control'
            else:
                field.widget.attrs['class'] = 'form-control'

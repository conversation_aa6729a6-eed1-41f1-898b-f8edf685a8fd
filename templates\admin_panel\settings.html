{% extends 'admin_panel/base.html' %}
{% load static %}

{% block title %}إعدادات النظام{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-cogs text-primary me-2"></i>إعدادات النظام
                </h2>
                
                <div class="btn-group">
                    <button type="button" class="btn btn-success" onclick="saveAllSettings()">
                        <i class="fas fa-save me-1"></i>حفظ جميع الإعدادات
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="resetSettings()">
                        <i class="fas fa-undo me-1"></i>إعادة تعيين
                    </button>
                </div>
            </div>

            <div class="row">
                <!-- إعدادات عامة -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-globe me-2"></i>الإعدادات العامة
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="generalSettings">
                                <div class="mb-3">
                                    <label for="site_name" class="form-label">اسم الموقع</label>
                                    <input type="text" class="form-control" id="site_name" value="موقع الإعلانات المبوبة">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="site_description" class="form-label">وصف الموقع</label>
                                    <textarea class="form-control" id="site_description" rows="3">أفضل موقع للإعلانات المبوبة في المملكة العربية السعودية</textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="contact_email" class="form-label">بريد التواصل</label>
                                    <input type="email" class="form-control" id="contact_email" value="<EMAIL>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="contact_phone" class="form-label">هاتف التواصل</label>
                                    <input type="text" class="form-control" id="contact_phone" value="+966501234567">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="timezone" class="form-label">المنطقة الزمنية</label>
                                    <select class="form-select" id="timezone">
                                        <option value="Asia/Riyadh" selected>الرياض (GMT+3)</option>
                                        <option value="Asia/Dubai">دبي (GMT+4)</option>
                                        <option value="UTC">UTC (GMT+0)</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="language" class="form-label">اللغة الافتراضية</label>
                                    <select class="form-select" id="language">
                                        <option value="ar" selected>العربية</option>
                                        <option value="en">English</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- إعدادات الإعلانات -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bullhorn me-2"></i>إعدادات الإعلانات
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="adSettings">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="auto_approve" checked>
                                        <label class="form-check-label" for="auto_approve">
                                            الموافقة التلقائية على الإعلانات
                                        </label>
                                    </div>
                                    <div class="form-text">إذا كانت مفعلة، ستتم الموافقة على الإعلانات تلقائياً</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="max_images" class="form-label">الحد الأقصى للصور</label>
                                    <input type="number" class="form-control" id="max_images" value="5" min="1" max="20">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="max_image_size" class="form-label">الحد الأقصى لحجم الصورة (MB)</label>
                                    <input type="number" class="form-control" id="max_image_size" value="5" min="1" max="50">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="ad_expiry_days" class="form-label">مدة انتهاء الإعلان (أيام)</label>
                                    <input type="number" class="form-control" id="ad_expiry_days" value="30" min="1" max="365">
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="allow_guest_ads">
                                        <label class="form-check-label" for="allow_guest_ads">
                                            السماح للزوار بنشر الإعلانات
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="require_phone" checked>
                                        <label class="form-check-label" for="require_phone">
                                            رقم الهاتف مطلوب
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- إعدادات الأمان -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-shield-alt me-2"></i>إعدادات الأمان
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="securitySettings">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enable_captcha" checked>
                                        <label class="form-check-label" for="enable_captcha">
                                            تفعيل CAPTCHA
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="email_verification" checked>
                                        <label class="form-check-label" for="email_verification">
                                            تأكيد البريد الإلكتروني مطلوب
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="max_login_attempts" class="form-label">الحد الأقصى لمحاولات تسجيل الدخول</label>
                                    <input type="number" class="form-control" id="max_login_attempts" value="5" min="1" max="20">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="session_timeout" class="form-label">انتهاء الجلسة (دقائق)</label>
                                    <input type="number" class="form-control" id="session_timeout" value="30" min="5" max="1440">
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="log_user_activity" checked>
                                        <label class="form-check-label" for="log_user_activity">
                                            تسجيل أنشطة المستخدمين
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- إعدادات الإشعارات -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bell me-2"></i>إعدادات الإشعارات
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="notificationSettings">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="email_notifications" checked>
                                        <label class="form-check-label" for="email_notifications">
                                            إشعارات البريد الإلكتروني
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="sms_notifications">
                                        <label class="form-check-label" for="sms_notifications">
                                            إشعارات الرسائل النصية
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="admin_notifications" checked>
                                        <label class="form-check-label" for="admin_notifications">
                                            إشعارات المديرين
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="notification_frequency" class="form-label">تكرار الإشعارات</label>
                                    <select class="form-select" id="notification_frequency">
                                        <option value="instant" selected>فوري</option>
                                        <option value="hourly">كل ساعة</option>
                                        <option value="daily">يومي</option>
                                        <option value="weekly">أسبوعي</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- إعدادات النسخ الاحتياطي -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-database me-2"></i>النسخ الاحتياطي
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="auto_backup" checked>
                                    <label class="form-check-label" for="auto_backup">
                                        النسخ الاحتياطي التلقائي
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="backup_frequency" class="form-label">تكرار النسخ الاحتياطي</label>
                                <select class="form-select" id="backup_frequency">
                                    <option value="daily" selected>يومي</option>
                                    <option value="weekly">أسبوعي</option>
                                    <option value="monthly">شهري</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="backup_retention" class="form-label">الاحتفاظ بالنسخ (أيام)</label>
                                <input type="number" class="form-control" id="backup_retention" value="30" min="1" max="365">
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-primary" onclick="createBackup()">
                                    <i class="fas fa-download me-1"></i>إنشاء نسخة احتياطية الآن
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="viewBackups()">
                                    <i class="fas fa-list me-1"></i>عرض النسخ الاحتياطية
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إعدادات الصيانة -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-tools me-2"></i>صيانة النظام
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="maintenance_mode">
                                    <label class="form-check-label" for="maintenance_mode">
                                        وضع الصيانة
                                    </label>
                                </div>
                                <div class="form-text text-warning">سيمنع الوصول للموقع عدا المديرين</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="maintenance_message" class="form-label">رسالة الصيانة</label>
                                <textarea class="form-control" id="maintenance_message" rows="3">الموقع تحت الصيانة حالياً. سنعود قريباً!</textarea>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-warning" onclick="clearCache()">
                                    <i class="fas fa-broom me-1"></i>مسح الذاكرة المؤقتة
                                </button>
                                <button type="button" class="btn btn-info" onclick="optimizeDatabase()">
                                    <i class="fas fa-database me-1"></i>تحسين قاعدة البيانات
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="viewLogs()">
                                    <i class="fas fa-file-alt me-1"></i>عرض سجلات النظام
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function saveAllSettings() {
    // جمع جميع الإعدادات
    const settings = {
        general: getFormData('generalSettings'),
        ads: getFormData('adSettings'),
        security: getFormData('securitySettings'),
        notifications: getFormData('notificationSettings'),
        backup: {
            auto_backup: document.getElementById('auto_backup').checked,
            backup_frequency: document.getElementById('backup_frequency').value,
            backup_retention: document.getElementById('backup_retention').value
        },
        maintenance: {
            maintenance_mode: document.getElementById('maintenance_mode').checked,
            maintenance_message: document.getElementById('maintenance_message').value
        }
    };
    
    // إرسال الإعدادات للخادم
    fetch('/admin-panel/save-settings/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم حفظ الإعدادات بنجاح!');
        } else {
            alert('حدث خطأ في حفظ الإعدادات');
        }
    });
}

function getFormData(formId) {
    const form = document.getElementById(formId);
    const formData = new FormData(form);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    // إضافة checkboxes
    const checkboxes = form.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        data[checkbox.id] = checkbox.checked;
    });
    
    return data;
}

function resetSettings() {
    if (confirm('هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟')) {
        location.reload();
    }
}

function createBackup() {
    if (confirm('هل تريد إنشاء نسخة احتياطية الآن؟')) {
        fetch('/admin-panel/create-backup/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إنشاء النسخة الاحتياطية بنجاح!');
            } else {
                alert('حدث خطأ في إنشاء النسخة الاحتياطية');
            }
        });
    }
}

function clearCache() {
    if (confirm('هل تريد مسح الذاكرة المؤقتة؟')) {
        fetch('/admin-panel/clear-cache/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم مسح الذاكرة المؤقتة بنجاح!');
            } else {
                alert('حدث خطأ في مسح الذاكرة المؤقتة');
            }
        });
    }
}

function optimizeDatabase() {
    if (confirm('هل تريد تحسين قاعدة البيانات؟ قد يستغرق هذا بعض الوقت.')) {
        fetch('/admin-panel/optimize-database/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم تحسين قاعدة البيانات بنجاح!');
            } else {
                alert('حدث خطأ في تحسين قاعدة البيانات');
            }
        });
    }
}

function viewBackups() {
    window.open('/admin-panel/backups/', '_blank');
}

function viewLogs() {
    window.open('/admin-panel/logs/', '_blank');
}
</script>
{% endblock %}

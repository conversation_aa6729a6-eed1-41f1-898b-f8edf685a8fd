{% extends 'base.html' %}
{% load static %}

{% block title %}الأقسام - إعلاناتي{% endblock %}

{% block extra_css %}
<link href="{% static 'css/dubizzle-style.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<!-- Include Dubizzle Header -->
{% include 'includes/dubizzle_header.html' %}

<div class="container" style="max-width: 1200px; margin: 0 auto; padding: 2rem 1rem;">
    <h1 style="font-size: 2rem; font-weight: 700; margin-bottom: 2rem; color: #1e293b;">جميع الأقسام</h1>
    
    {% if categories %}
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 1.5rem;">
            {% for category in categories %}
            <a href="{% url 'ads:search' %}?category={{ category.id }}" style="background: white; border: 1px solid #e2e8f0; border-radius: 1rem; padding: 2rem; text-align: center; text-decoration: none; color: inherit; transition: all 0.3s ease; display: block;">
                <div style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #ff6b35, #ff8c66); display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; font-size: 2rem; color: white; box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);">
                    <i class="{{ category.icon|default:'fas fa-tag' }}"></i>
                </div>
                <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 0.75rem; color: #1e293b;">{{ category.name }}</h3>
                <p style="color: #64748b; margin-bottom: 1rem;">{{ category.description|default:"" }}</p>
                <span style="color: #ff6b35; font-weight: 500;">
                    {{ category.ads_count|default:0 }} إعلان
                </span>
            </a>
            {% endfor %}
        </div>
    {% else %}
        <div style="text-align: center; padding: 4rem 0; color: #64748b;">
            <i class="fas fa-tags" style="font-size: 4rem; margin-bottom: 1rem; color: #cbd5e1;"></i>
            <h3 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 1rem;">لا توجد أقسام</h3>
            <p>لم يتم إنشاء أي أقسام بعد</p>
        </div>
    {% endif %}
</div>

<style>
a[href*="category"]:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    border-color: #ff6b35;
}

a[href*="category"]:hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: #ff6b35;
}
</style>
{% endblock %}

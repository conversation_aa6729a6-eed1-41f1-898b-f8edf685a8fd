#!/usr/bin/env python
"""
اختبار شامل للصلاحيات الإدارية المتقدمة
"""
import os
import django
import requests
from urllib.parse import urljoin

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

def test_admin_permissions():
    """اختبار صلاحيات المدير"""
    print("🔐 اختبار صلاحيات المدير")
    print("=" * 50)
    
    try:
        from admin_panel.permissions import (
            is_admin_user, is_superuser, get_user_permissions, 
            has_permission, check_ad_permissions, check_user_permissions
        )
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # اختبار المدير العام
        admin_user = User.objects.get(username='admin')
        print(f"✅ المدير العام: {admin_user.username}")
        print(f"   - is_admin_user: {is_admin_user(admin_user)}")
        print(f"   - is_superuser: {is_superuser(admin_user)}")
        
        # اختبار الصلاحيات
        permissions = get_user_permissions(admin_user)
        print(f"   - الصلاحيات: {len(permissions)} صلاحية")
        for perm in permissions[:5]:  # أول 5 صلاحيات
            print(f"     * {perm}")
        
        # اختبار صلاحيات محددة
        test_permissions = [
            'view_admin_panel',
            'manage_users',
            'manage_system_settings',
            'delete_any_content'
        ]
        
        for perm in test_permissions:
            result = has_permission(admin_user, perm)
            print(f"   - {perm}: {'✅' if result else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الصلاحيات: {e}")
        return False

def test_new_admin_urls():
    """اختبار الروابط الإدارية الجديدة"""
    print(f"\n🌐 اختبار الروابط الإدارية الجديدة")
    print("-" * 40)
    
    base_url = "http://127.0.0.1:8000"
    new_admin_urls = [
        # إدارة الإعلانات الشاملة
        "/admin-panel/ads/1/approve-new/",
        "/admin-panel/ads/1/reject-new/",
        "/admin-panel/ads/1/delete-new/",
        "/admin-panel/ads/1/toggle-featured/",
        "/admin-panel/ads/1/toggle-urgent/",
        "/admin-panel/ads/bulk-approve/",
        
        # إدارة المستخدمين المتقدمة
        "/admin-panel/users/1/toggle-status/",
        "/admin-panel/users/1/delete/",
        "/admin-panel/users/1/permissions/",
        "/admin-panel/users/1/activity/",
        "/admin-panel/users/bulk-actions/",
        
        # إدارة النظام والإعدادات
        "/admin-panel/system/maintenance-mode/",
        "/admin-panel/system/cleanup/",
        "/admin-panel/system/health/",
        "/admin-panel/system/export/",
    ]
    
    working_urls = []
    broken_urls = []
    
    for url_path in new_admin_urls:
        full_url = urljoin(base_url, url_path)
        try:
            response = requests.get(full_url, timeout=5)
            if response.status_code in [200, 302, 405]:  # 405 للـ POST methods
                working_urls.append(url_path)
                print(f"✅ {url_path}")
            else:
                broken_urls.append((url_path, response.status_code))
                print(f"❌ {url_path} - {response.status_code}")
        except requests.exceptions.RequestException as e:
            broken_urls.append((url_path, f"خطأ: {str(e)}"))
            print(f"❌ {url_path} - خطأ في الاتصال")
    
    return working_urls, broken_urls

def test_activity_logging():
    """اختبار تسجيل الأنشطة"""
    print(f"\n📋 اختبار تسجيل الأنشطة")
    print("-" * 30)
    
    try:
        from admin_panel.models import ActivityLog
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        admin_user = User.objects.get(username='admin')
        
        # إنشاء نشاط تجريبي
        test_activity = ActivityLog.log_activity(
            user=admin_user,
            action='test',
            description='اختبار تسجيل الأنشطة المتقدم',
            object_type='test',
            object_id=999
        )
        
        print(f"✅ تم إنشاء نشاط تجريبي: {test_activity.id}")
        print(f"   - المستخدم: {test_activity.user.username}")
        print(f"   - النشاط: {test_activity.get_action_display()}")
        print(f"   - الوصف: {test_activity.description}")
        print(f"   - الوقت: {test_activity.timestamp}")
        
        # إحصائيات الأنشطة
        total_activities = ActivityLog.objects.count()
        recent_activities = ActivityLog.objects.order_by('-timestamp')[:5]
        
        print(f"✅ إجمالي الأنشطة: {total_activities}")
        print(f"✅ أحدث الأنشطة:")
        for activity in recent_activities:
            user_name = activity.user.username if activity.user else 'مجهول'
            print(f"   - {user_name}: {activity.get_action_display()} - {activity.description[:30]}...")
        
        # تنظيف النشاط التجريبي
        test_activity.delete()
        print(f"✅ تم حذف النشاط التجريبي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الأنشطة: {e}")
        return False

def test_system_settings():
    """اختبار إعدادات النظام المتقدمة"""
    print(f"\n⚙️ اختبار إعدادات النظام المتقدمة")
    print("-" * 40)
    
    try:
        from admin_panel.models import SystemSettings
        
        # اختبار إعدادات الصيانة
        maintenance_mode = SystemSettings.get_setting('maintenance_mode', 'false')
        print(f"✅ وضع الصيانة: {maintenance_mode}")
        
        # اختبار إعدادات الأمان
        security_settings = [
            'enable_captcha',
            'require_email_verification',
            'max_login_attempts',
            'session_timeout'
        ]
        
        for setting in security_settings:
            value = SystemSettings.get_setting(setting, 'غير محدد')
            print(f"✅ {setting}: {value}")
        
        # اختبار إنشاء إعداد جديد
        test_setting = SystemSettings.set_setting(
            'test_advanced_setting',
            'test_value_advanced',
            'إعداد تجريبي متقدم'
        )
        print(f"✅ تم إنشاء إعداد تجريبي: {test_setting.key}")
        
        # تنظيف الإعداد التجريبي
        test_setting.delete()
        print(f"✅ تم حذف الإعداد التجريبي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإعدادات: {e}")
        return False

def test_advanced_features():
    """اختبار الميزات المتقدمة"""
    print(f"\n🚀 اختبار الميزات المتقدمة")
    print("-" * 30)
    
    try:
        from ads.models import Advertisement, Notification
        from accounts.models import CustomUser
        from admin_panel.models import ActivityLog, SystemSettings
        
        # إحصائيات شاملة
        stats = {
            'users': CustomUser.objects.count(),
            'active_users': CustomUser.objects.filter(is_active=True).count(),
            'staff_users': CustomUser.objects.filter(is_staff=True).count(),
            'superusers': CustomUser.objects.filter(is_superuser=True).count(),
            'ads': Advertisement.objects.count(),
            'pending_ads': Advertisement.objects.filter(status='pending').count(),
            'approved_ads': Advertisement.objects.filter(status='approved').count(),
            'featured_ads': Advertisement.objects.filter(is_featured=True).count(),
            'urgent_ads': Advertisement.objects.filter(is_urgent=True).count(),
            'notifications': Notification.objects.count(),
            'unread_notifications': Notification.objects.filter(is_read=False).count(),
            'activities': ActivityLog.objects.count(),
            'settings': SystemSettings.objects.count(),
        }
        
        print(f"📊 إحصائيات النظام المتقدمة:")
        print(f"   👥 المستخدمون: {stats['users']} (نشط: {stats['active_users']}, مدير: {stats['staff_users']}, مدير عام: {stats['superusers']})")
        print(f"   📢 الإعلانات: {stats['ads']} (معلق: {stats['pending_ads']}, معتمد: {stats['approved_ads']}, مميز: {stats['featured_ads']}, عاجل: {stats['urgent_ads']})")
        print(f"   🔔 الإشعارات: {stats['notifications']} (غير مقروء: {stats['unread_notifications']})")
        print(f"   📋 الأنشطة: {stats['activities']}")
        print(f"   ⚙️ الإعدادات: {stats['settings']}")
        
        return stats
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الميزات: {e}")
        return {}

def generate_advanced_report():
    """إنشاء تقرير متقدم"""
    print(f"\n📊 التقرير المتقدم للصلاحيات الإدارية")
    print("=" * 60)
    
    # تشغيل جميع الاختبارات
    permissions_test = test_admin_permissions()
    working_urls, broken_urls = test_new_admin_urls()
    activity_test = test_activity_logging()
    settings_test = test_system_settings()
    advanced_stats = test_advanced_features()
    
    # تقييم النتائج
    total_tests = 4
    passed_tests = sum([permissions_test, activity_test, settings_test, bool(advanced_stats)])
    
    print(f"\n📈 ملخص النتائج:")
    print(f"✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"🌐 الروابط العاملة: {len(working_urls)}")
    print(f"❌ الروابط المكسورة: {len(broken_urls)}")
    
    if broken_urls:
        print(f"\n🔗 الروابط التي تحتاج إصلاح:")
        for url, error in broken_urls:
            print(f"   - {url}: {error}")
    
    # تقييم الجودة
    quality_score = (passed_tests / total_tests) * 100
    url_score = (len(working_urls) / (len(working_urls) + len(broken_urls))) * 100 if (working_urls or broken_urls) else 100
    
    overall_score = (quality_score + url_score) / 2
    
    print(f"\n🎯 تقييم الجودة:")
    print(f"   📋 نجاح الاختبارات: {quality_score:.1f}%")
    print(f"   🌐 صحة الروابط: {url_score:.1f}%")
    print(f"   🏆 التقييم الإجمالي: {overall_score:.1f}%")
    
    if overall_score >= 90:
        print(f"🎉 ممتاز! النظام يعمل بشكل مثالي")
    elif overall_score >= 75:
        print(f"✅ جيد جداً! النظام يعمل بشكل جيد مع بعض التحسينات المطلوبة")
    elif overall_score >= 60:
        print(f"⚠️ مقبول! النظام يحتاج بعض الإصلاحات")
    else:
        print(f"❌ يحتاج تحسين! النظام يحتاج إصلاحات جوهرية")
    
    return {
        'passed_tests': passed_tests,
        'total_tests': total_tests,
        'working_urls': len(working_urls),
        'broken_urls': len(broken_urls),
        'overall_score': overall_score,
        'advanced_stats': advanced_stats
    }

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل للصلاحيات الإدارية المتقدمة")
    print("=" * 70)
    
    report = generate_advanced_report()
    
    print("\n" + "=" * 70)
    print("🎯 خلاصة الاختبار:")
    
    if report['overall_score'] >= 90:
        print("🏆 تم تطوير نظام صلاحيات إداري متقدم وشامل بنجاح!")
        print("✨ جميع الميزات المطلوبة تعمل بشكل مثالي")
        
        print(f"\n🌟 الميزات المطبقة:")
        print(f"   🔐 نظام صلاحيات متقدم ومحكم")
        print(f"   📢 إدارة إعلانات شاملة (موافقة، رفض، تمييز، حذف)")
        print(f"   👥 إدارة مستخدمين متقدمة (تفعيل، صلاحيات، أنشطة)")
        print(f"   ⚙️ إدارة نظام شاملة (صيانة، تنظيف، تصدير)")
        print(f"   🛡️ ميزات أمان متطورة (تسجيل أنشطة، مراقبة)")
        
        print(f"\n🔗 الروابط المتاحة:")
        print(f"   - لوحة الإدارة: http://127.0.0.1:8000/admin-panel/")
        print(f"   - إعدادات النظام: http://127.0.0.1:8000/admin-panel/settings/")
        print(f"   - تنظيف قاعدة البيانات: http://127.0.0.1:8000/admin-panel/system/cleanup/")
        
        print(f"\n🔑 بيانات الدخول:")
        print(f"   - المدير العام: admin / admin123")
        
    else:
        print("⚠️ النظام يحتاج بعض التحسينات")
        print(f"📊 النتيجة: {report['overall_score']:.1f}%")
    
    print("=" * 70)
    
    return report['overall_score'] >= 90

if __name__ == '__main__':
    main()

#!/usr/bin/env python
"""
Script to create all tables in MySQL database
"""
import os
import sys
import django

# Setup Django environment with MySQL
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
os.environ['USE_MYSQL'] = 'true'
django.setup()

from django.db import connection
from django.core.management import execute_from_command_line
import mysql.connector
from mysql.connector import Error

def setup_mysql_database():
    """إعداد قاعدة بيانات MySQL"""
    try:
        # الاتصال بـ MySQL
        connection_mysql = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            port=3306
        )
        
        if connection_mysql.is_connected():
            cursor = connection_mysql.cursor()
            
            # إنشاء قاعدة البيانات
            cursor.execute("""
                CREATE DATABASE IF NOT EXISTS classified_ads_db 
                CHARACTER SET utf8mb4 
                COLLATE utf8mb4_unicode_ci
            """)
            
            print("✅ تم إنشاء قاعدة البيانات MySQL")
            return True
            
    except Error as e:
        print(f"❌ خطأ في MySQL: {e}")
        return False
    finally:
        if 'connection_mysql' in locals() and connection_mysql.is_connected():
            cursor.close()
            connection_mysql.close()

def create_mysql_tables():
    """إنشاء جميع الجداول في MySQL"""
    print("🔧 إنشاء جميع الجداول في MySQL...")
    
    try:
        # تطبيق جميع الهجرات
        print("🚀 تطبيق جميع الهجرات على MySQL...")
        execute_from_command_line(['manage.py', 'migrate', '--run-syncdb'])
        
        print("✅ تم إنشاء جميع الجداول في MySQL بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False

def check_mysql_tables():
    """فحص الجداول في MySQL"""
    try:
        with connection.cursor() as cursor:
            # عرض معلومات قاعدة البيانات
            cursor.execute("SELECT DATABASE()")
            db_name = cursor.fetchone()[0]
            print(f"📋 اسم قاعدة البيانات: {db_name}")
            
            # عرض الجداول الموجودة
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"📁 عدد الجداول الموجودة: {len(tables)}")
            
            if tables:
                print("\n📋 الجداول الموجودة في MySQL:")
                for table in tables:
                    table_name = table[0]
                    cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                    count = cursor.fetchone()[0]
                    print(f"   - {table_name}: {count} سجل")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في فحص الجداول: {e}")
        return False

def create_mysql_indexes():
    """إنشاء الفهارس في MySQL"""
    print("\n📇 إنشاء الفهارس في MySQL...")
    
    try:
        with connection.cursor() as cursor:
            # إنشاء فهارس MySQL محسنة
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_ads_status ON ads_advertisement(status)",
                "CREATE INDEX IF NOT EXISTS idx_ads_category ON ads_advertisement(category_id)",
                "CREATE INDEX IF NOT EXISTS idx_ads_user ON ads_advertisement(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_ads_created ON ads_advertisement(created_at)",
                "CREATE INDEX IF NOT EXISTS idx_ads_featured ON ads_advertisement(is_featured)",
                "CREATE INDEX IF NOT EXISTS idx_ads_price ON ads_advertisement(price)",
                "CREATE INDEX IF NOT EXISTS idx_ads_location ON ads_advertisement(location)",
                "CREATE INDEX IF NOT EXISTS idx_category_active ON ads_category(is_active)",
                "CREATE INDEX IF NOT EXISTS idx_user_active ON accounts_customuser(is_active)",
                "CREATE INDEX IF NOT EXISTS idx_user_verified ON accounts_customuser(is_verified)",
                "CREATE INDEX IF NOT EXISTS idx_reports_resolved ON ads_report(is_resolved)",
                "CREATE INDEX IF NOT EXISTS idx_images_main ON ads_adimage(is_main)",
                # فهارس مركبة للبحث المتقدم
                "CREATE INDEX IF NOT EXISTS idx_ads_status_category ON ads_advertisement(status, category_id)",
                "CREATE INDEX IF NOT EXISTS idx_ads_status_featured ON ads_advertisement(status, is_featured)",
                "CREATE INDEX IF NOT EXISTS idx_ads_status_created ON ads_advertisement(status, created_at)",
            ]
            
            for index_sql in indexes:
                try:
                    cursor.execute(index_sql)
                    print(f"✅ تم إنشاء فهرس")
                except Exception as e:
                    if "Duplicate key name" in str(e):
                        print(f"⚠️ فهرس موجود بالفعل")
                    else:
                        print(f"❌ خطأ في إنشاء فهرس: {e}")
        
        print("✅ تم إنشاء الفهارس في MySQL بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الفهارس: {e}")
        return False

def optimize_mysql_tables():
    """تحسين جداول MySQL"""
    print("\n⚡ تحسين جداول MySQL...")
    
    try:
        with connection.cursor() as cursor:
            # الحصول على قائمة الجداول
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            for table in tables:
                table_name = table[0]
                try:
                    # تحسين الجدول
                    cursor.execute(f"OPTIMIZE TABLE `{table_name}`")
                    print(f"✅ تم تحسين جدول {table_name}")
                except Exception as e:
                    print(f"⚠️ تحذير في تحسين {table_name}: {e}")
        
        print("✅ تم تحسين جميع الجداول!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحسين الجداول: {e}")
        return False

def create_sample_data_mysql():
    """إنشاء البيانات التجريبية في MySQL"""
    print("\n📊 إنشاء البيانات التجريبية في MySQL...")
    
    try:
        from ads.models import Category, Advertisement
        from accounts.models import CustomUser
        
        # إنشاء المستخدم الإداري
        admin_user, created = CustomUser.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'مدير',
                'last_name': 'النظام',
                'is_staff': True,
                'is_superuser': True,
                'is_verified': True
            }
        )
        if created:
            admin_user.set_password('admin123')
            admin_user.save()
            print("✅ تم إنشاء المستخدم الإداري")
        
        # إنشاء الأقسام
        categories_data = [
            {'name': 'عقارات', 'description': 'شقق، فيلات، أراضي للبيع والإيجار', 'icon': 'fas fa-home'},
            {'name': 'سيارات', 'description': 'سيارات جديدة ومستعملة للبيع', 'icon': 'fas fa-car'},
            {'name': 'وظائف', 'description': 'فرص عمل في جميع المجالات', 'icon': 'fas fa-briefcase'},
            {'name': 'دورات تدريبية', 'description': 'دورات ودروس في مختلف المجالات', 'icon': 'fas fa-graduation-cap'},
            {'name': 'إلكترونيات', 'description': 'أجهزة إلكترونية ومعدات تقنية', 'icon': 'fas fa-laptop'},
            {'name': 'أثاث ومنزل', 'description': 'أثاث وأدوات منزلية', 'icon': 'fas fa-couch'},
            {'name': 'خدمات', 'description': 'خدمات متنوعة', 'icon': 'fas fa-tools'},
            {'name': 'أزياء وموضة', 'description': 'ملابس وإكسسوارات', 'icon': 'fas fa-tshirt'},
        ]
        
        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={
                    'description': cat_data['description'],
                    'icon': cat_data['icon'],
                    'is_active': True
                }
            )
            if created:
                print(f"✅ تم إنشاء القسم: {category.name}")
        
        print("✅ تم إنشاء البيانات التجريبية في MySQL!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🏗️ إنشاء جميع جداول MySQL لموقع الإعلانات")
    print("=" * 60)
    
    # 1. إعداد قاعدة البيانات
    print("1️⃣ إعداد قاعدة بيانات MySQL...")
    if not setup_mysql_database():
        return False
    
    # 2. إنشاء الجداول
    print("\n2️⃣ إنشاء جميع الجداول...")
    if not create_mysql_tables():
        return False
    
    # 3. فحص الجداول
    print("\n3️⃣ فحص الجداول المنشأة...")
    if not check_mysql_tables():
        return False
    
    # 4. إنشاء الفهارس
    print("\n4️⃣ إنشاء الفهارس...")
    create_mysql_indexes()
    
    # 5. تحسين الجداول
    print("\n5️⃣ تحسين الجداول...")
    optimize_mysql_tables()
    
    # 6. إنشاء البيانات التجريبية
    print("\n6️⃣ إنشاء البيانات التجريبية...")
    create_sample_data_mysql()
    
    # 7. فحص نهائي
    print("\n7️⃣ فحص نهائي...")
    check_mysql_tables()
    
    print("\n" + "=" * 60)
    print("🎉 تم إنشاء جميع جداول MySQL بنجاح!")
    print("=" * 60)
    
    print("\n📋 الجداول المنشأة في MySQL:")
    print("✅ accounts_customuser - جدول المستخدمين المخصص")
    print("✅ ads_category - جدول أقسام الإعلانات")
    print("✅ ads_advertisement - جدول الإعلانات الرئيسي")
    print("✅ ads_adimage - جدول صور الإعلانات")
    print("✅ ads_report - جدول تقارير الإعلانات")
    print("✅ auth_* - جداول نظام المصادقة")
    print("✅ django_* - جداول Django الأساسية")
    
    print("\n🔧 المميزات المضافة:")
    print("✅ فهارس محسنة للبحث السريع")
    print("✅ ترميز UTF8MB4 لدعم الأحرف العربية")
    print("✅ تحسين الجداول للأداء")
    print("✅ بيانات تجريبية جاهزة")
    
    print("\n🌐 لتشغيل الموقع مع MySQL:")
    print("set USE_MYSQL=true && python manage.py runserver")
    
    return True

if __name__ == '__main__':
    main()

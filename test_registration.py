#!/usr/bin/env python
"""
Test user registration after MariaDB fix
"""
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

def test_user_registration():
    """Test creating a new user"""
    print("🧪 اختبار تسجيل مستخدم جديد...")
    
    try:
        from accounts.models import CustomUser
        from django.db import transaction
        
        # Test data
        test_username = "new_test_user"
        test_email = "<EMAIL>"
        
        # Delete if exists
        CustomUser.objects.filter(username=test_username).delete()
        
        # Create new user
        print("📝 إنشاء مستخدم جديد...")
        
        with transaction.atomic():
            user = CustomUser.objects.create_user(
                username=test_username,
                email=test_email,
                password="newpass123",
                first_name="مستخدم",
                last_name="جديد",
                phone="**********"
            )
        
        print(f"✅ تم إنشاء المستخدم: {user.username}")
        print(f"   - البريد: {user.email}")
        print(f"   - الاسم: {user.get_full_name()}")
        
        # Test authentication
        from django.contrib.auth import authenticate
        auth_user = authenticate(username=test_username, password="newpass123")
        
        if auth_user:
            print("✅ تسجيل الدخول يعمل")
        else:
            print("❌ فشل تسجيل الدخول")
        
        # Test updating user
        user.first_name = "مستخدم محدث"
        user.save()
        print("✅ تحديث المستخدم يعمل")
        
        # Cleanup
        user.delete()
        print("✅ تم حذف المستخدم التجريبي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التسجيل: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_form_submission():
    """Test form submission simulation"""
    print("\n📝 اختبار إرسال النموذج...")
    
    try:
        from accounts.forms import CustomUserCreationForm
        
        # Simulate form data
        form_data = {
            'username': 'form_test_user',
            'email': '<EMAIL>',
            'first_name': 'مستخدم',
            'last_name': 'النموذج',
            'password1': 'formpass123456',
            'password2': 'formpass123456',
            'phone': '**********'
        }
        
        form = CustomUserCreationForm(data=form_data)
        
        if form.is_valid():
            print("✅ النموذج صحيح")
            
            # Test saving
            user = form.save()
            print(f"✅ تم حفظ المستخدم من النموذج: {user.username}")
            
            # Cleanup
            user.delete()
            print("✅ تم حذف مستخدم النموذج")
            
        else:
            print(f"❌ النموذج غير صحيح: {form.errors}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النموذج: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 اختبار إصلاح MariaDB 10.4")
    print("=" * 50)
    
    success = True
    
    # Test 1: Direct user creation
    if not test_user_registration():
        success = False
    
    # Test 2: Form submission
    if not test_form_submission():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ مشكلة RETURNING clause تم حلها")
        print("✅ تسجيل المستخدمين يعمل بشكل طبيعي")
        print("\n🌐 يمكنك الآن:")
        print("- تشغيل الموقع: python manage.py runserver")
        print("- تسجيل مستخدمين جدد")
        print("- استخدام جميع ميزات الموقع")
    else:
        print("❌ بعض الاختبارات فشلت")
    
    print("=" * 50)
    
    return success

if __name__ == '__main__':
    main()

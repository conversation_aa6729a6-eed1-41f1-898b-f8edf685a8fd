/**
 * نظام Sliders و Carousels المتقدم
 * Enhanced Sliders and Carousels System
 */

class EnhancedSlider {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            autoPlay: options.autoPlay || false,
            autoPlayInterval: options.autoPlayInterval || 5000,
            showDots: options.showDots !== false,
            showArrows: options.showArrows !== false,
            slidesToShow: options.slidesToShow || 1,
            slidesToScroll: options.slidesToScroll || 1,
            infinite: options.infinite !== false,
            responsive: options.responsive || [],
            ...options
        };
        
        this.currentSlide = 0;
        this.totalSlides = 0;
        this.isAnimating = false;
        this.autoPlayTimer = null;
        
        this.init();
    }
    
    init() {
        this.setupSlider();
        this.createControls();
        this.bindEvents();
        this.updateResponsive();
        
        if (this.options.autoPlay) {
            this.startAutoPlay();
        }
    }
    
    setupSlider() {
        const slides = this.container.querySelectorAll('.slide-item');
        this.totalSlides = slides.length;
        
        // Create slider wrapper
        const sliderWrapper = document.createElement('div');
        sliderWrapper.className = 'slider-wrapper';
        
        const sliderTrack = document.createElement('div');
        sliderTrack.className = 'slider-track';
        
        // Move slides to track
        slides.forEach(slide => {
            sliderTrack.appendChild(slide);
        });
        
        sliderWrapper.appendChild(sliderTrack);
        this.container.appendChild(sliderWrapper);
        
        this.track = sliderTrack;
        this.slides = slides;
        
        // Add CSS
        this.addSliderCSS();
    }
    
    addSliderCSS() {
        const style = document.createElement('style');
        style.textContent = `
            .enhanced-slider {
                position: relative;
                overflow: hidden;
                border-radius: var(--radius-xl);
            }
            
            .slider-wrapper {
                overflow: hidden;
                width: 100%;
            }
            
            .slider-track {
                display: flex;
                transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                will-change: transform;
            }
            
            .slide-item {
                flex: 0 0 auto;
                width: 100%;
                position: relative;
            }
            
            .slider-controls {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                background: rgba(255, 255, 255, 0.9);
                border: none;
                border-radius: 50%;
                width: 48px;
                height: 48px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.3s ease;
                z-index: 10;
                box-shadow: var(--shadow-lg);
            }
            
            .slider-controls:hover {
                background: white;
                transform: translateY(-50%) scale(1.1);
            }
            
            .slider-prev {
                left: 16px;
            }
            
            .slider-next {
                right: 16px;
            }
            
            .slider-dots {
                display: flex;
                justify-content: center;
                gap: 8px;
                margin-top: 16px;
            }
            
            .slider-dot {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                background: var(--neutral-300);
                cursor: pointer;
                transition: all 0.3s ease;
            }
            
            .slider-dot.active {
                background: var(--primary-500);
                transform: scale(1.2);
            }
            
            .slider-dot:hover {
                background: var(--primary-400);
            }
            
            /* Horizontal Scroll Categories */
            .categories-horizontal {
                display: flex;
                gap: 16px;
                overflow-x: auto;
                padding: 16px 0;
                scroll-behavior: smooth;
                -webkit-overflow-scrolling: touch;
            }
            
            .categories-horizontal::-webkit-scrollbar {
                height: 6px;
            }
            
            .categories-horizontal::-webkit-scrollbar-track {
                background: var(--neutral-200);
                border-radius: 3px;
            }
            
            .categories-horizontal::-webkit-scrollbar-thumb {
                background: var(--primary-400);
                border-radius: 3px;
            }
            
            .category-item-horizontal {
                flex: 0 0 auto;
                min-width: 200px;
                background: white;
                border-radius: var(--radius-lg);
                padding: 20px;
                text-align: center;
                box-shadow: var(--shadow-md);
                transition: all 0.3s ease;
                cursor: pointer;
            }
            
            .category-item-horizontal:hover {
                transform: translateY(-4px);
                box-shadow: var(--shadow-lg);
            }
            
            .category-icon-horizontal {
                font-size: 2.5rem;
                margin-bottom: 12px;
                background: var(--gradient-primary);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }
            
            .category-title-horizontal {
                font-size: var(--font-size-lg);
                font-weight: 600;
                margin-bottom: 8px;
                color: var(--neutral-800);
            }
            
            .category-count-horizontal {
                font-size: var(--font-size-sm);
                color: var(--neutral-500);
            }
            
            @media (max-width: 768px) {
                .slider-controls {
                    width: 40px;
                    height: 40px;
                }
                
                .slider-prev {
                    left: 8px;
                }
                
                .slider-next {
                    right: 8px;
                }
                
                .category-item-horizontal {
                    min-width: 160px;
                    padding: 16px;
                }
            }
        `;
        document.head.appendChild(style);
    }
    
    createControls() {
        if (this.options.showArrows) {
            this.createArrows();
        }
        
        if (this.options.showDots) {
            this.createDots();
        }
    }
    
    createArrows() {
        const prevBtn = document.createElement('button');
        prevBtn.className = 'slider-controls slider-prev';
        prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
        prevBtn.setAttribute('aria-label', 'الشريحة السابقة');
        
        const nextBtn = document.createElement('button');
        nextBtn.className = 'slider-controls slider-next';
        nextBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
        nextBtn.setAttribute('aria-label', 'الشريحة التالية');
        
        this.container.appendChild(prevBtn);
        this.container.appendChild(nextBtn);
        
        this.prevBtn = prevBtn;
        this.nextBtn = nextBtn;
    }
    
    createDots() {
        const dotsContainer = document.createElement('div');
        dotsContainer.className = 'slider-dots';
        
        for (let i = 0; i < this.totalSlides; i++) {
            const dot = document.createElement('button');
            dot.className = 'slider-dot';
            dot.setAttribute('aria-label', `الذهاب للشريحة ${i + 1}`);
            if (i === 0) dot.classList.add('active');
            dotsContainer.appendChild(dot);
        }
        
        this.container.parentNode.insertBefore(dotsContainer, this.container.nextSibling);
        this.dotsContainer = dotsContainer;
    }
    
    bindEvents() {
        if (this.prevBtn) {
            this.prevBtn.addEventListener('click', () => this.prevSlide());
        }
        
        if (this.nextBtn) {
            this.nextBtn.addEventListener('click', () => this.nextSlide());
        }
        
        if (this.dotsContainer) {
            this.dotsContainer.addEventListener('click', (e) => {
                if (e.target.classList.contains('slider-dot')) {
                    const index = Array.from(this.dotsContainer.children).indexOf(e.target);
                    this.goToSlide(index);
                }
            });
        }
        
        // Touch events for mobile
        let startX = 0;
        let endX = 0;
        
        this.container.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
        });
        
        this.container.addEventListener('touchend', (e) => {
            endX = e.changedTouches[0].clientX;
            const diff = startX - endX;
            
            if (Math.abs(diff) > 50) {
                if (diff > 0) {
                    this.nextSlide();
                } else {
                    this.prevSlide();
                }
            }
        });
        
        // Pause autoplay on hover
        this.container.addEventListener('mouseenter', () => {
            this.pauseAutoPlay();
        });
        
        this.container.addEventListener('mouseleave', () => {
            if (this.options.autoPlay) {
                this.startAutoPlay();
            }
        });
        
        // Responsive handling
        window.addEventListener('resize', () => {
            this.updateResponsive();
        });
    }
    
    updateResponsive() {
        const width = window.innerWidth;
        let newSlidesToShow = this.options.slidesToShow;
        
        this.options.responsive.forEach(breakpoint => {
            if (width <= breakpoint.breakpoint) {
                newSlidesToShow = breakpoint.settings.slidesToShow || newSlidesToShow;
            }
        });
        
        this.currentSlidesToShow = newSlidesToShow;
        this.updateSlideWidth();
    }
    
    updateSlideWidth() {
        const slideWidth = 100 / this.currentSlidesToShow;
        this.slides.forEach(slide => {
            slide.style.width = `${slideWidth}%`;
        });
    }
    
    goToSlide(index) {
        if (this.isAnimating || index === this.currentSlide) return;
        
        this.isAnimating = true;
        this.currentSlide = index;
        
        const translateX = -(index * (100 / this.currentSlidesToShow));
        this.track.style.transform = `translateX(${translateX}%)`;
        
        this.updateDots();
        
        setTimeout(() => {
            this.isAnimating = false;
        }, 500);
    }
    
    nextSlide() {
        const nextIndex = this.currentSlide + 1;
        if (nextIndex >= this.totalSlides) {
            if (this.options.infinite) {
                this.goToSlide(0);
            }
        } else {
            this.goToSlide(nextIndex);
        }
    }
    
    prevSlide() {
        const prevIndex = this.currentSlide - 1;
        if (prevIndex < 0) {
            if (this.options.infinite) {
                this.goToSlide(this.totalSlides - 1);
            }
        } else {
            this.goToSlide(prevIndex);
        }
    }
    
    updateDots() {
        if (!this.dotsContainer) return;
        
        const dots = this.dotsContainer.querySelectorAll('.slider-dot');
        dots.forEach((dot, index) => {
            dot.classList.toggle('active', index === this.currentSlide);
        });
    }
    
    startAutoPlay() {
        this.pauseAutoPlay();
        this.autoPlayTimer = setInterval(() => {
            this.nextSlide();
        }, this.options.autoPlayInterval);
    }
    
    pauseAutoPlay() {
        if (this.autoPlayTimer) {
            clearInterval(this.autoPlayTimer);
            this.autoPlayTimer = null;
        }
    }
    
    destroy() {
        this.pauseAutoPlay();
        // Remove event listeners and clean up
    }
}

// Horizontal Categories Scroller
class HorizontalScroller {
    constructor(container) {
        this.container = container;
        this.init();
    }
    
    init() {
        this.createScrollButtons();
        this.bindEvents();
    }
    
    createScrollButtons() {
        const leftBtn = document.createElement('button');
        leftBtn.className = 'scroll-btn scroll-left';
        leftBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
        leftBtn.style.cssText = `
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            background: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            box-shadow: var(--shadow-md);
            cursor: pointer;
            z-index: 10;
            transition: all 0.3s ease;
        `;
        
        const rightBtn = document.createElement('button');
        rightBtn.className = 'scroll-btn scroll-right';
        rightBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
        rightBtn.style.cssText = leftBtn.style.cssText.replace('left: 0', 'right: 0');
        
        this.container.style.position = 'relative';
        this.container.appendChild(leftBtn);
        this.container.appendChild(rightBtn);
        
        this.leftBtn = leftBtn;
        this.rightBtn = rightBtn;
    }
    
    bindEvents() {
        this.leftBtn.addEventListener('click', () => {
            this.container.querySelector('.categories-horizontal').scrollBy({
                left: -200,
                behavior: 'smooth'
            });
        });
        
        this.rightBtn.addEventListener('click', () => {
            this.container.querySelector('.categories-horizontal').scrollBy({
                left: 200,
                behavior: 'smooth'
            });
        });
    }
}

// Initialize sliders when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize featured ads slider
    const featuredSlider = document.querySelector('.featured-slider');
    if (featuredSlider) {
        new EnhancedSlider(featuredSlider, {
            autoPlay: true,
            autoPlayInterval: 4000,
            slidesToShow: 3,
            slidesToScroll: 1,
            responsive: [
                {
                    breakpoint: 768,
                    settings: {
                        slidesToShow: 2
                    }
                },
                {
                    breakpoint: 480,
                    settings: {
                        slidesToShow: 1
                    }
                }
            ]
        });
    }
    
    // Initialize categories horizontal scroller
    const categoriesContainer = document.querySelector('.categories-container');
    if (categoriesContainer) {
        new HorizontalScroller(categoriesContainer);
    }
});

// Export for use in other files
window.EnhancedSlider = EnhancedSlider;
window.HorizontalScroller = HorizontalScroller;

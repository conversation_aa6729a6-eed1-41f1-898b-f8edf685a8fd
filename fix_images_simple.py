#!/usr/bin/env python
"""
إصلاح مشكلة الصور - حل مبسط
"""
import os
import django
import urllib.request
import shutil

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

from ads.models import Advertisement, AdImage
from django.core.files.base import ContentFile
from django.core.files import File

def create_media_directories():
    """إنشاء مجلدات media المطلوبة"""
    print("📁 إنشاء مجلدات media...")
    
    directories = [
        'media',
        'media/ads',
        'media/categories',
        'media/users'
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ تم إنشاء المجلد: {directory}")
        except Exception as e:
            print(f"❌ خطأ في إنشاء المجلد {directory}: {e}")
    
    return True

def create_placeholder_images():
    """إنشاء صور placeholder بسيطة"""
    print("\n🖼️ إنشاء صور placeholder...")
    
    try:
        # إنشاء صورة placeholder بسيطة باستخدام Python
        from PIL import Image, ImageDraw, ImageFont
        
        # ألوان مختلفة للفئات
        colors = [
            '#3B82F6',  # أزرق
            '#10B981',  # أخضر
            '#8B5CF6',  # بنفسجي
            '#F59E0B',  # برتقالي
            '#EF4444',  # أحمر
            '#6B7280',  # رمادي
        ]
        
        ads_without_images = Advertisement.objects.filter(images__isnull=True).distinct()
        
        if not ads_without_images.exists():
            print("ℹ️ جميع الإعلانات لديها صور")
            return True
        
        print(f"📊 عدد الإعلانات بدون صور: {ads_without_images.count()}")
        
        for i, ad in enumerate(ads_without_images[:10]):  # أول 10 إعلانات فقط
            try:
                # إنشاء صورة بسيطة
                img = Image.new('RGB', (400, 300), color=colors[i % len(colors)])
                draw = ImageDraw.Draw(img)
                
                # إضافة نص بسيط
                try:
                    font = ImageFont.load_default()
                except:
                    font = None
                
                text = f"إعلان #{ad.id}"
                if font:
                    # حساب موضع النص في المنتصف
                    bbox = draw.textbbox((0, 0), text, font=font)
                    text_width = bbox[2] - bbox[0]
                    text_height = bbox[3] - bbox[1]
                    x = (400 - text_width) // 2
                    y = (300 - text_height) // 2
                    
                    # إضافة خلفية للنص
                    draw.rectangle([x-10, y-10, x+text_width+10, y+text_height+10], fill='white')
                    draw.text((x, y), text, fill='black', font=font)
                
                # حفظ الصورة
                filename = f"placeholder_{ad.id}.jpg"
                filepath = os.path.join('media', 'ads', filename)
                img.save(filepath, 'JPEG', quality=85)
                
                # إنشاء كائن AdImage
                with open(filepath, 'rb') as f:
                    ad_image = AdImage.objects.create(
                        advertisement=ad,
                        is_main=True
                    )
                    ad_image.image.save(filename, File(f), save=True)
                
                print(f"✅ تم إنشاء صورة للإعلان: {ad.title[:30]}...")
                
            except Exception as e:
                print(f"❌ خطأ في إنشاء صورة للإعلان {ad.id}: {e}")
        
        return True
        
    except ImportError:
        print("⚠️ مكتبة PIL غير متاحة، سيتم استخدام طريقة بديلة...")
        return create_simple_placeholder_files()
    except Exception as e:
        print(f"❌ خطأ في إنشاء الصور: {e}")
        return False

def create_simple_placeholder_files():
    """إنشاء ملفات placeholder بسيطة بدون PIL"""
    print("📄 إنشاء ملفات placeholder بسيطة...")
    
    # إنشاء ملف SVG بسيط كـ placeholder
    svg_content = '''<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
    <rect width="400" height="300" fill="#f3f4f6"/>
    <text x="200" y="150" text-anchor="middle" font-family="Arial" font-size="20" fill="#6b7280">
        صورة الإعلان
    </text>
</svg>'''
    
    ads_without_images = Advertisement.objects.filter(images__isnull=True).distinct()
    
    for ad in ads_without_images[:5]:  # أول 5 إعلانات فقط
        try:
            filename = f"placeholder_{ad.id}.svg"
            filepath = os.path.join('media', 'ads', filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(svg_content)
            
            # إنشاء كائن AdImage
            with open(filepath, 'rb') as f:
                ad_image = AdImage.objects.create(
                    advertisement=ad,
                    is_main=True
                )
                ad_image.image.save(filename, File(f), save=True)
            
            print(f"✅ تم إنشاء placeholder للإعلان: {ad.title[:30]}...")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء placeholder للإعلان {ad.id}: {e}")
    
    return True

def test_image_urls():
    """اختبار URLs الصور"""
    print("\n🧪 اختبار URLs الصور...")
    
    try:
        ads_with_images = Advertisement.objects.filter(images__isnull=False).distinct()[:3]
        
        for ad in ads_with_images:
            first_image = ad.images.first()
            print(f"\n📋 الإعلان: {ad.title[:30]}...")
            print(f"   📁 مسار الصورة: {first_image.image.name}")
            print(f"   🌐 URL: {first_image.image.url}")
            
            # فحص وجود الملف
            try:
                file_path = first_image.image.path
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f"   ✅ الملف موجود ({file_size} بايت)")
                else:
                    print(f"   ❌ الملف غير موجود")
                    print(f"   📍 المسار: {file_path}")
            except Exception as e:
                print(f"   ⚠️ خطأ في فحص الملف: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار URLs: {e}")
        return False

def check_django_settings():
    """فحص إعدادات Django"""
    print("\n⚙️ فحص إعدادات Django...")
    
    from django.conf import settings
    
    print(f"📁 MEDIA_ROOT: {settings.MEDIA_ROOT}")
    print(f"🌐 MEDIA_URL: {settings.MEDIA_URL}")
    print(f"📁 STATIC_ROOT: {getattr(settings, 'STATIC_ROOT', 'غير محدد')}")
    print(f"🌐 STATIC_URL: {settings.STATIC_URL}")
    
    # فحص وجود MEDIA_ROOT
    if os.path.exists(settings.MEDIA_ROOT):
        print(f"✅ مجلد MEDIA_ROOT موجود")
    else:
        print(f"❌ مجلد MEDIA_ROOT غير موجود")
        try:
            os.makedirs(settings.MEDIA_ROOT, exist_ok=True)
            print(f"✅ تم إنشاء مجلد MEDIA_ROOT")
        except Exception as e:
            print(f"❌ فشل في إنشاء MEDIA_ROOT: {e}")
    
    return True

def create_test_template():
    """إنشاء قالب اختبار للصور"""
    print("\n📄 إنشاء قالب اختبار...")
    
    test_template = '''{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الصور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">اختبار عرض الصور</h1>
        
        <div class="row">
            {% for ad in ads %}
            <div class="col-md-4 mb-4">
                <div class="card">
                    {% if ad.images.exists %}
                        <img src="{{ ad.images.first.image.url }}" class="card-img-top" alt="{{ ad.title }}" style="height: 200px; object-fit: cover;">
                    {% else %}
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                            <span class="text-muted">لا توجد صورة</span>
                        </div>
                    {% endif %}
                    <div class="card-body">
                        <h5 class="card-title">{{ ad.title }}</h5>
                        <p class="card-text">{{ ad.description|truncatewords:10 }}</p>
                        {% if ad.images.exists %}
                            <small class="text-success">✅ يوجد {{ ad.images.count }} صورة</small>
                        {% else %}
                            <small class="text-danger">❌ لا توجد صور</small>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</body>
</html>'''
    
    try:
        with open('templates/test_images.html', 'w', encoding='utf-8') as f:
            f.write(test_template)
        print("✅ تم إنشاء قالب الاختبار: templates/test_images.html")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء قالب الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح مشكلة عدم ظهور الصور")
    print("=" * 60)
    
    try:
        # 1. فحص إعدادات Django
        check_django_settings()
        
        # 2. إنشاء مجلدات media
        create_media_directories()
        
        # 3. إنشاء صور placeholder
        create_placeholder_images()
        
        # 4. اختبار URLs الصور
        test_image_urls()
        
        # 5. إنشاء قالب اختبار
        create_test_template()
        
        print("\n🎉 تم إصلاح مشكلة الصور بنجاح!")
        print("\n📋 الخطوات التالية:")
        print("1. قم بزيارة الموقع: http://127.0.0.1:8000/")
        print("2. تحقق من ظهور الصور في الإعلانات")
        print("3. إذا لم تظهر الصور، تحقق من console المتصفح للأخطاء")
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        return False

if __name__ == '__main__':
    main()

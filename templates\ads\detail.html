{% extends 'base.html' %}

{% block title %}{{ ad.title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <!-- Ad Images -->
        <div class="card mb-4">
            {% if ad.images.all %}
                <div id="adCarousel" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-inner">
                        {% for image in ad.images.all %}
                        <div class="carousel-item {% if forloop.first %}active{% endif %}">
                            <img src="{{ image.image.url }}" class="d-block w-100" style="height: 400px; object-fit: cover;" alt="{{ ad.title }}">
                        </div>
                        {% endfor %}
                    </div>
                    {% if ad.images.count > 1 %}
                    <button class="carousel-control-prev" type="button" data-bs-target="#adCarousel" data-bs-slide="prev">
                        <span class="carousel-control-prev-icon"></span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#adCarousel" data-bs-slide="next">
                        <span class="carousel-control-next-icon"></span>
                    </button>
                    {% endif %}
                </div>
            {% else %}
                <div class="bg-light d-flex align-items-center justify-content-center" style="height: 400px;">
                    <i class="fas fa-image fa-5x text-muted"></i>
                </div>
            {% endif %}
        </div>
        
        <!-- Ad Details -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">{{ ad.title }}</h3>
                    {% if ad.is_featured %}
                        <span class="badge bg-warning text-dark">
                            <i class="fas fa-star me-1"></i>مميز
                        </span>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>القسم:</strong>
                        <span class="badge bg-secondary ms-2">{{ ad.category.name }}</span>
                    </div>
                    <div class="col-md-6 text-end">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>{{ ad.created_at|date:"Y/m/d H:i" }}
                        </small>
                    </div>
                </div>
                
                {% if ad.price %}
                <div class="alert alert-info">
                    <h4 class="alert-heading">
                        <i class="fas fa-tag me-2"></i>السعر: {{ ad.price }} ريال
                    </h4>
                </div>
                {% endif %}
                
                <div class="mb-4">
                    <h5>الوصف:</h5>
                    <p class="lead">{{ ad.description|linebreaks }}</p>
                </div>
                
                <div class="row">
                    {% if ad.location %}
                    <div class="col-md-6 mb-2">
                        <i class="fas fa-map-marker-alt text-primary me-2"></i>
                        <strong>الموقع:</strong> {{ ad.location }}
                    </div>
                    {% endif %}
                    
                    <div class="col-md-6 mb-2">
                        <i class="fas fa-eye text-primary me-2"></i>
                        <strong>المشاهدات:</strong> {{ ad.views_count }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Contact Info -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-user me-2"></i>معلومات المعلن</h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    {% if ad.user.profile_image %}
                        <img src="{{ ad.user.profile_image.url }}" class="rounded-circle me-3" width="50" height="50" alt="{{ ad.user.username }}">
                    {% else %}
                        <i class="fas fa-user-circle fa-3x text-muted me-3"></i>
                    {% endif %}
                    <div>
                        <h6 class="mb-0">{{ ad.user.get_full_name|default:ad.user.username }}</h6>
                        {% if ad.user.is_verified %}
                            <small class="text-success">
                                <i class="fas fa-check-circle me-1"></i>حساب موثق
                            </small>
                        {% endif %}
                    </div>
                </div>
                
                {% if ad.phone %}
                <div class="mb-2">
                    <i class="fas fa-phone text-primary me-2"></i>
                    <strong>الهاتف:</strong>
                    <a href="tel:{{ ad.phone }}" class="text-decoration-none">{{ ad.phone }}</a>
                </div>
                {% endif %}
                
                {% if ad.email %}
                <div class="mb-2">
                    <i class="fas fa-envelope text-primary me-2"></i>
                    <strong>البريد:</strong>
                    <a href="mailto:{{ ad.email }}" class="text-decoration-none">{{ ad.email }}</a>
                </div>
                {% endif %}
                
                <div class="d-grid gap-2 mt-3">
                    {% if ad.phone %}
                    <a href="tel:{{ ad.phone }}" class="btn btn-success">
                        <i class="fas fa-phone me-2"></i>اتصال
                    </a>
                    {% endif %}
                    
                    {% if ad.email %}
                    <a href="mailto:{{ ad.email }}" class="btn btn-primary">
                        <i class="fas fa-envelope me-2"></i>مراسلة
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-cog me-2"></i>إجراءات</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if user.is_authenticated and user != ad.user %}
                    <a href="{% url 'ads:report' ad.pk %}" class="btn btn-outline-danger">
                        <i class="fas fa-flag me-2"></i>الإبلاغ عن الإعلان
                    </a>
                    {% endif %}
                    
                    {% if user == ad.user %}
                    <a href="{% url 'ads:edit' ad.pk %}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>تعديل الإعلان
                    </a>
                    <a href="{% url 'ads:delete' ad.pk %}" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>حذف الإعلان
                    </a>
                    {% endif %}
                    
                    <button class="btn btn-outline-primary" onclick="shareAd()">
                        <i class="fas fa-share me-2"></i>مشاركة
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Ad Stats -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar me-2"></i>إحصائيات</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ ad.views_count }}</h4>
                        <small>مشاهدة</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ ad.created_at|timesince }}</h4>
                        <small>منذ النشر</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Related Ads -->
{% if related_ads %}
<div class="mt-5">
    <h4 class="mb-3"><i class="fas fa-bullhorn me-2"></i>إعلانات مشابهة</h4>
    <div class="row">
        {% for related_ad in related_ads %}
        <div class="col-md-3 mb-3">
            <div class="card">
                {% if related_ad.images.first %}
                    <img src="{{ related_ad.images.first.image.url }}" class="card-img-top" style="height: 150px; object-fit: cover;" alt="{{ related_ad.title }}">
                {% else %}
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 150px;">
                        <i class="fas fa-image fa-2x text-muted"></i>
                    </div>
                {% endif %}
                <div class="card-body">
                    <h6 class="card-title">{{ related_ad.title|truncatechars:30 }}</h6>
                    {% if related_ad.price %}
                        <p class="text-primary fw-bold mb-2">{{ related_ad.price }} ريال</p>
                    {% endif %}
                    <a href="{{ related_ad.get_absolute_url }}" class="btn btn-outline-primary btn-sm w-100">عرض</a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}

<script>
function shareAd() {
    if (navigator.share) {
        navigator.share({
            title: '{{ ad.title }}',
            text: '{{ ad.description|truncatewords:20 }}',
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(function() {
            alert('تم نسخ رابط الإعلان!');
        });
    }
}
</script>
{% endblock %}

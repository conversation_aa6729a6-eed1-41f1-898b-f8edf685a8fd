{% extends 'base_landing.html' %}
{% load static %}

{% block title %}الرئيسية - أفضل موقع للإعلانات المبوبة في المملكة{% endblock %}

{% block extra_css %}
<style>
/* Hero Section Styles */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.1)" points="0,1000 1000,0 1000,1000"/></svg>');
    background-size: cover;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.search-container {
    background: rgba(255,255,255,0.95);
    border-radius: 50px;
    padding: 1rem;
    margin: 2rem auto;
    max-width: 600px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    animation: fadeInUp 1s ease-out 0.4s both;
}

.search-form {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-input {
    border: none;
    outline: none;
    flex: 1;
    padding: 0.75rem 1rem;
    font-size: 1.1rem;
    background: transparent;
    color: #333;
}

.search-input::placeholder {
    color: #666;
}

.search-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    border-radius: 50px;
    padding: 0.75rem 2rem;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.cta-buttons {
    margin-top: 2rem;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.cta-btn {
    padding: 1rem 2rem;
    margin: 0.5rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    border: 2px solid white;
}

.cta-btn-primary {
    background: white;
    color: #667eea;
}

.cta-btn-secondary {
    background: transparent;
    color: white;
}

.cta-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

/* Floating Elements */
.floating-element {
    position: absolute;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
.floating-element:nth-child(2) { top: 60%; right: 15%; animation-delay: 2s; }
.floating-element:nth-child(3) { bottom: 30%; left: 20%; animation-delay: 4s; }

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Categories Section */
.categories-section {
    padding: 5rem 0;
    background: #f8f9fa;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
}

.category-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    height: 100%;
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.category-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.category-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
}

.category-description {
    color: #666;
    margin-bottom: 1.5rem;
}

.category-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.category-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: white;
}

/* Featured Ads Section */
.featured-section {
    padding: 5rem 0;
    background: white;
}

.ad-card {
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    height: 100%;
}

.ad-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.ad-image {
    height: 250px;
    object-fit: cover;
    width: 100%;
}

.ad-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.ad-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

/* Statistics Section */
.stats-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.stat-item {
    text-align: center;
    padding: 2rem;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    display: block;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .search-container {
        margin: 1rem;
        padding: 0.75rem;
    }
    
    .search-form {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .search-btn {
        width: 100%;
    }
    
    .cta-btn {
        display: block;
        margin: 0.5rem 0;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Hero Section Responsive -->
<section class="hero-responsive">
    <!-- Floating Elements -->
    <div class="floating-element">
        <i class="fas fa-home fa-3x"></i>
    </div>
    <div class="floating-element">
        <i class="fas fa-car fa-3x"></i>
    </div>
    <div class="floating-element">
        <i class="fas fa-mobile-alt fa-3x"></i>
    </div>

    <div class="container-responsive">
        <div class="hero-content">
            <h1 class="hero-title">أفضل موقع للإعلانات المبوبة</h1>
            <p class="hero-subtitle">اكتشف آلاف الإعلانات المتنوعة أو انشر إعلانك مجاناً واصل لملايين المشترين</p>

            <!-- Advanced Search Responsive -->
            <div class="search-responsive">
                <form method="GET" action="{% url 'ads:search' %}" class="search-form">
                    <input type="text" name="q" class="search-input" placeholder="ابحث عن أي شيء... سيارات، عقارات، وظائف، إلكترونيات" aria-label="البحث في الإعلانات">
                    <button type="submit" class="search-btn">
                        <i class="fas fa-search me-2"></i>
                        <span class="hidden-mobile">ابحث الآن</span>
                        <span class="visible-mobile">بحث</span>
                    </button>
                </form>
            </div>

            <!-- CTA Buttons Responsive -->
            <div class="flex-responsive flex-center" style="margin-top: var(--spacing-xl);">
                <a href="{% url 'ads:create' %}" class="btn-responsive btn-lg" style="background: white; color: #667eea; flex: 1; max-width: 280px;">
                    <i class="fas fa-plus me-2"></i>أضف إعلانك مجاناً
                </a>
                <a href="{% url 'ads:list' %}" class="btn-responsive btn-lg" style="background: transparent; color: white; border: 2px solid white; flex: 1; max-width: 280px;">
                    <i class="fas fa-search me-2"></i>تصفح الإعلانات
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section Responsive -->
<section class="py-5 bg-light">
    <div class="container-responsive">
        <h2 class="title-responsive text-center">تصفح حسب الأقسام</h2>
        <div class="card-grid-responsive">
            {% if categories %}
                {% for category in categories %}
                <div class="card-item-responsive">
                    <div class="card-content-responsive text-center">
                        <div style="font-size: var(--text-4xl); margin-bottom: var(--spacing-md); background: linear-gradient(45deg, #667eea, #764ba2); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                            <i class="{{ category.icon|default:'fas fa-tag' }}"></i>
                        </div>
                        <h3 class="card-title-responsive">{{ category.name }}</h3>
                        <p class="card-description-responsive">{{ category.description|default:'اكتشف أفضل العروض في هذا القسم' }}</p>
                        <a href="{% url 'ads:search' %}?category={{ category.id }}" class="btn-responsive" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; width: 100%;">
                            تصفح الآن
                        </a>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <!-- Default Categories Responsive -->
                <div class="card-item-responsive">
                    <div class="card-content-responsive text-center">
                        <div style="font-size: var(--text-4xl); margin-bottom: var(--spacing-md); background: linear-gradient(45deg, #667eea, #764ba2); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                            <i class="fas fa-car"></i>
                        </div>
                        <h3 class="card-title-responsive">سيارات ومركبات</h3>
                        <p class="card-description-responsive">سيارات جديدة ومستعملة، دراجات نارية وقطع غيار</p>
                        <a href="{% url 'ads:list' %}" class="btn-responsive" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; width: 100%;">تصفح الآن</a>
                    </div>
                </div>
                <div class="card-item-responsive">
                    <div class="card-content-responsive text-center">
                        <div style="font-size: var(--text-4xl); margin-bottom: var(--spacing-md); background: linear-gradient(45deg, #667eea, #764ba2); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                            <i class="fas fa-home"></i>
                        </div>
                        <h3 class="card-title-responsive">عقارات</h3>
                        <p class="card-description-responsive">شقق، فلل، أراضي للبيع والإيجار</p>
                        <a href="{% url 'ads:list' %}" class="btn-responsive" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; width: 100%;">تصفح الآن</a>
                    </div>
                </div>
                <div class="card-item-responsive">
                    <div class="card-content-responsive text-center">
                        <div style="font-size: var(--text-4xl); margin-bottom: var(--spacing-md); background: linear-gradient(45deg, #667eea, #764ba2); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                            <i class="fas fa-laptop"></i>
                        </div>
                        <h3 class="card-title-responsive">إلكترونيات</h3>
                        <p class="card-description-responsive">هواتف، أجهزة كمبيوتر، أجهزة منزلية</p>
                        <a href="{% url 'ads:list' %}" class="btn-responsive" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; width: 100%;">تصفح الآن</a>
                    </div>
                </div>
                <div class="card-item-responsive">
                    <div class="card-content-responsive text-center">
                        <div style="font-size: var(--text-4xl); margin-bottom: var(--spacing-md); background: linear-gradient(45deg, #667eea, #764ba2); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <h3 class="card-title-responsive">وظائف</h3>
                        <p class="card-description-responsive">فرص عمل في جميع التخصصات</p>
                        <a href="{% url 'ads:list' %}" class="btn-responsive" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; width: 100%;">تصفح الآن</a>
                    </div>
                </div>
                <div class="card-item-responsive">
                    <div class="card-content-responsive text-center">
                        <div style="font-size: var(--text-4xl); margin-bottom: var(--spacing-md); background: linear-gradient(45deg, #667eea, #764ba2); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                            <i class="fas fa-tshirt"></i>
                        </div>
                        <h3 class="card-title-responsive">أزياء وموضة</h3>
                        <p class="card-description-responsive">ملابس، أحذية، إكسسوارات</p>
                        <a href="{% url 'ads:list' %}" class="btn-responsive" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; width: 100%;">تصفح الآن</a>
                    </div>
                </div>
                <div class="card-item-responsive">
                    <div class="card-content-responsive text-center">
                        <div style="font-size: var(--text-4xl); margin-bottom: var(--spacing-md); background: linear-gradient(45deg, #667eea, #764ba2); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                            <i class="fas fa-couch"></i>
                        </div>
                        <h3 class="card-title-responsive">أثاث ومنزل</h3>
                        <p class="card-description-responsive">أثاث، ديكور، أدوات منزلية</p>
                        <a href="{% url 'ads:list' %}" class="btn-responsive" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; width: 100%;">تصفح الآن</a>
                    </div>
                </div>
                <div class="card-item-responsive">
                    <div class="card-content-responsive text-center">
                        <div style="font-size: var(--text-4xl); margin-bottom: var(--spacing-md); background: linear-gradient(45deg, #667eea, #764ba2); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                            <i class="fas fa-gamepad"></i>
                        </div>
                        <h3 class="card-title-responsive">ألعاب ورياضة</h3>
                        <p class="card-description-responsive">ألعاب فيديو، معدات رياضية</p>
                        <a href="{% url 'ads:list' %}" class="btn-responsive" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; width: 100%;">تصفح الآن</a>
                    </div>
                </div>
                <div class="card-item-responsive">
                    <div class="card-content-responsive text-center">
                        <div style="font-size: var(--text-4xl); margin-bottom: var(--spacing-md); background: linear-gradient(45deg, #667eea, #764ba2); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                            <i class="fas fa-tools"></i>
                        </div>
                        <h3 class="card-title-responsive">خدمات</h3>
                        <p class="card-description-responsive">خدمات متنوعة ومهنية</p>
                        <a href="{% url 'ads:list' %}" class="btn-responsive" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; width: 100%;">تصفح الآن</a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</section>

<!-- Featured Ads Section Responsive -->
<section class="py-5 bg-white">
    <div class="container-responsive">
        <h2 class="title-responsive text-center">الإعلانات المميزة</h2>
        {% if featured_ads %}
            <div class="card-grid-responsive">
                {% for ad in featured_ads %}
                <div class="card-item-responsive" style="position: relative;">
                    <div style="position: absolute; top: var(--spacing-md); right: var(--spacing-md); background: linear-gradient(45deg, #ff6b6b, #ee5a24); color: white; padding: var(--spacing-sm) var(--spacing-md); border-radius: 20px; font-size: var(--text-xs); font-weight: 600; z-index: 2;">
                        مميز
                    </div>
                    {% if ad.images.exists %}
                        <img src="{{ ad.images.first.image.url }}" class="card-image-responsive img-cover" alt="{{ ad.title }}">
                    {% else %}
                        <div class="card-image-responsive bg-light d-flex align-items-center justify-content-center">
                            <i class="fas fa-image fa-3x text-muted"></i>
                        </div>
                    {% endif %}
                    <div class="card-content-responsive">
                        <h5 class="card-title-responsive">{{ ad.title|truncatechars:40 }}</h5>
                        <p class="card-description-responsive">{{ ad.description|truncatechars:80 }}</p>
                        <div class="card-footer-responsive">
                            {% if ad.price %}
                                <span style="font-size: var(--text-lg); font-weight: 700; color: #667eea;">{{ ad.price|floatformat:0 }} ريال</span>
                            {% else %}
                                <span class="text-muted">السعر غير محدد</span>
                            {% endif %}
                            <small class="text-muted">{{ ad.created_at|timesince }}</small>
                        </div>
                        <a href="{% url 'ads:detail' ad.pk %}" class="btn-responsive" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; width: 100%; margin-top: var(--spacing-md);">
                            عرض التفاصيل
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="row">
                <!-- Sample Featured Ads -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card ad-card position-relative">
                        <div class="ad-badge">مميز</div>
                        <div class="ad-image bg-gradient d-flex align-items-center justify-content-center">
                            <i class="fas fa-car fa-4x text-white"></i>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">سيارة تويوتا كامري 2022</h5>
                            <p class="card-text text-muted">سيارة بحالة ممتازة، قليلة الاستخدام</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="ad-price">85,000 ريال</span>
                                <small class="text-muted">منذ يومين</small>
                            </div>
                            <a href="{% url 'ads:create' %}" class="btn btn-primary w-100 mt-3">
                                أضف إعلانك هنا
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card ad-card position-relative">
                        <div class="ad-badge">مميز</div>
                        <div class="ad-image bg-gradient d-flex align-items-center justify-content-center">
                            <i class="fas fa-home fa-4x text-white"></i>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">شقة للإيجار - الرياض</h5>
                            <p class="card-text text-muted">شقة 3 غرف في موقع مميز</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="ad-price">2,500 ريال/شهر</span>
                                <small class="text-muted">منذ 3 أيام</small>
                            </div>
                            <a href="{% url 'ads:create' %}" class="btn btn-primary w-100 mt-3">
                                أضف إعلانك هنا
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card ad-card position-relative">
                        <div class="ad-badge">مميز</div>
                        <div class="ad-image bg-gradient d-flex align-items-center justify-content-center">
                            <i class="fas fa-laptop fa-4x text-white"></i>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">لابتوب MacBook Pro</h5>
                            <p class="card-text text-muted">جهاز بحالة ممتازة مع الضمان</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="ad-price">4,200 ريال</span>
                                <small class="text-muted">منذ أسبوع</small>
                            </div>
                            <a href="{% url 'ads:create' %}" class="btn btn-primary w-100 mt-3">
                                أضف إعلانك هنا
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        <div class="text-center mt-5">
            <a href="{% url 'ads:list' %}" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-eye me-2"></i>عرض جميع الإعلانات
            </a>
        </div>
    </div>
</section>

<!-- Statistics Section Responsive -->
<section class="stats-responsive">
    <div class="container-responsive">
        <div class="stats-grid">
            <div class="stat-item-responsive">
                <span class="stat-number" data-count="{{ stats.total_ads|default:1250 }}">0</span>
                <div class="stat-label">إعلان نشط</div>
            </div>
            <div class="stat-item-responsive">
                <span class="stat-number" data-count="{{ stats.total_users|default:5680 }}">0</span>
                <div class="stat-label">مستخدم مسجل</div>
            </div>
            <div class="stat-item-responsive">
                <span class="stat-number" data-count="{{ stats.total_categories|default:12 }}">0</span>
                <div class="stat-label">قسم متنوع</div>
            </div>
            <div class="stat-item-responsive">
                <span class="stat-number" data-count="{{ stats.featured_ads_count|default:89 }}">0</span>
                <div class="stat-label">إعلان مميز</div>
            </div>
        </div>
    </div>
</section>

<!-- Testimonials Section Responsive -->
<section class="testimonials-responsive">
    <div class="container-responsive">
        <h2 class="title-responsive text-center">ماذا يقول عملاؤنا</h2>
        <div class="testimonial-grid">
            <div class="testimonial-item">
                <div class="testimonial-avatar" style="background: linear-gradient(45deg, #667eea, #764ba2); display: flex; align-items: center; justify-content: center; color: white; font-weight: 700; font-size: var(--text-xl);">
                    أ
                </div>
                <h5 class="testimonial-name">أحمد محمد</h5>
                <p class="testimonial-text">"موقع رائع ساعدني في بيع سيارتي بسرعة وبسعر ممتاز. التعامل سهل والواجهة واضحة."</p>
                <div class="testimonial-rating">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                </div>
            </div>
            <div class="testimonial-item">
                <div class="testimonial-avatar" style="background: linear-gradient(45deg, #764ba2, #667eea); display: flex; align-items: center; justify-content: center; color: white; font-weight: 700; font-size: var(--text-xl);">
                    ف
                </div>
                <h5 class="testimonial-name">فاطمة العلي</h5>
                <p class="testimonial-text">"وجدت الشقة المثالية لعائلتي من خلال هذا الموقع. خدمة ممتازة وإعلانات موثوقة."</p>
                <div class="testimonial-rating">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                </div>
            </div>
            <div class="testimonial-item">
                <div class="testimonial-avatar" style="background: linear-gradient(45deg, #667eea, #764ba2); display: flex; align-items: center; justify-content: center; color: white; font-weight: 700; font-size: var(--text-xl);">
                    م
                </div>
                <h5 class="testimonial-name">محمد السعد</h5>
                <p class="testimonial-text">"أفضل موقع للإعلانات المبوبة في المملكة. سهولة في الاستخدام ونتائج سريعة."</p>
                <div class="testimonial-rating">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="py-5" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container text-center text-white">
        <h2 class="mb-4">جاهز لنشر إعلانك؟</h2>
        <p class="lead mb-4">انضم لآلاف المستخدمين الذين يثقون بنا في بيع وشراء منتجاتهم</p>
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="d-flex flex-column flex-md-row gap-3 justify-content-center">
                    <a href="{% url 'ads:create' %}" class="btn btn-light btn-lg px-5">
                        <i class="fas fa-plus me-2"></i>أضف إعلانك مجاناً
                    </a>
                    <a href="{% url 'accounts:register' %}" class="btn btn-outline-light btn-lg px-5">
                        <i class="fas fa-user-plus me-2"></i>إنشاء حساب جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-5 bg-white">
    <div class="container">
        <h2 class="section-title">لماذا تختارنا؟</h2>
        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="text-center p-4">
                    <div class="mb-3">
                        <i class="fas fa-shield-alt fa-3x text-primary"></i>
                    </div>
                    <h4>آمن وموثوق</h4>
                    <p class="text-muted">جميع الإعلانات تخضع للمراجعة لضمان الجودة والأمان</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="text-center p-4">
                    <div class="mb-3">
                        <i class="fas fa-rocket fa-3x text-primary"></i>
                    </div>
                    <h4>سريع وسهل</h4>
                    <p class="text-muted">انشر إعلانك في دقائق واحصل على نتائج فورية</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="text-center p-4">
                    <div class="mb-3">
                        <i class="fas fa-users fa-3x text-primary"></i>
                    </div>
                    <h4>مجتمع كبير</h4>
                    <p class="text-muted">آلاف المستخدمين النشطين يومياً في جميع أنحاء المملكة</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="text-center p-4">
                    <div class="mb-3">
                        <i class="fas fa-mobile-alt fa-3x text-primary"></i>
                    </div>
                    <h4>متوافق مع الجوال</h4>
                    <p class="text-muted">تصميم متجاوب يعمل بسلاسة على جميع الأجهزة</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="text-center p-4">
                    <div class="mb-3">
                        <i class="fas fa-search fa-3x text-primary"></i>
                    </div>
                    <h4>بحث متقدم</h4>
                    <p class="text-muted">أدوات بحث قوية للعثور على ما تريد بسرعة</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="text-center p-4">
                    <div class="mb-3">
                        <i class="fas fa-headset fa-3x text-primary"></i>
                    </div>
                    <h4>دعم فني 24/7</h4>
                    <p class="text-muted">فريق دعم متخصص لمساعدتك في أي وقت</p>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/landing-page.js' %}"></script>
<script>
// Additional inline scripts for specific functionality
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced search form submission
    const searchForm = document.querySelector('.search-form');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            const input = this.querySelector('.search-input');
            if (!input.value.trim()) {
                e.preventDefault();
                input.focus();
                input.style.borderColor = '#dc3545';
                setTimeout(() => {
                    input.style.borderColor = '';
                }, 2000);
            }
        });
    }

    // CTA button click tracking
    document.querySelectorAll('.cta-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });

    // Category card interactions
    document.querySelectorAll('.category-card').forEach(card => {
        card.addEventListener('click', function() {
            const btn = this.querySelector('.category-btn');
            if (btn) {
                btn.click();
            }
        });
    });

    // Testimonials carousel (if needed)
    const testimonials = document.querySelectorAll('.testimonial-card');
    if (testimonials.length > 3) {
        // Add carousel functionality here if needed
    }

    // Lazy loading for images
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
});

// Performance optimization
window.addEventListener('load', function() {
    // Remove loading states
    document.body.classList.add('loaded');

    // Initialize additional features after load
    setTimeout(() => {
        // Add any post-load animations or features
        document.querySelectorAll('.hero-content > *').forEach((el, index) => {
            el.style.animationDelay = `${index * 0.2}s`;
        });
    }, 500);
});
</script>
{% endblock %}

"""
MariaDB 10.4 Compatibility Fix for Django
Fixes the RETURNING clause issue
"""
from django.db.backends.mysql.base import DatabaseWrapper
from django.db.backends.mysql.features import DatabaseFeatures


class MariaDBCompatibleFeatures(DatabaseFeatures):
    """
    Custom database features for MariaDB 10.4 compatibility
    """
    # Disable RETURNING support for MariaDB 10.4
    can_return_columns_from_insert = False
    can_return_rows_from_bulk_insert = False
    
    def __init__(self, connection):
        super().__init__(connection)
        # Force disable RETURNING for all operations
        self.can_return_columns_from_insert = False
        self.can_return_rows_from_bulk_insert = False


class MariaDBCompatibleWrapper(DatabaseWrapper):
    """
    Custom database wrapper for MariaDB 10.4 compatibility
    """
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Override features with our compatible version
        self.features_class = MariaDBCompatibleFeatures
    
    @property
    def features(self):
        if self._features is None:
            self._features = self.features_class(self)
        return self._features


def patch_django_mysql():
    """
    Patch Django MySQL backend for MariaDB 10.4 compatibility
    """
    # Override the default MySQL database wrapper
    import django.db.backends.mysql.base as mysql_base
    mysql_base.DatabaseWrapper = MariaDBCompatibleWrapper
    
    # Also patch the features directly
    original_init = DatabaseFeatures.__init__
    
    def patched_init(self, connection):
        original_init(self, connection)
        # Force disable RETURNING support
        self.can_return_columns_from_insert = False
        self.can_return_rows_from_bulk_insert = False
    
    DatabaseFeatures.__init__ = patched_init


# Apply the patch
patch_django_mysql()

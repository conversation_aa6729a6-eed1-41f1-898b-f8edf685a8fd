-- =====================================================
-- قاعدة بيانات موقع الإعلانات المبوبة
-- Classified Ads Database
-- =====================================================

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS classified_ads_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- استخدام قاعدة البيانات
USE classified_ads_db;

-- =====================================================
-- جدول المستخدمين المخصص
-- =====================================================
CREATE TABLE accounts_customuser (
    id INT AUTO_INCREMENT PRIMARY KEY,
    password VARCHAR(128) NOT NULL,
    last_login DATETIME(6) NULL,
    is_superuser BOOLEAN NOT NULL DEFAULT FALSE,
    username VARCHAR(150) NOT NULL UNIQUE,
    first_name VARCHAR(150) NOT NULL DEFAULT '',
    last_name VARCHAR(150) NOT NULL DEFAULT '',
    email VARCHAR(254) NOT NULL DEFAULT '',
    is_staff BOOLEAN NOT NULL DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    date_joined DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    phone VARCHAR(20) NULL,
    address TEXT NULL,
    profile_image VARCHAR(100) NULL,
    is_verified BOOLEAN NOT NULL DEFAULT FALSE,
    created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    updated_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- جدول أقسام الإعلانات
-- =====================================================
CREATE TABLE ads_category (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT NULL,
    icon VARCHAR(50) NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    updated_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- جدول الإعلانات الرئيسي
-- =====================================================
CREATE TABLE ads_advertisement (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    price DECIMAL(10,2) NULL,
    location VARCHAR(100) NULL,
    phone VARCHAR(20) NULL,
    email VARCHAR(254) NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    is_featured BOOLEAN NOT NULL DEFAULT FALSE,
    views_count INT NOT NULL DEFAULT 0,
    created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    updated_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    expires_at DATETIME(6) NULL,
    category_id INT NOT NULL,
    user_id INT NOT NULL,
    FOREIGN KEY (category_id) REFERENCES ads_category(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES accounts_customuser(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- جدول صور الإعلانات
-- =====================================================
CREATE TABLE ads_adimage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    image VARCHAR(100) NOT NULL,
    is_main BOOLEAN NOT NULL DEFAULT FALSE,
    created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    advertisement_id INT NOT NULL,
    FOREIGN KEY (advertisement_id) REFERENCES ads_advertisement(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- جدول تقارير الإعلانات
-- =====================================================
CREATE TABLE ads_report (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_type VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    is_resolved BOOLEAN NOT NULL DEFAULT FALSE,
    created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    advertisement_id INT NOT NULL,
    user_id INT NOT NULL,
    FOREIGN KEY (advertisement_id) REFERENCES ads_advertisement(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES accounts_customuser(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- جداول Django الأساسية
-- =====================================================

-- جدول أنواع المحتوى
CREATE TABLE django_content_type (
    id INT AUTO_INCREMENT PRIMARY KEY,
    app_label VARCHAR(100) NOT NULL,
    model VARCHAR(100) NOT NULL,
    UNIQUE KEY django_content_type_app_label_model (app_label, model)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الصلاحيات
CREATE TABLE auth_permission (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    content_type_id INT NOT NULL,
    codename VARCHAR(100) NOT NULL,
    UNIQUE KEY auth_permission_content_type_id_codename (content_type_id, codename),
    FOREIGN KEY (content_type_id) REFERENCES django_content_type(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المجموعات
CREATE TABLE auth_group (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(150) NOT NULL UNIQUE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول صلاحيات المجموعات
CREATE TABLE auth_group_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    group_id INT NOT NULL,
    permission_id INT NOT NULL,
    UNIQUE KEY auth_group_permissions_group_id_permission_id (group_id, permission_id),
    FOREIGN KEY (group_id) REFERENCES auth_group(id),
    FOREIGN KEY (permission_id) REFERENCES auth_permission(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول مجموعات المستخدمين
CREATE TABLE accounts_customuser_groups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customuser_id INT NOT NULL,
    group_id INT NOT NULL,
    UNIQUE KEY accounts_customuser_groups_customuser_id_group_id (customuser_id, group_id),
    FOREIGN KEY (customuser_id) REFERENCES accounts_customuser(id),
    FOREIGN KEY (group_id) REFERENCES auth_group(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول صلاحيات المستخدمين
CREATE TABLE accounts_customuser_user_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customuser_id INT NOT NULL,
    permission_id INT NOT NULL,
    UNIQUE KEY accounts_customuser_user_permissions_customuser_id_permission_id (customuser_id, permission_id),
    FOREIGN KEY (customuser_id) REFERENCES accounts_customuser(id),
    FOREIGN KEY (permission_id) REFERENCES auth_permission(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الجلسات
CREATE TABLE django_session (
    session_key VARCHAR(40) NOT NULL PRIMARY KEY,
    session_data LONGTEXT NOT NULL,
    expire_date DATETIME(6) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول سجل الإدارة
CREATE TABLE django_admin_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    action_time DATETIME(6) NOT NULL,
    object_id LONGTEXT NULL,
    object_repr VARCHAR(200) NOT NULL,
    action_flag SMALLINT UNSIGNED NOT NULL,
    change_message LONGTEXT NOT NULL,
    content_type_id INT NULL,
    user_id INT NOT NULL,
    FOREIGN KEY (content_type_id) REFERENCES django_content_type(id),
    FOREIGN KEY (user_id) REFERENCES accounts_customuser(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الهجرات
CREATE TABLE django_migrations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    app VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    applied DATETIME(6) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- الفهارس المحسنة للأداء
-- =====================================================

-- فهارس جدول الإعلانات
CREATE INDEX idx_ads_status ON ads_advertisement(status);
CREATE INDEX idx_ads_category ON ads_advertisement(category_id);
CREATE INDEX idx_ads_user ON ads_advertisement(user_id);
CREATE INDEX idx_ads_created ON ads_advertisement(created_at);
CREATE INDEX idx_ads_featured ON ads_advertisement(is_featured);
CREATE INDEX idx_ads_price ON ads_advertisement(price);
CREATE INDEX idx_ads_location ON ads_advertisement(location);

-- فهارس مركبة للبحث المتقدم
CREATE INDEX idx_ads_status_category ON ads_advertisement(status, category_id);
CREATE INDEX idx_ads_status_featured ON ads_advertisement(status, is_featured);
CREATE INDEX idx_ads_status_created ON ads_advertisement(status, created_at);

-- فهارس جدول الأقسام
CREATE INDEX idx_category_active ON ads_category(is_active);

-- فهارس جدول المستخدمين
CREATE INDEX idx_user_active ON accounts_customuser(is_active);
CREATE INDEX idx_user_verified ON accounts_customuser(is_verified);
CREATE INDEX idx_user_email ON accounts_customuser(email);

-- فهارس جدول التقارير
CREATE INDEX idx_reports_resolved ON ads_report(is_resolved);
CREATE INDEX idx_reports_created ON ads_report(created_at);

-- فهارس جدول صور الإعلانات
CREATE INDEX idx_images_main ON ads_adimage(is_main);
CREATE INDEX idx_images_ad ON ads_adimage(advertisement_id);

-- فهارس جدول الجلسات
CREATE INDEX idx_session_expire ON django_session(expire_date);

-- =====================================================
-- البيانات الأساسية
-- =====================================================

-- إدراج أنواع المحتوى
INSERT INTO django_content_type (app_label, model) VALUES
('accounts', 'customuser'),
('ads', 'category'),
('ads', 'advertisement'),
('ads', 'adimage'),
('ads', 'report'),
('auth', 'permission'),
('auth', 'group'),
('contenttypes', 'contenttype'),
('sessions', 'session'),
('admin', 'logentry');

-- إدراج الصلاحيات الأساسية
INSERT INTO auth_permission (name, content_type_id, codename) VALUES
('Can add user', 1, 'add_customuser'),
('Can change user', 1, 'change_customuser'),
('Can delete user', 1, 'delete_customuser'),
('Can view user', 1, 'view_customuser'),
('Can add category', 2, 'add_category'),
('Can change category', 2, 'change_category'),
('Can delete category', 2, 'delete_category'),
('Can view category', 2, 'view_category'),
('Can add advertisement', 3, 'add_advertisement'),
('Can change advertisement', 3, 'change_advertisement'),
('Can delete advertisement', 3, 'delete_advertisement'),
('Can view advertisement', 3, 'view_advertisement'),
('Can add ad image', 4, 'add_adimage'),
('Can change ad image', 4, 'change_adimage'),
('Can delete ad image', 4, 'delete_adimage'),
('Can view ad image', 4, 'view_adimage'),
('Can add report', 5, 'add_report'),
('Can change report', 5, 'change_report'),
('Can delete report', 5, 'delete_report'),
('Can view report', 5, 'view_report');

-- إدراج المستخدم الإداري
-- كلمة المرور: admin123 (مشفرة بـ Django)
INSERT INTO accounts_customuser (
    password,
    is_superuser,
    username,
    first_name,
    last_name,
    email,
    is_staff,
    is_active,
    date_joined,
    is_verified
) VALUES (
    'pbkdf2_sha256$600000$randomsalt123$hashedpassword123',
    TRUE,
    'admin',
    'مدير',
    'النظام',
    '<EMAIL>',
    TRUE,
    TRUE,
    NOW(),
    TRUE
);

-- إدراج مستخدمين تجريبيين
-- كلمة المرور: password123 (مشفرة بـ Django)
INSERT INTO accounts_customuser (
    password,
    is_superuser,
    username,
    first_name,
    last_name,
    email,
    is_staff,
    is_active,
    date_joined,
    phone,
    is_verified
) VALUES
(
    'pbkdf2_sha256$600000$randomsalt456$hashedpassword456',
    FALSE,
    'user1',
    'أحمد',
    'محمد',
    '<EMAIL>',
    FALSE,
    TRUE,
    NOW(),
    '**********',
    TRUE
),
(
    'pbkdf2_sha256$600000$randomsalt789$hashedpassword789',
    FALSE,
    'user2',
    'فاطمة',
    'علي',
    '<EMAIL>',
    FALSE,
    TRUE,
    NOW(),
    '**********',
    TRUE
),
(
    'pbkdf2_sha256$600000$randomsalt101$hashedpassword101',
    FALSE,
    'user3',
    'خالد',
    'السعد',
    '<EMAIL>',
    FALSE,
    TRUE,
    NOW(),
    '**********',
    TRUE
);

-- إدراج أقسام الإعلانات
INSERT INTO ads_category (name, description, icon, is_active) VALUES
('عقارات', 'شقق، فيلات، أراضي للبيع والإيجار', 'fas fa-home', TRUE),
('سيارات', 'سيارات جديدة ومستعملة للبيع', 'fas fa-car', TRUE),
('وظائف', 'فرص عمل في جميع المجالات', 'fas fa-briefcase', TRUE),
('دورات تدريبية', 'دورات ودروس في مختلف المجالات', 'fas fa-graduation-cap', TRUE),
('إلكترونيات', 'أجهزة إلكترونية ومعدات تقنية', 'fas fa-laptop', TRUE),
('أثاث ومنزل', 'أثاث وأدوات منزلية', 'fas fa-couch', TRUE),
('خدمات', 'خدمات متنوعة', 'fas fa-tools', TRUE),
('أزياء وموضة', 'ملابس وإكسسوارات', 'fas fa-tshirt', TRUE);

-- إدراج إعلانات تجريبية
INSERT INTO ads_advertisement (
    title,
    description,
    price,
    location,
    phone,
    status,
    is_featured,
    category_id,
    user_id,
    views_count
) VALUES
(
    'شقة للبيع في الرياض',
    'شقة مميزة للبيع في حي الملز، 3 غرف نوم، 2 حمام، صالة واسعة، مطبخ مجهز. الشقة في الدور الثالث مع مصعد، موقف سيارة، قريبة من الخدمات.',
    450000.00,
    'الرياض - حي الملز',
    '**********',
    'approved',
    TRUE,
    1, -- عقارات
    2, -- user1
    25
),
(
    'سيارة تويوتا كامري 2020',
    'سيارة تويوتا كامري موديل 2020، حالة ممتازة، قطعت 45000 كم فقط. صيانة دورية منتظمة، لون أبيض، فحص شامل متاح.',
    85000.00,
    'جدة',
    '**********',
    'approved',
    TRUE,
    2, -- سيارات
    3, -- user2
    18
),
(
    'مطلوب مطور ويب',
    'شركة تقنية رائدة تبحث عن مطور ويب خبرة 3 سنوات في Django و React. راتب مجزي، بيئة عمل ممتازة، فرص تطوير مهني.',
    NULL,
    'الرياض',
    NULL,
    'approved',
    FALSE,
    3, -- وظائف
    1, -- admin
    42
),
(
    'لابتوب ديل للبيع',
    'لابتوب ديل Inspiron 15، معالج Intel i7، ذاكرة 16GB، قرص صلب SSD 512GB. حالة ممتازة، استخدام شخصي خفيف.',
    2500.00,
    'الدمام',
    '**********',
    'approved',
    FALSE,
    5, -- إلكترونيات
    4, -- user3
    12
),
(
    'فيلا للإيجار في جدة',
    'فيلا مفروشة للإيجار في حي الروضة، 4 غرف نوم، 3 حمامات، مجلس، صالة، مطبخ مجهز، حديقة، موقف سيارتين.',
    8000.00,
    'جدة - حي الروضة',
    '**********',
    'approved',
    TRUE,
    1, -- عقارات
    2, -- user1
    31
);

-- إدراج سجلات الهجرات
INSERT INTO django_migrations (app, name, applied) VALUES
('contenttypes', '0001_initial', NOW()),
('auth', '0001_initial', NOW()),
('accounts', '0001_initial', NOW()),
('ads', '0001_initial', NOW()),
('admin', '0001_initial', NOW()),
('sessions', '0001_initial', NOW());

-- =====================================================
-- تحديث العدادات التلقائية
-- =====================================================
ALTER TABLE accounts_customuser AUTO_INCREMENT = 5;
ALTER TABLE ads_category AUTO_INCREMENT = 9;
ALTER TABLE ads_advertisement AUTO_INCREMENT = 6;
ALTER TABLE django_content_type AUTO_INCREMENT = 11;
ALTER TABLE auth_permission AUTO_INCREMENT = 21;
ALTER TABLE django_migrations AUTO_INCREMENT = 7;

-- =====================================================
-- ملاحظات مهمة
-- =====================================================
/*
1. كلمات المرور المدرجة هنا هي أمثلة وليست مشفرة بشكل صحيح
2. يجب تغيير كلمات المرور بعد الاستيراد
3. المستخدم الإداري: admin / admin123
4. المستخدمون التجريبيون: user1, user2, user3 / password123
5. تأكد من تشغيل Django بعد الاستيراد لتحديث كلمات المرور
*/

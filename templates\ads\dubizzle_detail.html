{% extends 'base.html' %}
{% load static %}

{% block title %}{{ ad.title }} - إعلاناتي{% endblock %}

{% block extra_css %}
<link href="{% static 'css/dubizzle-style.css' %}" rel="stylesheet">
<style>
/* Ad Detail Page Styles */
.ad-detail-page {
    background: var(--neutral-50);
    min-height: 100vh;
}

.detail-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--space-8) var(--space-4);
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-8);
}

/* Breadcrumb */
.breadcrumb {
    background: white;
    padding: var(--space-4) 0;
    border-bottom: 1px solid var(--neutral-200);
}

.breadcrumb .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--font-size-sm);
}

.breadcrumb a {
    color: var(--dubizzle-primary);
    text-decoration: none;
}

.breadcrumb span {
    color: var(--neutral-500);
}

/* Main Content */
.ad-main-content {
    background: white;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

/* Image Gallery */
.image-gallery {
    position: relative;
    background: var(--neutral-100);
}

.main-image-container {
    position: relative;
    height: 400px;
    overflow: hidden;
}

.main-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: zoom-in;
    transition: var(--transition-normal);
}

.main-image:hover {
    transform: scale(1.05);
}

.image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--neutral-200);
    color: var(--neutral-400);
    font-size: var(--font-size-5xl);
}

.gallery-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0,0,0,0.5);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    transition: var(--transition-fast);
}

.gallery-nav:hover {
    background: rgba(0,0,0,0.7);
}

.gallery-nav.prev {
    right: var(--space-4);
}

.gallery-nav.next {
    left: var(--space-4);
}

.image-counter {
    position: absolute;
    bottom: var(--space-4);
    right: var(--space-4);
    background: rgba(0,0,0,0.7);
    color: white;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
}

.thumbnail-gallery {
    display: flex;
    gap: var(--space-2);
    padding: var(--space-4);
    overflow-x: auto;
}

.thumbnail {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: var(--radius-md);
    cursor: pointer;
    border: 2px solid transparent;
    transition: var(--transition-fast);
}

.thumbnail.active {
    border-color: var(--dubizzle-primary);
}

.thumbnail:hover {
    border-color: var(--dubizzle-primary-light);
}

/* Ad Content */
.ad-content {
    padding: var(--space-8);
}

.ad-header {
    margin-bottom: var(--space-6);
}

.ad-badges {
    display: flex;
    gap: var(--space-2);
    margin-bottom: var(--space-4);
}

.ad-badge {
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    color: white;
}

.ad-badge.featured {
    background: var(--dubizzle-primary);
}

.ad-badge.urgent {
    background: var(--error-500);
    animation: pulse 2s infinite;
}

.ad-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--neutral-800);
    margin-bottom: var(--space-4);
    line-height: 1.3;
}

.ad-price {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    color: var(--dubizzle-primary);
    margin-bottom: var(--space-6);
}

.ad-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
    margin-bottom: var(--space-8);
    padding: var(--space-6);
    background: var(--neutral-50);
    border-radius: var(--radius-lg);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.meta-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--dubizzle-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
}

.meta-content h4 {
    font-size: var(--font-size-sm);
    color: var(--neutral-500);
    margin-bottom: var(--space-1);
    font-weight: 500;
}

.meta-content p {
    font-size: var(--font-size-base);
    color: var(--neutral-800);
    font-weight: 600;
}

.ad-description {
    margin-bottom: var(--space-8);
}

.description-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--space-4);
    color: var(--neutral-800);
}

.description-text {
    font-size: var(--font-size-lg);
    line-height: 1.8;
    color: var(--neutral-700);
    white-space: pre-wrap;
}

/* Sidebar */
.ad-sidebar {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.sidebar-card {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-md);
}

.card-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-4);
    color: var(--neutral-800);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

/* Contact Card */
.contact-card {
    text-align: center;
}

.seller-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--dubizzle-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin: 0 auto var(--space-4);
}

.seller-name {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--space-2);
    color: var(--neutral-800);
}

.seller-rating {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    margin-bottom: var(--space-6);
    color: var(--warning-500);
}

.contact-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.contact-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-normal);
}

.contact-btn.primary {
    background: var(--dubizzle-primary);
    color: white;
}

.contact-btn.primary:hover {
    background: var(--dubizzle-primary-dark);
    transform: translateY(-2px);
}

.contact-btn.secondary {
    background: var(--neutral-100);
    color: var(--neutral-700);
    border: 1px solid var(--neutral-300);
}

.contact-btn.secondary:hover {
    background: var(--neutral-200);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: var(--space-3);
}

.action-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3);
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-lg);
    background: white;
    color: var(--neutral-700);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
}

.action-btn:hover {
    border-color: var(--dubizzle-primary);
    color: var(--dubizzle-primary);
}

/* Similar Ads */
.similar-ads {
    margin-top: var(--space-12);
    padding: var(--space-8) 0;
    background: white;
}

.similar-ads .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.similar-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--space-8);
    color: var(--neutral-800);
    text-align: center;
}

.similar-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-6);
}

.similar-card {
    background: white;
    border: 1px solid var(--neutral-200);
    border-radius: var(--radius-xl);
    overflow: hidden;
    transition: var(--transition-normal);
    text-decoration: none;
    color: inherit;
}

.similar-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--dubizzle-primary);
}

.similar-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
    background: var(--neutral-200);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--neutral-400);
    font-size: var(--font-size-2xl);
}

.similar-content {
    padding: var(--space-4);
}

.similar-title-text {
    font-size: var(--font-size-base);
    font-weight: 600;
    margin-bottom: var(--space-2);
    color: var(--neutral-800);
}

.similar-price {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--dubizzle-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .detail-container {
        grid-template-columns: 1fr;
        gap: var(--space-4);
        padding: var(--space-4);
    }
    
    .ad-sidebar {
        order: -1;
    }
    
    .main-image-container {
        height: 250px;
    }
    
    .ad-content {
        padding: var(--space-4);
    }
    
    .ad-title {
        font-size: var(--font-size-2xl);
    }
    
    .ad-price {
        font-size: var(--font-size-3xl);
    }
    
    .ad-meta {
        grid-template-columns: 1fr;
        gap: var(--space-3);
        padding: var(--space-4);
    }
    
    .contact-buttons {
        flex-direction: row;
    }
    
    .similar-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--space-4);
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Include Dubizzle Header -->
{% include 'includes/dubizzle_header.html' %}

<div class="ad-detail-page">
    <!-- Breadcrumb -->
    <div class="breadcrumb">
        <div class="container">
            <a href="{% url 'home' %}">الرئيسية</a>
            <span>/</span>
            <a href="{% url 'ads:list' %}">الإعلانات</a>
            {% if ad.category %}
                <span>/</span>
                <a href="{% url 'ads:search' %}?category={{ ad.category.id }}">{{ ad.category.name }}</a>
            {% endif %}
            <span>/</span>
            <span>{{ ad.title|truncatechars:30 }}</span>
        </div>
    </div>

    <!-- Main Content -->
    <div class="detail-container">
        <!-- Main Content -->
        <div class="ad-main-content">
            <!-- Image Gallery -->
            <div class="image-gallery">
                <div class="main-image-container">
                    {% if ad.images.exists %}
                        <img src="{{ ad.images.first.image.url }}" class="main-image" alt="{{ ad.title }}" id="main-image">
                        {% if ad.images.count > 1 %}
                            <button class="gallery-nav prev" onclick="previousImage()">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                            <button class="gallery-nav next" onclick="nextImage()">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                        {% endif %}
                        <div class="image-counter">
                            <span id="current-image">1</span> / {{ ad.images.count }}
                        </div>
                    {% else %}
                        <div class="image-placeholder">
                            <i class="fas fa-image"></i>
                        </div>
                    {% endif %}
                </div>
                
                {% if ad.images.count > 1 %}
                <div class="thumbnail-gallery">
                    {% for image in ad.images.all %}
                        <img src="{{ image.image.url }}" class="thumbnail {% if forloop.first %}active{% endif %}" 
                             alt="{{ ad.title }}" onclick="showImage({{ forloop.counter0 }})">
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <!-- Ad Content -->
            <div class="ad-content">
                <!-- Header -->
                <div class="ad-header">
                    <div class="ad-badges">
                        {% if ad.is_featured %}
                            <span class="ad-badge featured">
                                <i class="fas fa-star"></i> مميز
                            </span>
                        {% endif %}
                        {% if ad.is_urgent %}
                            <span class="ad-badge urgent">
                                <i class="fas fa-bolt"></i> عاجل
                            </span>
                        {% endif %}
                    </div>
                    
                    <h1 class="ad-title">{{ ad.title }}</h1>
                    
                    {% if ad.price %}
                        <div class="ad-price">{{ ad.price|floatformat:0 }} ريال</div>
                    {% else %}
                        <div class="ad-price">السعر غير محدد</div>
                    {% endif %}
                </div>

                <!-- Meta Information -->
                <div class="ad-meta">
                    <div class="meta-item">
                        <div class="meta-icon">
                            <i class="fas fa-tag"></i>
                        </div>
                        <div class="meta-content">
                            <h4>القسم</h4>
                            <p>{{ ad.category.name|default:"غير محدد" }}</p>
                        </div>
                    </div>
                    
                    <div class="meta-item">
                        <div class="meta-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="meta-content">
                            <h4>الموقع</h4>
                            <p>{{ ad.location|default:"غير محدد" }}</p>
                        </div>
                    </div>
                    
                    <div class="meta-item">
                        <div class="meta-icon">
                            <i class="fas fa-calendar"></i>
                        </div>
                        <div class="meta-content">
                            <h4>تاريخ النشر</h4>
                            <p>{{ ad.created_at|date:"d/m/Y" }}</p>
                        </div>
                    </div>
                    
                    <div class="meta-item">
                        <div class="meta-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="meta-content">
                            <h4>المشاهدات</h4>
                            <p>{{ ad.views_count|default:0 }}</p>
                        </div>
                    </div>
                </div>

                <!-- Description -->
                <div class="ad-description">
                    <h3 class="description-title">وصف الإعلان</h3>
                    <p class="description-text">{{ ad.description|linebreaks }}</p>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="ad-sidebar">
            <!-- Contact Card -->
            <div class="sidebar-card contact-card">
                <h3 class="card-title">
                    <i class="fas fa-user"></i>
                    معلومات البائع
                </h3>
                
                <div class="seller-avatar">
                    {{ ad.user.first_name.0|default:ad.user.username.0|upper }}
                </div>
                
                <h4 class="seller-name">{{ ad.user.get_full_name|default:ad.user.username }}</h4>
                
                <div class="seller-rating">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star-half-alt"></i>
                    <span>(4.5)</span>
                </div>
                
                <div class="contact-buttons">
                    <a href="tel:{{ ad.phone|default:'#' }}" class="contact-btn primary">
                        <i class="fas fa-phone"></i>
                        اتصل الآن
                    </a>
                    <a href="#" class="contact-btn secondary" onclick="sendMessage()">
                        <i class="fas fa-envelope"></i>
                        أرسل رسالة
                    </a>
                </div>
            </div>

            <!-- Actions Card -->
            <div class="sidebar-card">
                <h3 class="card-title">
                    <i class="fas fa-cog"></i>
                    إجراءات
                </h3>
                
                <div class="action-buttons">
                    <a href="#" class="action-btn" onclick="toggleFavorite()">
                        <i class="fas fa-heart"></i>
                        حفظ
                    </a>
                    <a href="#" class="action-btn" onclick="shareAd()">
                        <i class="fas fa-share"></i>
                        مشاركة
                    </a>
                </div>
                
                <div style="margin-top: var(--space-4);">
                    <a href="#" class="action-btn" onclick="reportAd()" style="color: var(--error-500); border-color: var(--error-500);">
                        <i class="fas fa-flag"></i>
                        إبلاغ عن مشكلة
                    </a>
                </div>
            </div>

            <!-- Safety Tips -->
            <div class="sidebar-card">
                <h3 class="card-title">
                    <i class="fas fa-shield-alt"></i>
                    نصائح الأمان
                </h3>
                
                <ul style="list-style: none; padding: 0; margin: 0;">
                    <li style="margin-bottom: var(--space-3); display: flex; align-items: flex-start; gap: var(--space-2);">
                        <i class="fas fa-check-circle" style="color: var(--success-500); margin-top: 2px;"></i>
                        <span style="font-size: var(--font-size-sm);">تأكد من المنتج قبل الدفع</span>
                    </li>
                    <li style="margin-bottom: var(--space-3); display: flex; align-items: flex-start; gap: var(--space-2);">
                        <i class="fas fa-check-circle" style="color: var(--success-500); margin-top: 2px;"></i>
                        <span style="font-size: var(--font-size-sm);">التقي في مكان عام آمن</span>
                    </li>
                    <li style="margin-bottom: var(--space-3); display: flex; align-items: flex-start; gap: var(--space-2);">
                        <i class="fas fa-check-circle" style="color: var(--success-500); margin-top: 2px;"></i>
                        <span style="font-size: var(--font-size-sm);">لا تدفع مقدماً</span>
                    </li>
                    <li style="display: flex; align-items: flex-start; gap: var(--space-2);">
                        <i class="fas fa-check-circle" style="color: var(--success-500); margin-top: 2px;"></i>
                        <span style="font-size: var(--font-size-sm);">تحقق من هوية البائع</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Similar Ads -->
    {% if similar_ads %}
    <div class="similar-ads">
        <div class="container">
            <h2 class="similar-title">إعلانات مشابهة</h2>
            
            <div class="similar-grid">
                {% for similar_ad in similar_ads %}
                <a href="{% url 'ads:detail' similar_ad.pk %}" class="similar-card">
                    {% if similar_ad.images.exists %}
                        <img src="{{ similar_ad.images.first.image.url }}" class="similar-image" alt="{{ similar_ad.title }}">
                    {% else %}
                        <div class="similar-image">
                            <i class="fas fa-image"></i>
                        </div>
                    {% endif %}
                    
                    <div class="similar-content">
                        <h4 class="similar-title-text">{{ similar_ad.title|truncatechars:40 }}</h4>
                        {% if similar_ad.price %}
                            <p class="similar-price">{{ similar_ad.price|floatformat:0 }} ريال</p>
                        {% else %}
                            <p class="similar-price">السعر غير محدد</p>
                        {% endif %}
                    </div>
                </a>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Image gallery functionality
    const images = [
        {% for image in ad.images.all %}
            "{{ image.image.url }}"{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];
    
    let currentImageIndex = 0;
    
    window.showImage = function(index) {
        if (images.length === 0) return;
        
        currentImageIndex = index;
        const mainImage = document.getElementById('main-image');
        const currentCounter = document.getElementById('current-image');
        const thumbnails = document.querySelectorAll('.thumbnail');
        
        if (mainImage) {
            mainImage.src = images[index];
        }
        
        if (currentCounter) {
            currentCounter.textContent = index + 1;
        }
        
        // Update active thumbnail
        thumbnails.forEach((thumb, i) => {
            thumb.classList.toggle('active', i === index);
        });
    };
    
    window.nextImage = function() {
        if (images.length === 0) return;
        currentImageIndex = (currentImageIndex + 1) % images.length;
        showImage(currentImageIndex);
    };
    
    window.previousImage = function() {
        if (images.length === 0) return;
        currentImageIndex = (currentImageIndex - 1 + images.length) % images.length;
        showImage(currentImageIndex);
    };
    
    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowLeft') {
            nextImage();
        } else if (e.key === 'ArrowRight') {
            previousImage();
        }
    });
    
    // Image zoom functionality
    const mainImage = document.getElementById('main-image');
    if (mainImage) {
        mainImage.addEventListener('click', function() {
            // Simple zoom implementation
            if (this.style.transform === 'scale(2)') {
                this.style.transform = 'scale(1)';
                this.style.cursor = 'zoom-in';
            } else {
                this.style.transform = 'scale(2)';
                this.style.cursor = 'zoom-out';
            }
        });
    }
});

// Action functions
function toggleFavorite() {
    // Implement favorite functionality
    const btn = event.target.closest('.action-btn');
    const icon = btn.querySelector('i');
    
    if (icon.classList.contains('fas')) {
        icon.classList.remove('fas');
        icon.classList.add('far');
        btn.style.color = 'var(--neutral-700)';
    } else {
        icon.classList.remove('far');
        icon.classList.add('fas');
        btn.style.color = 'var(--error-500)';
    }
}

function shareAd() {
    if (navigator.share) {
        navigator.share({
            title: '{{ ad.title }}',
            text: '{{ ad.description|truncatechars:100 }}',
            url: window.location.href
        });
    } else {
        // Fallback
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('تم نسخ رابط الإعلان');
        });
    }
}

function sendMessage() {
    // Implement messaging functionality
    alert('سيتم تطوير نظام الرسائل قريباً');
}

function reportAd() {
    // Implement reporting functionality
    if (confirm('هل تريد الإبلاغ عن هذا الإعلان؟')) {
        alert('تم إرسال البلاغ. شكراً لك.');
    }
}

// View counter (increment on page load)
fetch(`/ads/{{ ad.pk }}/view/`, {
    method: 'POST',
    headers: {
        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || '',
        'Content-Type': 'application/json',
    },
}).catch(() => {
    // Ignore errors for view counting
});
</script>
{% endblock %}

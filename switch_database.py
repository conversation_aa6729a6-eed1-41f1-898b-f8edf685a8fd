#!/usr/bin/env python
"""
Script to switch between SQLite and MySQL databases
"""
import os
import sys
import subprocess

def switch_to_sqlite():
    """التبديل إلى SQLite"""
    print("🔄 التبديل إلى قاعدة بيانات SQLite...")
    
    # إزالة متغير البيئة
    if 'USE_MYSQL' in os.environ:
        del os.environ['USE_MYSQL']
    
    # تطبيق الهجرات
    print("📦 تطبيق الهجرات على SQLite...")
    result = subprocess.run(['python', 'manage.py', 'migrate'], 
                          capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ تم التبديل إلى SQLite بنجاح!")
        return True
    else:
        print(f"❌ فشل في التبديل إلى SQLite: {result.stderr}")
        return False

def switch_to_mysql():
    """التبديل إلى MySQL"""
    print("🔄 التبديل إلى قاعدة بيانات MySQL...")
    
    # تعيين متغير البيئة
    os.environ['USE_MYSQL'] = 'true'
    
    # التحقق من الاتصال بـ MySQL
    try:
        import mysql.connector
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='classified_ads_db'
        )
        
        if connection.is_connected():
            print("✅ تم الاتصال بـ MySQL بنجاح!")
            connection.close()
            
            # تطبيق الهجرات
            print("📦 تطبيق الهجرات على MySQL...")
            result = subprocess.run(['python', 'manage.py', 'migrate'], 
                                  capture_output=True, text=True, 
                                  env=dict(os.environ, USE_MYSQL='true'))
            
            if result.returncode == 0:
                print("✅ تم التبديل إلى MySQL بنجاح!")
                return True
            else:
                print(f"❌ فشل في تطبيق الهجرات على MySQL: {result.stderr}")
                return False
        
    except Exception as e:
        print(f"❌ فشل الاتصال بـ MySQL: {e}")
        print("💡 تأكد من:")
        print("- تشغيل خدمة MySQL")
        print("- وجود قاعدة البيانات classified_ads_db")
        return False

def export_data_from_sqlite():
    """تصدير البيانات من SQLite"""
    print("📤 تصدير البيانات من SQLite...")
    
    # تصدير البيانات
    result = subprocess.run(['python', 'manage.py', 'dumpdata', 
                           '--natural-foreign', '--natural-primary',
                           '--exclude=contenttypes', '--exclude=auth.permission',
                           '--output=data_backup.json'], 
                          capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ تم تصدير البيانات إلى data_backup.json")
        return True
    else:
        print(f"❌ فشل في تصدير البيانات: {result.stderr}")
        return False

def import_data_to_mysql():
    """استيراد البيانات إلى MySQL"""
    print("📥 استيراد البيانات إلى MySQL...")
    
    # التحقق من وجود ملف البيانات
    if not os.path.exists('data_backup.json'):
        print("⚠️ لا يوجد ملف بيانات للاستيراد")
        return False
    
    # استيراد البيانات
    result = subprocess.run(['python', 'manage.py', 'loaddata', 'data_backup.json'], 
                          capture_output=True, text=True,
                          env=dict(os.environ, USE_MYSQL='true'))
    
    if result.returncode == 0:
        print("✅ تم استيراد البيانات إلى MySQL")
        return True
    else:
        print(f"❌ فشل في استيراد البيانات: {result.stderr}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔄 أداة تبديل قواعد البيانات")
    print("=" * 40)
    
    if len(sys.argv) < 2:
        print("الاستخدام:")
        print("python switch_database.py sqlite    # للتبديل إلى SQLite")
        print("python switch_database.py mysql     # للتبديل إلى MySQL")
        print("python switch_database.py migrate   # لنقل البيانات من SQLite إلى MySQL")
        return
    
    command = sys.argv[1].lower()
    
    if command == 'sqlite':
        switch_to_sqlite()
        
    elif command == 'mysql':
        switch_to_mysql()
        
    elif command == 'migrate':
        print("🚀 نقل البيانات من SQLite إلى MySQL...")
        
        # 1. تصدير البيانات من SQLite
        if not export_data_from_sqlite():
            return
        
        # 2. التبديل إلى MySQL
        if not switch_to_mysql():
            return
        
        # 3. استيراد البيانات إلى MySQL
        if import_data_to_mysql():
            print("🎉 تم نقل البيانات بنجاح!")
        
    else:
        print("❌ أمر غير صحيح. استخدم: sqlite, mysql, أو migrate")

if __name__ == '__main__':
    main()

# موقع الإعلانات المبوبة

موقع إعلانات مبوبة شامل مبني باستخدام Django مع لوحة إدارة متقدمة.

## المميزات

### للمستخدمين
- ✅ تسجيل الدخول والتسجيل
- ✅ إنشاء وإدارة الإعلانات
- ✅ تصفح الإعلانات حسب الأقسام
- ✅ البحث المتقدم والتصفية
- ✅ رفع الصور للإعلانات
- ✅ الإبلاغ عن الإعلانات المخالفة
- ✅ ملف شخصي مخصص
- ✅ واجهة متجاوبة (Bootstrap)

### للإدارة
- ✅ لوحة تحكم شاملة
- ✅ إدارة المستخدمين
- ✅ مراجعة وموافقة الإعلانات
- ✅ إدارة الأقسام
- ✅ مراجعة التقارير
- ✅ إحصائيات مفصلة
- ✅ تمييز الإعلانات

## التقنيات المستخدمة

- **Backend**: Django 5.2.4
- **Database**: SQLite (للتطوير) / MySQL (للإنتاج)
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Icons**: Font Awesome 6
- **Forms**: Django Crispy Forms
- **Images**: Pillow

## التثبيت والتشغيل

### المتطلبات
- Python 3.8+
- pip

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd classified_ads_site
```

2. **إنشاء بيئة افتراضية**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate  # Windows
```

3. **تثبيت المتطلبات**
```bash
pip install -r requirements.txt
```

4. **إعداد قاعدة البيانات**
```bash
python manage.py migrate
```

5. **إنشاء مستخدم إداري**
```bash
python manage.py createsuperuser
```

6. **إنشاء بيانات تجريبية (اختياري)**
```bash
python create_sample_data.py
```

7. **تشغيل الخادم**
```bash
python manage.py runserver
```

8. **فتح الموقع**
   - الموقع الرئيسي: http://127.0.0.1:8000/
   - لوحة الإدارة: http://127.0.0.1:8000/admin-panel/
   - إدارة Django: http://127.0.0.1:8000/admin/

## إعداد MySQL (للإنتاج)

1. **إنشاء قاعدة البيانات**
```sql
CREATE DATABASE classified_ads_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. **تحديث إعدادات قاعدة البيانات في `settings.py`**
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'classified_ads_db',
        'USER': 'your_username',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'charset': 'utf8mb4',
        },
    }
}
```

## هيكل المشروع

```
classified_ads_site/
├── accounts/           # تطبيق إدارة المستخدمين
├── ads/               # تطبيق الإعلانات
├── admin_panel/       # تطبيق لوحة الإدارة
├── templates/         # قوالب HTML
├── static/           # ملفات CSS/JS/Images
├── media/            # ملفات المستخدمين المرفوعة
├── classified_ads_site/  # إعدادات المشروع
└── manage.py
```

## الحسابات التجريبية

بعد تشغيل `create_sample_data.py`:

- **المدير**: admin / admin123
- **مستخدم 1**: user1 / password123
- **مستخدم 2**: user2 / password123
- **مستخدم 3**: user3 / password123

## الصفحات الرئيسية

- `/` - الصفحة الرئيسية
- `/ads/` - جميع الإعلانات
- `/ads/search/` - البحث في الإعلانات
- `/ads/create/` - إضافة إعلان جديد
- `/accounts/login/` - تسجيل الدخول
- `/accounts/register/` - التسجيل
- `/accounts/profile/` - الملف الشخصي
- `/admin-panel/` - لوحة الإدارة

## المساهمة

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى Branch (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى فتح issue في GitHub.

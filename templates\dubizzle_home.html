{% extends 'base.html' %}
{% load static %}

{% block title %}إعلاناتي - أفضل موقع للإعلانات المبوبة في المملكة{% endblock %}

{% block extra_css %}
<link href="{% static 'css/dubizzle-style.css' %}" rel="stylesheet">
<style>
/* Hero Section Styles */
.dubizzle-hero {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 50%, #ff6b35 100%);
    position: relative;
    padding: var(--space-20) 0;
    overflow: hidden;
    min-height: 500px;
    display: flex;
    align-items: center;
}

.dubizzle-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hero-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23hero-pattern)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateX(0px); }
    50% { transform: translateX(20px); }
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    margin-bottom: var(--space-6);
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    line-height: 1.2;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-8);
    opacity: 0.95;
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: var(--space-8);
    margin-top: var(--space-8);
    flex-wrap: wrap;
}

.hero-stat {
    text-align: center;
    background: rgba(255,255,255,0.1);
    padding: var(--space-4) var(--space-6);
    border-radius: var(--radius-xl);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.hero-stat-number {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--space-2);
}

.hero-stat-label {
    font-size: var(--font-size-base);
    opacity: 0.9;
}

/* Categories Grid */
.categories-section {
    padding: var(--space-20) 0;
    background: white;
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-16);
}

.section-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--neutral-800);
    margin-bottom: var(--space-4);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--neutral-600);
    max-width: 600px;
    margin: 0 auto;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-6);
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.category-card {
    background: white;
    border: 1px solid var(--neutral-200);
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    text-align: center;
    transition: var(--transition-normal);
    text-decoration: none;
    color: inherit;
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--dubizzle-primary);
    transform: scaleX(0);
    transition: var(--transition-normal);
}

.category-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--dubizzle-primary);
}

.category-card:hover::before {
    transform: scaleX(1);
}

.category-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--dubizzle-primary), var(--dubizzle-primary-light));
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-6);
    font-size: var(--font-size-3xl);
    color: white;
    box-shadow: var(--shadow-lg);
}

.category-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--space-3);
    color: var(--neutral-800);
}

.category-count {
    font-size: var(--font-size-base);
    color: var(--neutral-600);
    margin-bottom: var(--space-4);
}

.category-link {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    color: var(--dubizzle-primary);
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition-fast);
}

.category-link:hover {
    gap: var(--space-3);
}

/* Featured Ads Section */
.featured-section {
    padding: var(--space-20) 0;
    background: var(--neutral-50);
}

.ads-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.ad-card {
    background: white;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    text-decoration: none;
    color: inherit;
    position: relative;
}

.ad-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.ad-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background: var(--neutral-200);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--neutral-400);
    font-size: var(--font-size-3xl);
}

.ad-badge {
    position: absolute;
    top: var(--space-3);
    right: var(--space-3);
    background: var(--dubizzle-primary);
    color: white;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    z-index: 2;
}

.ad-content {
    padding: var(--space-6);
}

.ad-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-3);
    color: var(--neutral-800);
    line-height: 1.4;
}

.ad-description {
    color: var(--neutral-600);
    margin-bottom: var(--space-4);
    line-height: 1.5;
}

.ad-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--space-4);
    border-top: 1px solid var(--neutral-200);
}

.ad-price {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--dubizzle-primary);
}

.ad-time {
    font-size: var(--font-size-sm);
    color: var(--neutral-500);
}

/* CTA Section */
.cta-section {
    padding: var(--space-20) 0;
    background: var(--neutral-800);
    color: white;
    text-align: center;
}

.cta-content {
    max-width: 600px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.cta-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--space-4);
}

.cta-subtitle {
    font-size: var(--font-size-lg);
    margin-bottom: var(--space-8);
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    flex-wrap: wrap;
}

.cta-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-xl);
    text-decoration: none;
    font-weight: 600;
    font-size: var(--font-size-lg);
    transition: var(--transition-normal);
}

.cta-btn.primary {
    background: var(--dubizzle-primary);
    color: white;
}

.cta-btn.primary:hover {
    background: var(--dubizzle-primary-dark);
    transform: translateY(-2px);
}

.cta-btn.secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.cta-btn.secondary:hover {
    background: white;
    color: var(--neutral-800);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-lg);
    }
    
    .hero-stats {
        gap: var(--space-4);
    }
    
    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--space-4);
    }
    
    .ads-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .cta-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Include Dubizzle Header -->
{% include 'includes/dubizzle_header.html' %}

<!-- Hero Section -->
<section class="dubizzle-hero">
    <div class="hero-content">
        <h1 class="hero-title">أفضل موقع للإعلانات المبوبة في المملكة</h1>
        <p class="hero-subtitle">
            اكتشف آلاف الإعلانات المتنوعة أو انشر إعلانك مجاناً واصل لملايين المشترين في جميع أنحاء المملكة العربية السعودية
        </p>
        
        <div class="hero-stats">
            <div class="hero-stat">
                <span class="hero-stat-number" data-count="{{ stats.total_ads }}">0</span>
                <span class="hero-stat-label">إعلان نشط</span>
            </div>
            <div class="hero-stat">
                <span class="hero-stat-number" data-count="{{ stats.total_users }}">0</span>
                <span class="hero-stat-label">مستخدم مسجل</span>
            </div>
            <div class="hero-stat">
                <span class="hero-stat-number" data-count="{{ stats.total_categories }}">0</span>
                <span class="hero-stat-label">قسم متنوع</span>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="categories-section">
    <div class="section-header">
        <h2 class="section-title">تصفح حسب الأقسام</h2>
        <p class="section-subtitle">اختر من بين مجموعة واسعة من الأقسام المتنوعة واعثر على ما تبحث عنه بسهولة</p>
    </div>
    
    <div class="categories-grid">
        {% for category in categories %}
        <a href="{% url 'ads:search' %}?category={{ category.id }}" class="category-card">
            <div class="category-icon">
                <i class="{{ category.icon|default:'fas fa-tag' }}"></i>
            </div>
            <h3 class="category-title">{{ category.name }}</h3>
            <p class="category-count">{{ category.ads_count|default:0 }} إعلان</p>
            <span class="category-link">
                تصفح الآن <i class="fas fa-arrow-left"></i>
            </span>
        </a>
        {% endfor %}
    </div>
</section>

<!-- Featured Ads Section -->
<section class="featured-section">
    <div class="section-header">
        <h2 class="section-title">الإعلانات المميزة</h2>
        <p class="section-subtitle">أفضل الإعلانات المختارة بعناية لك</p>
    </div>
    
    <div class="ads-grid">
        {% for ad in featured_ads %}
        <a href="{% url 'ads:detail' ad.pk %}" class="ad-card">
            {% if ad.is_featured %}
                <div class="ad-badge">
                    <i class="fas fa-star"></i> مميز
                </div>
            {% endif %}
            
            {% if ad.images.exists %}
                <img src="{{ ad.images.first.image.url }}" class="ad-image" alt="{{ ad.title }}">
            {% else %}
                <div class="ad-image">
                    <i class="fas fa-image"></i>
                </div>
            {% endif %}
            
            <div class="ad-content">
                <h3 class="ad-title">{{ ad.title|truncatechars:50 }}</h3>
                <p class="ad-description">{{ ad.description|truncatechars:80 }}</p>
                
                <div class="ad-footer">
                    {% if ad.price %}
                        <span class="ad-price">{{ ad.price|floatformat:0 }} ريال</span>
                    {% else %}
                        <span class="ad-price">السعر غير محدد</span>
                    {% endif %}
                    <span class="ad-time">{{ ad.created_at|timesince }}</span>
                </div>
            </div>
        </a>
        {% endfor %}
    </div>
    
    <div style="text-align: center; margin-top: var(--space-12);">
        <a href="{% url 'ads:list' %}" class="cta-btn primary">
            <i class="fas fa-eye"></i>
            عرض جميع الإعلانات
        </a>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
    <div class="cta-content">
        <h2 class="cta-title">جاهز لنشر إعلانك؟</h2>
        <p class="cta-subtitle">انضم لآلاف المستخدمين الذين يثقون بنا في بيع وشراء منتجاتهم</p>
        
        <div class="cta-buttons">
            <a href="{% url 'ads:create' %}" class="cta-btn primary">
                <i class="fas fa-plus"></i>
                أضف إعلانك مجاناً
            </a>
            <a href="{% url 'accounts:register' %}" class="cta-btn secondary">
                <i class="fas fa-user-plus"></i>
                إنشاء حساب جديد
            </a>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate hero stats
    function animateCounters() {
        const counters = document.querySelectorAll('.hero-stat-number');
        const speed = 200;
        
        const animateCounter = (counter) => {
            const target = parseInt(counter.getAttribute('data-count'));
            const count = parseInt(counter.innerText);
            const increment = target / speed;
            
            if (count < target) {
                counter.innerText = Math.ceil(count + increment);
                setTimeout(() => animateCounter(counter), 1);
            } else {
                counter.innerText = target.toLocaleString('ar-SA');
            }
        };
        
        // Intersection Observer for triggering animation
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const counter = entry.target;
                    if (!counter.classList.contains('animated')) {
                        counter.classList.add('animated');
                        animateCounter(counter);
                    }
                }
            });
        }, { threshold: 0.5 });
        
        counters.forEach(counter => observer.observe(counter));
    }
    
    // Initialize animations
    animateCounters();
    
    // Add scroll animations for cards
    const cards = document.querySelectorAll('.category-card, .ad-card');
    const cardObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 100);
            }
        });
    }, { threshold: 0.1 });
    
    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'all 0.6s ease';
        cardObserver.observe(card);
    });
});
</script>
{% endblock %}

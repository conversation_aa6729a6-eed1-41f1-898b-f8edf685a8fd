{% extends 'admin_panel/base.html' %}
{% load static %}

{% block title %}الإعلانات المعلقة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-clock text-warning me-2"></i>الإعلانات في انتظار الموافقة
                    {% if ads %}
                        <span class="badge bg-warning">{{ ads|length }}</span>
                    {% endif %}
                </h2>
                
                {% if ads %}
                <div>
                    <button type="button" class="btn btn-success" onclick="selectAll()">
                        <i class="fas fa-check-square me-1"></i>تحديد الكل
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="bulkApprove()">
                        <i class="fas fa-check-double me-1"></i>اعتماد المحدد
                    </button>
                </div>
                {% endif %}
            </div>
            
            {% if ads %}
                <form id="bulkForm" method="post" action="{% url 'admin_panel:bulk_approve' %}">
                    {% csrf_token %}
                    
                    <div class="row">
                        {% for ad in ads %}
                        <div class="col-lg-6 col-xl-4 mb-4">
                            <div class="card h-100 border-warning">
                                <div class="card-header bg-warning bg-opacity-10">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="form-check">
                                            <input class="form-check-input ad-checkbox" type="checkbox" name="ad_ids" value="{{ ad.id }}" id="ad_{{ ad.id }}">
                                            <label class="form-check-label fw-bold" for="ad_{{ ad.id }}">
                                                {{ ad.title|truncatechars:30 }}
                                            </label>
                                        </div>
                                        <span class="badge bg-warning">معلق</span>
                                    </div>
                                </div>
                                
                                {% if ad.images.exists %}
                                    <img src="{{ ad.images.first.image.url }}" class="card-img-top" style="height: 200px; object-fit: cover;" alt="{{ ad.title }}">
                                {% else %}
                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                        <i class="fas fa-image fa-3x text-muted"></i>
                                    </div>
                                {% endif %}
                                
                                <div class="card-body">
                                    <h6 class="card-title">{{ ad.title }}</h6>
                                    <p class="card-text small">{{ ad.description|truncatechars:100 }}</p>
                                    
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-tag me-1"></i>{{ ad.category.name }}
                                        </small>
                                    </div>
                                    
                                    {% if ad.price %}
                                    <div class="mb-2">
                                        <span class="text-success fw-bold">{{ ad.price|floatformat:0 }} ريال</span>
                                    </div>
                                    {% endif %}
                                    
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>{{ ad.user.get_full_name|default:ad.user.username }}
                                        </small>
                                    </div>
                                    
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>{{ ad.created_at|date:"Y-m-d H:i" }}
                                        </small>
                                    </div>
                                    
                                    {% if ad.location %}
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-map-marker-alt me-1"></i>{{ ad.location }}
                                        </small>
                                    </div>
                                    {% endif %}
                                </div>
                                
                                <div class="card-footer bg-transparent">
                                    <div class="d-grid gap-2">
                                        <div class="btn-group" role="group">
                                            <form method="post" action="{% url 'admin_panel:approve_ad' ad.id %}" class="d-inline">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-success btn-sm" onclick="return confirm('هل تريد اعتماد هذا الإعلان؟')">
                                                    <i class="fas fa-check me-1"></i>اعتماد
                                                </button>
                                            </form>
                                            
                                            <form method="post" action="{% url 'admin_panel:reject_ad' ad.id %}" class="d-inline">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('هل تريد رفض هذا الإعلان؟')">
                                                    <i class="fas fa-times me-1"></i>رفض
                                                </button>
                                            </form>
                                        </div>
                                        
                                        <a href="{% url 'ads:detail' ad.pk %}" class="btn btn-outline-primary btn-sm" target="_blank">
                                            <i class="fas fa-eye me-1"></i>معاينة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </form>
                
                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1">الأولى</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
            {% else %}
                <div class="text-center py-5">
                    <div class="card border-0 bg-light">
                        <div class="card-body p-5">
                            <i class="fas fa-check-circle fa-5x text-success mb-4"></i>
                            <h3 class="text-success mb-3">ممتاز! لا توجد إعلانات معلقة</h3>
                            <p class="text-muted mb-4">
                                جميع الإعلانات تم مراجعتها. ستظهر هنا الإعلانات الجديدة التي تحتاج للموافقة.
                            </p>
                            <a href="{% url 'admin_panel:dashboard' %}" class="btn btn-primary">
                                <i class="fas fa-tachometer-alt me-2"></i>العودة للوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function selectAll() {
    const checkboxes = document.querySelectorAll('.ad-checkbox');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
    
    checkboxes.forEach(cb => {
        cb.checked = !allChecked;
    });
    
    const button = document.querySelector('button[onclick="selectAll()"]');
    if (allChecked) {
        button.innerHTML = '<i class="fas fa-check-square me-1"></i>تحديد الكل';
    } else {
        button.innerHTML = '<i class="fas fa-square me-1"></i>إلغاء التحديد';
    }
}

function bulkApprove() {
    const checkedBoxes = document.querySelectorAll('.ad-checkbox:checked');
    
    if (checkedBoxes.length === 0) {
        alert('يرجى تحديد إعلان واحد على الأقل');
        return;
    }
    
    if (confirm(`هل تريد اعتماد ${checkedBoxes.length} إعلان؟`)) {
        document.getElementById('bulkForm').submit();
    }
}

// Auto-refresh every 30 seconds
setTimeout(function() {
    location.reload();
}, 30000);
</script>
{% endblock %}

{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}إضافة إعلان جديد{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-plus me-2"></i>إضافة إعلان جديد</h3>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.title.id_for_label }}" class="form-label">عنوان الإعلان *</label>
                            {{ form.title }}
                            {% if form.title.errors %}
                                <div class="text-danger">{{ form.title.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.category.id_for_label }}" class="form-label">القسم *</label>
                            {{ form.category }}
                            {% if form.category.errors %}
                                <div class="text-danger">{{ form.category.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.price.id_for_label }}" class="form-label">السعر (ريال)</label>
                            {{ form.price }}
                            {% if form.price.errors %}
                                <div class="text-danger">{{ form.price.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">وصف الإعلان *</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger">{{ form.description.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.location.id_for_label }}" class="form-label">الموقع</label>
                            {{ form.location }}
                            {% if form.location.errors %}
                                <div class="text-danger">{{ form.location.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.phone.id_for_label }}" class="form-label">رقم الهاتف</label>
                            {{ form.phone }}
                            {% if form.phone.errors %}
                                <div class="text-danger">{{ form.phone.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.email.id_for_label }}" class="form-label">البريد الإلكتروني</label>
                        {{ form.email }}
                        {% if form.email.errors %}
                            <div class="text-danger">{{ form.email.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Image Upload Section -->
                    <div class="mb-4">
                        <label class="form-label">صور الإعلان</label>
                        <div class="border rounded p-3">
                            <div id="image-upload-area" class="text-center">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-2"></i>
                                <p class="text-muted">اسحب الصور هنا أو انقر لاختيار الصور</p>
                                <input type="file" id="image-input" name="images" multiple accept="image/*" class="d-none">
                                <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('image-input').click()">
                                    <i class="fas fa-image me-2"></i>اختر الصور
                                </button>
                            </div>
                            <div id="image-preview" class="mt-3 row"></div>
                        </div>
                        <small class="text-muted">يمكنك رفع حتى 5 صور. الحد الأقصى لحجم الصورة 5 ميجابايت.</small>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> سيتم مراجعة إعلانك من قبل الإدارة قبل نشره على الموقع.
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'ads:list' %}" class="btn btn-secondary me-md-2">إلغاء</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>نشر الإعلان
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const imageInput = document.getElementById('image-input');
    const imagePreview = document.getElementById('image-preview');
    const uploadArea = document.getElementById('image-upload-area');
    
    // Handle file selection
    imageInput.addEventListener('change', function(e) {
        handleFiles(e.target.files);
    });
    
    // Handle drag and drop
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('border-primary');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('border-primary');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('border-primary');
        handleFiles(e.dataTransfer.files);
    });
    
    function handleFiles(files) {
        imagePreview.innerHTML = '';
        
        if (files.length > 5) {
            alert('يمكنك رفع حتى 5 صور فقط');
            return;
        }
        
        Array.from(files).forEach((file, index) => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const col = document.createElement('div');
                    col.className = 'col-md-2 mb-2';
                    col.innerHTML = `
                        <div class="position-relative">
                            <img src="${e.target.result}" class="img-thumbnail" style="height: 100px; width: 100%; object-fit: cover;">
                            <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0" onclick="this.parentElement.parentElement.remove()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    `;
                    imagePreview.appendChild(col);
                };
                reader.readAsDataURL(file);
            }
        });
    }
});
</script>

<style>
.form-control, .form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#image-upload-area {
    transition: all 0.3s ease;
}

#image-upload-area:hover {
    background-color: #f8f9fa;
}

#image-upload-area.border-primary {
    border-color: #007bff !important;
    background-color: #f8f9fa;
}
</style>
{% endblock %}

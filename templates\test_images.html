{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الصور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">اختبار عرض الصور</h1>
        
        <div class="row">
            {% for ad in ads %}
            <div class="col-md-4 mb-4">
                <div class="card">
                    {% if ad.images.exists %}
                        <img src="{{ ad.images.first.image.url }}" class="card-img-top" alt="{{ ad.title }}" style="height: 200px; object-fit: cover;">
                    {% else %}
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                            <span class="text-muted">لا توجد صورة</span>
                        </div>
                    {% endif %}
                    <div class="card-body">
                        <h5 class="card-title">{{ ad.title }}</h5>
                        <p class="card-text">{{ ad.description|truncatewords:10 }}</p>
                        {% if ad.images.exists %}
                            <small class="text-success">✅ يوجد {{ ad.images.count }} صورة</small>
                        {% else %}
                            <small class="text-danger">❌ لا توجد صور</small>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</body>
</html>
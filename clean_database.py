#!/usr/bin/env python
"""
Script to clean database from test data and make it production ready
"""
import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

def clean_test_data():
    """حذف البيانات التجريبية"""
    print("🧹 تنظيف قاعدة البيانات من البيانات التجريبية")
    print("=" * 60)
    
    try:
        from ads.models import Advertisement, Category
        from accounts.models import CustomUser
        
        # عرض البيانات الحالية
        print("📊 البيانات الحالية:")
        print(f"   - المستخدمون: {CustomUser.objects.count()}")
        print(f"   - الأقسام: {Category.objects.count()}")
        print(f"   - الإعلانات: {Advertisement.objects.count()}")
        
        # تأكيد الحذف
        print("\n⚠️ تحذير: سيتم حذف جميع البيانات التجريبية!")
        print("سيتم الاحتفاظ بـ:")
        print("- المستخدم الإداري (admin)")
        print("- الأقسام الأساسية")
        print("- هيكل قاعدة البيانات")
        
        response = input("\nهل تريد المتابعة؟ (y/N): ")
        
        if response.lower() != 'y':
            print("❌ تم إلغاء العملية")
            return False
        
        # حذف الإعلانات التجريبية
        print("\n🗑️ حذف الإعلانات التجريبية...")
        ads_deleted = Advertisement.objects.all().delete()
        print(f"✅ تم حذف {ads_deleted[0]} إعلان")
        
        # حذف المستخدمين التجريبيين (الاحتفاظ بالمدير فقط)
        print("\n👥 حذف المستخدمين التجريبيين...")
        test_users = CustomUser.objects.exclude(username='admin')
        users_count = test_users.count()
        test_users.delete()
        print(f"✅ تم حذف {users_count} مستخدم تجريبي")
        
        # الاحتفاظ بالأقسام الأساسية فقط
        print("\n📂 تنظيف الأقسام...")
        categories_count = Category.objects.count()
        print(f"✅ تم الاحتفاظ بـ {categories_count} قسم أساسي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تنظيف البيانات: {e}")
        return False

def setup_production_categories():
    """إعداد الأقسام الأساسية للإنتاج"""
    print("\n📂 إعداد الأقسام الأساسية...")
    
    try:
        from ads.models import Category
        
        # الأقسام الأساسية للموقع الحقيقي
        production_categories = [
            {'name': 'عقارات', 'description': 'شقق، فيلات، أراضي، مكاتب للبيع والإيجار', 'icon': 'fas fa-home'},
            {'name': 'سيارات', 'description': 'سيارات جديدة ومستعملة، دراجات، قطع غيار', 'icon': 'fas fa-car'},
            {'name': 'وظائف', 'description': 'فرص عمل، وظائف شاغرة، خدمات توظيف', 'icon': 'fas fa-briefcase'},
            {'name': 'إلكترونيات', 'description': 'أجهزة كمبيوتر، جوالات، أجهزة منزلية', 'icon': 'fas fa-laptop'},
            {'name': 'أثاث ومنزل', 'description': 'أثاث، ديكور، أدوات منزلية', 'icon': 'fas fa-couch'},
            {'name': 'خدمات', 'description': 'خدمات مهنية، صيانة، تنظيف', 'icon': 'fas fa-tools'},
            {'name': 'أزياء وموضة', 'description': 'ملابس، أحذية، إكسسوارات', 'icon': 'fas fa-tshirt'},
            {'name': 'رياضة وترفيه', 'description': 'معدات رياضية، ألعاب، هوايات', 'icon': 'fas fa-futbol'},
            {'name': 'تعليم ودورات', 'description': 'دورات تدريبية، كتب، خدمات تعليمية', 'icon': 'fas fa-graduation-cap'},
            {'name': 'حيوانات أليفة', 'description': 'حيوانات أليفة، مستلزمات، خدمات بيطرية', 'icon': 'fas fa-paw'},
        ]
        
        # حذف الأقسام الموجودة وإنشاء جديدة
        Category.objects.all().delete()
        
        for cat_data in production_categories:
            category = Category.objects.create(
                name=cat_data['name'],
                description=cat_data['description'],
                icon=cat_data['icon'],
                is_active=True
            )
            print(f"✅ تم إنشاء القسم: {category.name}")
        
        print(f"✅ تم إنشاء {len(production_categories)} قسم أساسي")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الأقسام: {e}")
        return False

def verify_clean_database():
    """التحقق من نظافة قاعدة البيانات"""
    print("\n🔍 التحقق من قاعدة البيانات...")
    
    try:
        from ads.models import Advertisement, Category
        from accounts.models import CustomUser
        
        # عد البيانات
        users_count = CustomUser.objects.count()
        categories_count = Category.objects.count()
        ads_count = Advertisement.objects.count()
        
        print(f"📊 البيانات النهائية:")
        print(f"   - المستخدمون: {users_count}")
        print(f"   - الأقسام: {categories_count}")
        print(f"   - الإعلانات: {ads_count}")
        
        # عرض المستخدمين
        print(f"\n👥 المستخدمون الموجودون:")
        for user in CustomUser.objects.all():
            user_type = "مدير" if user.is_superuser else "عادي"
            print(f"   - {user.username} ({user_type})")
        
        # عرض الأقسام
        print(f"\n📂 الأقسام المتاحة:")
        for category in Category.objects.all():
            print(f"   - {category.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
        return False

def create_admin_user():
    """إنشاء مستخدم إداري إذا لم يكن موجود"""
    print("\n👑 التحقق من المستخدم الإداري...")
    
    try:
        from accounts.models import CustomUser
        
        # التحقق من وجود مدير
        admin_exists = CustomUser.objects.filter(is_superuser=True).exists()
        
        if not admin_exists:
            print("📝 إنشاء مستخدم إداري جديد...")
            admin = CustomUser.objects.create_superuser(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                first_name='مدير',
                last_name='الموقع',
                is_verified=True
            )
            print(f"✅ تم إنشاء المستخدم الإداري: {admin.username}")
        else:
            admin = CustomUser.objects.filter(is_superuser=True).first()
            print(f"✅ المستخدم الإداري موجود: {admin.username}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المدير: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🏭 تحويل الموقع إلى الإنتاج الحقيقي")
    print("=" * 60)
    
    # 1. تنظيف البيانات التجريبية
    if not clean_test_data():
        return False
    
    # 2. إعداد الأقسام الأساسية
    if not setup_production_categories():
        return False
    
    # 3. التأكد من وجود مدير
    if not create_admin_user():
        return False
    
    # 4. التحقق النهائي
    if not verify_clean_database():
        return False
    
    print("\n" + "=" * 60)
    print("🎉 تم تحويل الموقع إلى الإنتاج بنجاح!")
    print("=" * 60)
    
    print("\n✅ ما تم إنجازه:")
    print("- حذف جميع البيانات التجريبية")
    print("- إعداد أقسام حقيقية للموقع")
    print("- الاحتفاظ بالمستخدم الإداري فقط")
    print("- قاعدة بيانات نظيفة وجاهزة للإنتاج")
    
    print("\n🌐 الموقع الآن:")
    print("- خالي من البيانات الوهمية")
    print("- جاهز لاستقبال بيانات حقيقية")
    print("- يعتمد على قاعدة البيانات فقط")
    
    print("\n🔑 تسجيل الدخول:")
    print("- المدير: admin / admin123")
    print("- الرابط: http://127.0.0.1:8000/admin-panel/")
    
    print("\n📋 الخطوات التالية:")
    print("1. تشغيل الموقع: python manage.py runserver")
    print("2. تسجيل مستخدمين حقيقيين")
    print("3. إنشاء إعلانات حقيقية")
    print("4. إدارة المحتوى من لوحة الإدارة")
    
    return True

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ تم إلغاء العملية بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()

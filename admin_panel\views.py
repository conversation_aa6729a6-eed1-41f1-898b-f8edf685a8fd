from django.shortcuts import get_object_or_404, redirect, render
from django.views.generic import Template<PERSON>iew, ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator
from django.contrib import messages
from django.urls import reverse_lazy
from django.db.models import Count, Q
from django.contrib.auth import get_user_model
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from ads.models import Advertisement, Category, Report, Notification
from ads.forms import CategoryForm

User = get_user_model()

@method_decorator(staff_member_required, name='dispatch')
class DashboardView(TemplateView):
    """لوحة التحكم الرئيسية"""
    template_name = 'admin_panel/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # إحصائيات عامة
        context['total_users'] = User.objects.count()
        context['total_ads'] = Advertisement.objects.count()
        context['pending_ads'] = Advertisement.objects.filter(status='pending').count()
        context['approved_ads'] = Advertisement.objects.filter(status='approved').count()
        context['total_categories'] = Category.objects.count()
        context['total_reports'] = Report.objects.filter(is_resolved=False).count()

        # أحدث الإعلانات
        context['recent_ads'] = Advertisement.objects.order_by('-created_at')[:5]

        # أحدث المستخدمين
        context['recent_users'] = User.objects.order_by('-date_joined')[:5]

        # التقارير غير المحلولة
        context['unresolved_reports'] = Report.objects.filter(
            is_resolved=False
        ).order_by('-created_at')[:5]

        return context

@method_decorator(staff_member_required, name='dispatch')
class UserListView(ListView):
    """قائمة المستخدمين"""
    model = User
    template_name = 'admin_panel/user_list.html'
    context_object_name = 'users'
    paginate_by = 20

    def get_queryset(self):
        queryset = User.objects.annotate(
            ads_count=Count('advertisements')
        ).order_by('-date_joined')

        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(username__icontains=search) |
                Q(email__icontains=search) |
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search)
            )

        return queryset

@method_decorator(staff_member_required, name='dispatch')
class UserDetailView(DetailView):
    """تفاصيل المستخدم"""
    model = User
    template_name = 'admin_panel/user_detail.html'
    context_object_name = 'user_detail'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['user_ads'] = Advertisement.objects.filter(
            user=self.object
        ).order_by('-created_at')
        return context

@method_decorator(staff_member_required, name='dispatch')
class ToggleUserStatusView(TemplateView):
    """تفعيل/إلغاء تفعيل المستخدم"""

    def post(self, request, pk):
        user = get_object_or_404(User, pk=pk)
        user.is_active = not user.is_active
        user.save()

        status = "تم تفعيل" if user.is_active else "تم إلغاء تفعيل"
        messages.success(request, f'{status} المستخدم {user.username} بنجاح!')

        return redirect('admin_panel:user_detail', pk=pk)

@method_decorator(staff_member_required, name='dispatch')
class AdminAdListView(ListView):
    """قائمة الإعلانات للإدارة"""
    model = Advertisement
    template_name = 'admin_panel/ad_list.html'
    context_object_name = 'ads'
    paginate_by = 20

    def get_queryset(self):
        queryset = Advertisement.objects.select_related(
            'user', 'category'
        ).order_by('-created_at')

        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) |
                Q(description__icontains=search) |
                Q(user__username__icontains=search)
            )

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = Advertisement.STATUS_CHOICES
        return context

@method_decorator(staff_member_required, name='dispatch')
class AdminAdDetailView(DetailView):
    """تفاصيل الإعلان للإدارة"""
    model = Advertisement
    template_name = 'admin_panel/ad_detail.html'
    context_object_name = 'ad'

@method_decorator(staff_member_required, name='dispatch')
class ApproveAdView(TemplateView):
    """الموافقة على الإعلان"""

    def post(self, request, pk):
        ad = get_object_or_404(Advertisement, pk=pk)
        ad.status = 'approved'
        ad.save()

        messages.success(request, f'تم الموافقة على الإعلان "{ad.title}" بنجاح!')
        return redirect('admin_panel:ad_detail', pk=pk)

@method_decorator(staff_member_required, name='dispatch')
class RejectAdView(TemplateView):
    """رفض الإعلان"""

    def post(self, request, pk):
        ad = get_object_or_404(Advertisement, pk=pk)
        ad.status = 'rejected'
        ad.save()

        messages.success(request, f'تم رفض الإعلان "{ad.title}"!')
        return redirect('admin_panel:ad_detail', pk=pk)

@method_decorator(staff_member_required, name='dispatch')
class ToggleFeatureAdView(TemplateView):
    """تمييز/إلغاء تمييز الإعلان"""

    def post(self, request, pk):
        ad = get_object_or_404(Advertisement, pk=pk)
        ad.is_featured = not ad.is_featured
        ad.save()

        status = "تم تمييز" if ad.is_featured else "تم إلغاء تمييز"
        messages.success(request, f'{status} الإعلان "{ad.title}" بنجاح!')

        return redirect('admin_panel:ad_detail', pk=pk)

# إدارة الأقسام
@method_decorator(staff_member_required, name='dispatch')
class CategoryListView(ListView):
    """قائمة الأقسام"""
    model = Category
    template_name = 'admin_panel/category_list.html'
    context_object_name = 'categories'

@method_decorator(staff_member_required, name='dispatch')
class CategoryCreateView(CreateView):
    """إنشاء قسم جديد"""
    model = Category
    form_class = CategoryForm
    template_name = 'admin_panel/category_form.html'
    success_url = reverse_lazy('admin_panel:category_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم إنشاء القسم بنجاح!')
        return super().form_valid(form)

@method_decorator(staff_member_required, name='dispatch')
class CategoryUpdateView(UpdateView):
    """تعديل القسم"""
    model = Category
    form_class = CategoryForm
    template_name = 'admin_panel/category_form.html'
    success_url = reverse_lazy('admin_panel:category_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث القسم بنجاح!')
        return super().form_valid(form)

@method_decorator(staff_member_required, name='dispatch')
class CategoryDeleteView(DeleteView):
    """حذف القسم"""
    model = Category
    template_name = 'admin_panel/category_confirm_delete.html'
    success_url = reverse_lazy('admin_panel:category_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف القسم بنجاح!')
        return super().delete(request, *args, **kwargs)

# إدارة التقارير
@method_decorator(staff_member_required, name='dispatch')
class ReportListView(ListView):
    """قائمة التقارير"""
    model = Report
    template_name = 'admin_panel/report_list.html'
    context_object_name = 'reports'
    paginate_by = 20

    def get_queryset(self):
        return Report.objects.select_related(
            'user', 'advertisement'
        ).order_by('-created_at')

@method_decorator(staff_member_required, name='dispatch')
class ReportDetailView(DetailView):
    """تفاصيل التقرير"""
    model = Report
    template_name = 'admin_panel/report_detail.html'
    context_object_name = 'report'

@method_decorator(staff_member_required, name='dispatch')
class ResolveReportView(TemplateView):
    """حل التقرير"""

    def post(self, request, pk):
        report = get_object_or_404(Report, pk=pk)
        report.is_resolved = True
        report.save()

        messages.success(request, 'تم حل التقرير بنجاح!')
        return redirect('admin_panel:report_detail', pk=pk)

@method_decorator(staff_member_required, name='dispatch')
class StatisticsView(TemplateView):
    """صفحة الإحصائيات"""
    template_name = 'admin_panel/statistics.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # إحصائيات الإعلانات حسب الحالة
        context['ads_by_status'] = {
            status[0]: Advertisement.objects.filter(status=status[0]).count()
            for status in Advertisement.STATUS_CHOICES
        }

        # إحصائيات الأقسام
        context['ads_by_category'] = Category.objects.annotate(
            ads_count=Count('advertisements')
        ).order_by('-ads_count')

        # إحصائيات المستخدمين
        context['active_users'] = User.objects.filter(is_active=True).count()
        context['inactive_users'] = User.objects.filter(is_active=False).count()

        return context

# إدارة الإعلانات
@method_decorator(staff_member_required, name='dispatch')
class PendingAdsView(ListView):
    """الإعلانات في انتظار الموافقة"""
    model = Advertisement
    template_name = 'admin_panel/pending_ads.html'
    context_object_name = 'ads'
    paginate_by = 20

    def get_queryset(self):
        return Advertisement.objects.filter(
            status='pending'
        ).select_related('user', 'category').order_by('-created_at')

@staff_member_required
@require_POST
def approve_advertisement(request, ad_id):
    """الموافقة على إعلان"""
    ad = get_object_or_404(Advertisement, id=ad_id)

    if ad.status == 'pending':
        ad.status = 'approved'
        ad.save()

        # إنشاء إشعار للمستخدم
        Notification.objects.create(
            user=ad.user,
            advertisement=ad,
            type='ad_approved',
            title='تم اعتماد إعلانك',
            message=f'مبروك! تم اعتماد إعلانك "{ad.title}" وهو الآن متاح للجمهور.'
        )

        messages.success(request, f'تم اعتماد الإعلان "{ad.title}"')
    else:
        messages.warning(request, 'الإعلان ليس في حالة انتظار')

    return redirect('admin_panel:pending_ads')

@staff_member_required
@require_POST
def reject_advertisement(request, ad_id):
    """رفض إعلان"""
    ad = get_object_or_404(Advertisement, id=ad_id)

    if ad.status == 'pending':
        ad.status = 'rejected'
        ad.save()

        # إنشاء إشعار للمستخدم
        Notification.objects.create(
            user=ad.user,
            advertisement=ad,
            type='ad_rejected',
            title='تم رفض إعلانك',
            message=f'نأسف، تم رفض إعلانك "{ad.title}". يرجى مراجعة شروط النشر وإعادة المحاولة.'
        )

        messages.success(request, f'تم رفض الإعلان "{ad.title}"')
    else:
        messages.warning(request, 'الإعلان ليس في حالة انتظار')

    return redirect('admin_panel:pending_ads')

@staff_member_required
@require_POST
def bulk_approve_ads(request):
    """الموافقة على إعلانات متعددة"""
    ad_ids = request.POST.getlist('ad_ids')

    if ad_ids:
        ads = Advertisement.objects.filter(
            id__in=ad_ids,
            status='pending'
        )

        approved_count = 0
        for ad in ads:
            ad.status = 'approved'
            ad.save()

            # إنشاء إشعار للمستخدم
            Notification.objects.create(
                user=ad.user,
                advertisement=ad,
                type='ad_approved',
                title='تم اعتماد إعلانك',
                message=f'مبروك! تم اعتماد إعلانك "{ad.title}" وهو الآن متاح للجمهور.'
            )

            approved_count += 1

        messages.success(request, f'تم اعتماد {approved_count} إعلان')
    else:
        messages.warning(request, 'لم يتم تحديد أي إعلانات')

    return redirect('admin_panel:pending_ads')

@staff_member_required
def advertisement_detail_admin(request, ad_id):
    """عرض تفاصيل إعلان للمدير"""
    ad = get_object_or_404(Advertisement, id=ad_id)

    context = {
        'ad': ad,
        'can_approve': ad.status == 'pending',
        'can_reject': ad.status == 'pending',
    }

    return render(request, 'admin_panel/advertisement_detail.html', context)

#!/usr/bin/env python
"""
Script to create sample data for the classified ads website
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

from ads.models import Category, Advertisement
from accounts.models import CustomUser

def create_categories():
    """Create sample categories"""
    categories = [
        {'name': 'عقارات', 'description': 'شقق، فيلات، أراضي للبيع والإيجار', 'icon': 'fas fa-home'},
        {'name': 'سيارات', 'description': 'سيارات جديدة ومستعملة للبيع', 'icon': 'fas fa-car'},
        {'name': 'وظائف', 'description': 'فرص عمل في جميع المجالات', 'icon': 'fas fa-briefcase'},
        {'name': 'دورات تدريبية', 'description': 'دورات ودروس في مختلف المجالات', 'icon': 'fas fa-graduation-cap'},
        {'name': 'إلكترونيات', 'description': 'أجهزة إلكترونية ومعدات تقنية', 'icon': 'fas fa-laptop'},
        {'name': 'أثاث ومنزل', 'description': 'أثاث وأدوات منزلية', 'icon': 'fas fa-couch'},
        {'name': 'خدمات', 'description': 'خدمات متنوعة', 'icon': 'fas fa-tools'},
        {'name': 'أزياء وموضة', 'description': 'ملابس وإكسسوارات', 'icon': 'fas fa-tshirt'},
    ]
    
    for cat_data in categories:
        category, created = Category.objects.get_or_create(
            name=cat_data['name'],
            defaults={
                'description': cat_data['description'],
                'icon': cat_data['icon'],
                'is_active': True
            }
        )
        if created:
            print(f"Created category: {category.name}")
        else:
            print(f"Category already exists: {category.name}")

def create_sample_users():
    """Create sample users"""
    users_data = [
        {'username': 'user1', 'email': '<EMAIL>', 'first_name': 'أحمد', 'last_name': 'محمد'},
        {'username': 'user2', 'email': '<EMAIL>', 'first_name': 'فاطمة', 'last_name': 'علي'},
        {'username': 'user3', 'email': '<EMAIL>', 'first_name': 'خالد', 'last_name': 'السعد'},
    ]
    
    for user_data in users_data:
        user, created = CustomUser.objects.get_or_create(
            username=user_data['username'],
            defaults={
                'email': user_data['email'],
                'first_name': user_data['first_name'],
                'last_name': user_data['last_name'],
                'is_verified': True
            }
        )
        if created:
            user.set_password('password123')
            user.save()
            print(f"Created user: {user.username}")
        else:
            print(f"User already exists: {user.username}")

def create_sample_ads():
    """Create sample advertisements"""
    # Get categories and users
    real_estate = Category.objects.get(name='عقارات')
    cars = Category.objects.get(name='سيارات')
    jobs = Category.objects.get(name='وظائف')
    electronics = Category.objects.get(name='إلكترونيات')
    
    user1 = CustomUser.objects.get(username='user1')
    user2 = CustomUser.objects.get(username='user2')
    user3 = CustomUser.objects.get(username='user3')
    admin_user = CustomUser.objects.get(username='admin')
    
    ads_data = [
        {
            'title': 'شقة للبيع في الرياض',
            'description': 'شقة مميزة للبيع في حي الملز، 3 غرف نوم، 2 حمام، صالة واسعة، مطبخ مجهز. الشقة في الدور الثالث مع مصعد.',
            'category': real_estate,
            'user': user1,
            'price': 450000,
            'location': 'الرياض - حي الملز',
            'phone': '0501234567',
            'status': 'approved',
            'is_featured': True
        },
        {
            'title': 'سيارة تويوتا كامري 2020',
            'description': 'سيارة تويوتا كامري موديل 2020، حالة ممتازة، قطعت 45000 كم فقط. السيارة تحت الضمان وجميع الخدمات منتظمة.',
            'category': cars,
            'user': user2,
            'price': 85000,
            'location': 'جدة',
            'phone': '0507654321',
            'status': 'approved',
            'is_featured': True
        },
        {
            'title': 'مطلوب مطور ويب',
            'description': 'شركة تقنية رائدة تبحث عن مطور ويب خبرة 3 سنوات في Django و React. راتب مجزي ومزايا ممتازة.',
            'category': jobs,
            'user': admin_user,
            'location': 'الرياض',
            'email': '<EMAIL>',
            'status': 'approved'
        },
        {
            'title': 'لابتوب ديل للبيع',
            'description': 'لابتوب ديل Inspiron 15، معالج Intel i7، ذاكرة 16GB، قرص صلب SSD 512GB. حالة ممتازة.',
            'category': electronics,
            'user': user3,
            'price': 2500,
            'location': 'الدمام',
            'phone': '0551234567',
            'status': 'approved'
        },
        {
            'title': 'فيلا للإيجار في جدة',
            'description': 'فيلا مفروشة للإيجار في حي الروضة، 5 غرف نوم، 4 حمامات، مجلس، صالة، حديقة واسعة.',
            'category': real_estate,
            'user': user1,
            'price': 8000,
            'location': 'جدة - حي الروضة',
            'phone': '0501234567',
            'status': 'pending'
        }
    ]
    
    for ad_data in ads_data:
        ad, created = Advertisement.objects.get_or_create(
            title=ad_data['title'],
            defaults=ad_data
        )
        if created:
            print(f"Created advertisement: {ad.title}")
        else:
            print(f"Advertisement already exists: {ad.title}")

def main():
    print("Creating sample data...")
    create_categories()
    create_sample_users()
    create_sample_ads()
    print("Sample data created successfully!")

if __name__ == '__main__':
    main()

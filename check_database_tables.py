#!/usr/bin/env python
"""
Script to check and create all database tables
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

from django.db import connection
from django.core.management import execute_from_command_line

def check_current_database():
    """فحص قاعدة البيانات الحالية"""
    with connection.cursor() as cursor:
        # التحقق من نوع قاعدة البيانات
        db_vendor = connection.vendor
        print(f"📊 نوع قاعدة البيانات: {db_vendor}")
        
        if db_vendor == 'mysql':
            # عرض معلومات قاعدة البيانات MySQL
            cursor.execute("SELECT DATABASE()")
            db_name = cursor.fetchone()[0]
            print(f"📋 اسم قاعدة البيانات: {db_name}")
            
            # عرض الجداول الموجودة
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"📁 عدد الجداول الموجودة: {len(tables)}")
            
            if tables:
                print("\n📋 الجداول الموجودة:")
                for table in tables:
                    table_name = table[0]
                    cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                    count = cursor.fetchone()[0]
                    print(f"   - {table_name}: {count} سجل")
            
        elif db_vendor == 'sqlite':
            # عرض معلومات قاعدة البيانات SQLite
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"📁 عدد الجداول الموجودة: {len(tables)}")
            
            if tables:
                print("\n📋 الجداول الموجودة:")
                for table in tables:
                    table_name = table[0]
                    if not table_name.startswith('sqlite_'):
                        cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                        count = cursor.fetchone()[0]
                        print(f"   - {table_name}: {count} سجل")

def create_all_tables():
    """إنشاء جميع الجداول"""
    print("\n🔧 إنشاء جميع الجداول...")
    
    try:
        # إنشاء هجرات جديدة إذا لزم الأمر
        print("📦 فحص الهجرات المطلوبة...")
        execute_from_command_line(['manage.py', 'makemigrations'])
        
        # تطبيق جميع الهجرات
        print("🚀 تطبيق جميع الهجرات...")
        execute_from_command_line(['manage.py', 'migrate'])
        
        print("✅ تم إنشاء جميع الجداول بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False

def verify_models():
    """التحقق من النماذج والجداول"""
    print("\n🔍 التحقق من النماذج...")
    
    try:
        # استيراد جميع النماذج
        from accounts.models import CustomUser
        from ads.models import Category, Advertisement, AdImage, Report
        
        # فحص النماذج
        models_info = [
            ('المستخدمون', CustomUser),
            ('الأقسام', Category),
            ('الإعلانات', Advertisement),
            ('صور الإعلانات', AdImage),
            ('التقارير', Report),
        ]
        
        print("📊 إحصائيات النماذج:")
        for name, model in models_info:
            try:
                count = model.objects.count()
                print(f"   - {name}: {count} سجل")
            except Exception as e:
                print(f"   - {name}: ❌ خطأ - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص النماذج: {e}")
        return False

def create_indexes():
    """إنشاء الفهارس المطلوبة"""
    print("\n📇 إنشاء الفهارس...")
    
    try:
        with connection.cursor() as cursor:
            if connection.vendor == 'mysql':
                # إنشاء فهارس MySQL
                indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_ads_status ON ads_advertisement(status)",
                    "CREATE INDEX IF NOT EXISTS idx_ads_category ON ads_advertisement(category_id)",
                    "CREATE INDEX IF NOT EXISTS idx_ads_user ON ads_advertisement(user_id)",
                    "CREATE INDEX IF NOT EXISTS idx_ads_created ON ads_advertisement(created_at)",
                    "CREATE INDEX IF NOT EXISTS idx_ads_featured ON ads_advertisement(is_featured)",
                    "CREATE INDEX IF NOT EXISTS idx_category_active ON ads_category(is_active)",
                    "CREATE INDEX IF NOT EXISTS idx_user_active ON accounts_customuser(is_active)",
                    "CREATE INDEX IF NOT EXISTS idx_reports_resolved ON ads_report(is_resolved)",
                ]
                
                for index_sql in indexes:
                    try:
                        cursor.execute(index_sql)
                        print(f"✅ تم إنشاء فهرس")
                    except Exception as e:
                        print(f"⚠️ فهرس موجود بالفعل أو خطأ: {e}")
            
            elif connection.vendor == 'sqlite':
                # إنشاء فهارس SQLite
                indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_ads_status ON ads_advertisement(status)",
                    "CREATE INDEX IF NOT EXISTS idx_ads_category ON ads_advertisement(category_id)",
                    "CREATE INDEX IF NOT EXISTS idx_ads_user ON ads_advertisement(user_id)",
                    "CREATE INDEX IF NOT EXISTS idx_ads_created ON ads_advertisement(created_at)",
                ]
                
                for index_sql in indexes:
                    try:
                        cursor.execute(index_sql)
                        print(f"✅ تم إنشاء فهرس")
                    except Exception as e:
                        print(f"⚠️ فهرس موجود بالفعل: {e}")
        
        print("✅ تم إنشاء الفهارس بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الفهارس: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🏗️ إنشاء جميع جداول قاعدة البيانات")
    print("=" * 50)
    
    # 1. فحص قاعدة البيانات الحالية
    print("1️⃣ فحص قاعدة البيانات الحالية...")
    check_current_database()
    
    # 2. إنشاء جميع الجداول
    print("\n2️⃣ إنشاء جميع الجداول...")
    if not create_all_tables():
        return False
    
    # 3. التحقق من النماذج
    print("\n3️⃣ التحقق من النماذج...")
    if not verify_models():
        return False
    
    # 4. إنشاء الفهارس
    print("\n4️⃣ إنشاء الفهارس...")
    create_indexes()
    
    # 5. فحص نهائي
    print("\n5️⃣ فحص نهائي...")
    check_current_database()
    
    print("\n" + "=" * 50)
    print("🎉 تم إنشاء جميع الجداول بنجاح!")
    print("=" * 50)
    
    print("\n📋 الجداول المنشأة:")
    print("✅ accounts_customuser - جدول المستخدمين")
    print("✅ ads_category - جدول الأقسام")
    print("✅ ads_advertisement - جدول الإعلانات")
    print("✅ ads_adimage - جدول صور الإعلانات")
    print("✅ ads_report - جدول التقارير")
    print("✅ django_migrations - جدول الهجرات")
    print("✅ auth_* - جداول المصادقة")
    print("✅ django_session - جدول الجلسات")
    
    return True

if __name__ == '__main__':
    main()

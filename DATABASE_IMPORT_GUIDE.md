# 📋 دليل استيراد قاعدة البيانات

## 📁 الملفات المطلوبة

- `classified_ads_database.sql` - ملف قاعدة البيانات الكاملة

## 🚀 طرق الاستيراد

### 1️⃣ الطريقة الأولى: استخدام phpMyAdmin

1. **افتح phpMyAdmin**
   - تشغيل XAMPP
   - فتح المتصفح والذهاب إلى: `http://localhost/phpmyadmin`

2. **إنشاء قاعدة البيانات**
   - انقر على "New" في الشريط الجانبي
   - اسم قاعدة البيانات: `classified_ads_db`
   - Collation: `utf8mb4_unicode_ci`
   - انقر "Create"

3. **استيراد الملف**
   - اختر قاعدة البيانات `classified_ads_db`
   - انقر على تبويب "Import"
   - انقر "Choose File" واختر `classified_ads_database.sql`
   - انقر "Go"

### 2️⃣ الطريقة الثانية: استخدام سطر الأوامر

```bash
# الدخول إلى مجلد XAMPP
cd C:\xampp2\mysql\bin

# تشغيل MySQL
mysql -u root -p

# إنشاء قاعدة البيانات
CREATE DATABASE classified_ads_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# الخروج من MySQL
exit

# استيراد الملف
mysql -u root -p classified_ads_db < path\to\classified_ads_database.sql
```

### 3️⃣ الطريقة الثالثة: استخدام MySQL Workbench

1. فتح MySQL Workbench
2. الاتصال بالخادم المحلي
3. File → Open SQL Script
4. اختيار ملف `classified_ads_database.sql`
5. تشغيل السكريبت

## 📊 محتويات قاعدة البيانات

### 🗂️ الجداول الرئيسية (5 جداول)

| الجدول | الوصف | عدد السجلات |
|--------|--------|-------------|
| `accounts_customuser` | المستخدمون | 4 |
| `ads_category` | أقسام الإعلانات | 8 |
| `ads_advertisement` | الإعلانات | 5 |
| `ads_adimage` | صور الإعلانات | 0 |
| `ads_report` | التقارير | 0 |

### ⚙️ جداول النظام (8 جداول)

- `django_content_type` - أنواع المحتوى
- `auth_permission` - الصلاحيات
- `auth_group` - المجموعات
- `auth_group_permissions` - صلاحيات المجموعات
- `accounts_customuser_groups` - مجموعات المستخدمين
- `accounts_customuser_user_permissions` - صلاحيات المستخدمين
- `django_session` - الجلسات
- `django_admin_log` - سجل الإدارة
- `django_migrations` - الهجرات

## 👥 المستخدمون المدرجون

### 🔑 المستخدم الإداري
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123
- **البريد الإلكتروني:** <EMAIL>
- **الصلاحيات:** مدير كامل

### 👤 المستخدمون التجريبيون
1. **user1** - أحمد محمد (<EMAIL>)
2. **user2** - فاطمة علي (<EMAIL>)  
3. **user3** - خالد السعد (<EMAIL>)
- **كلمة المرور للجميع:** password123

## 📂 الأقسام المدرجة

1. 🏠 **عقارات** - شقق، فيلات، أراضي للبيع والإيجار
2. 🚗 **سيارات** - سيارات جديدة ومستعملة للبيع
3. 💼 **وظائف** - فرص عمل في جميع المجالات
4. 🎓 **دورات تدريبية** - دورات ودروس في مختلف المجالات
5. 💻 **إلكترونيات** - أجهزة إلكترونية ومعدات تقنية
6. 🛋️ **أثاث ومنزل** - أثاث وأدوات منزلية
7. 🔧 **خدمات** - خدمات متنوعة
8. 👕 **أزياء وموضة** - ملابس وإكسسوارات

## 📢 الإعلانات التجريبية

1. **شقة للبيع في الرياض** (مميز) - 450,000 ريال
2. **سيارة تويوتا كامري 2020** (مميز) - 85,000 ريال
3. **مطلوب مطور ويب** - وظيفة
4. **لابتوب ديل للبيع** - 2,500 ريال
5. **فيلا للإيجار في جدة** (مميز) - 8,000 ريال/شهر

## 🔧 إعدادات Django بعد الاستيراد

### 1. تحديث إعدادات قاعدة البيانات

```python
# في ملف settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'classified_ads_db',
        'USER': 'root',
        'PASSWORD': '',  # أو كلمة مرور MySQL
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'charset': 'utf8mb4',
        },
    }
}
```

### 2. تشغيل الأوامر

```bash
# تحديث كلمات المرور (اختياري)
python manage.py shell

# في shell Django:
from accounts.models import CustomUser
admin = CustomUser.objects.get(username='admin')
admin.set_password('admin123')
admin.save()

user1 = CustomUser.objects.get(username='user1')
user1.set_password('password123')
user1.save()
# كرر للمستخدمين الآخرين

# تشغيل الخادم
python manage.py runserver
```

## ✅ التحقق من نجاح الاستيراد

### 1. فحص الجداول
```sql
USE classified_ads_db;
SHOW TABLES;
```

### 2. فحص البيانات
```sql
SELECT COUNT(*) FROM accounts_customuser;  -- يجب أن يكون 4
SELECT COUNT(*) FROM ads_category;         -- يجب أن يكون 8
SELECT COUNT(*) FROM ads_advertisement;    -- يجب أن يكون 5
```

### 3. اختبار تسجيل الدخول
- الذهاب إلى: `http://127.0.0.1:8000/admin-panel/`
- تسجيل الدخول بـ: admin / admin123

## 🚨 ملاحظات مهمة

1. **تأكد من تشغيل MySQL** قبل الاستيراد
2. **النسخ الاحتياطي** - احفظ نسخة من قاعدة البيانات الحالية
3. **كلمات المرور** - غير كلمات المرور الافتراضية في الإنتاج
4. **الصلاحيات** - تحقق من صلاحيات المستخدمين
5. **الترميز** - تأكد من استخدام UTF8MB4 لدعم العربية

## 🔗 الروابط المفيدة

- **الموقع الرئيسي:** http://127.0.0.1:8000/
- **لوحة الإدارة:** http://127.0.0.1:8000/admin-panel/
- **إدارة Django:** http://127.0.0.1:8000/admin/
- **phpMyAdmin:** http://localhost/phpmyadmin

## 📞 الدعم

في حالة مواجهة مشاكل:
1. تحقق من تشغيل خدمات XAMPP
2. تأكد من صحة إعدادات قاعدة البيانات
3. راجع ملفات السجل للأخطاء
4. تأكد من تطابق إصدارات Django و MySQL

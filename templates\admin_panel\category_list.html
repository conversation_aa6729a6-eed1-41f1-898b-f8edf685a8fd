{% extends 'admin_panel/base.html' %}

{% block title %}إدارة الأقسام{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tags me-2"></i>
        إدارة الأقسام
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'admin_panel:category_create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>إضافة قسم جديد
        </a>
    </div>
</div>

<!-- Categories Grid -->
<div class="row">
    {% for category in categories %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div class="d-flex align-items-center">
                        {% if category.icon %}
                            <i class="{{ category.icon }} fa-2x text-primary me-3"></i>
                        {% else %}
                            <i class="fas fa-tag fa-2x text-muted me-3"></i>
                        {% endif %}
                        <div>
                            <h5 class="card-title mb-1">{{ category.name }}</h5>
                            <small class="text-muted">{{ category.created_at|date:"Y/m/d" }}</small>
                        </div>
                    </div>
                    <div>
                        {% if category.is_active %}
                            <span class="badge bg-success">نشط</span>
                        {% else %}
                            <span class="badge bg-secondary">غير نشط</span>
                        {% endif %}
                    </div>
                </div>
                
                <p class="card-text">{{ category.description|default:"لا يوجد وصف" }}</p>
                
                <div class="d-flex justify-content-between align-items-center">
                    <span class="text-muted">
                        <i class="fas fa-bullhorn me-1"></i>
                        {{ category.advertisements.count }} إعلان
                    </span>
                    <div class="btn-group" role="group">
                        <a href="{% url 'admin_panel:category_edit' category.pk %}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit"></i>
                        </a>
                        <a href="{% url 'admin_panel:category_delete' category.pk %}" class="btn btn-sm btn-outline-danger" 
                           onclick="return confirm('هل أنت متأكد من حذف هذا القسم؟')">
                            <i class="fas fa-trash"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-tags fa-5x text-muted mb-3"></i>
            <h4>لا توجد أقسام</h4>
            <p class="text-muted">لم يتم إنشاء أي أقسام بعد</p>
            <a href="{% url 'admin_panel:category_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة أول قسم
            </a>
        </div>
    </div>
    {% endfor %}
</div>
{% endblock %}

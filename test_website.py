#!/usr/bin/env python
"""
Script to test the classified ads website functionality
"""
import os
import sys
import django
import requests
from django.test import Client
from django.contrib.auth import get_user_model

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

from ads.models import Category, Advertisement
from accounts.models import CustomUser

def test_basic_pages():
    """Test basic page accessibility"""
    client = Client()
    
    pages_to_test = [
        ('/', 'الصفحة الرئيسية'),
        ('/ads/', 'قائمة الإعلانات'),
        ('/ads/search/', 'البحث'),
        ('/accounts/login/', 'تسجيل الدخول'),
        ('/accounts/register/', 'التسجيل'),
    ]
    
    print("🧪 اختبار الصفحات الأساسية...")
    for url, name in pages_to_test:
        try:
            response = client.get(url)
            if response.status_code == 200:
                print(f"✅ {name} ({url}) - يعمل بشكل صحيح")
            else:
                print(f"❌ {name} ({url}) - خطأ {response.status_code}")
        except Exception as e:
            print(f"❌ {name} ({url}) - خطأ: {e}")

def test_database_data():
    """Test database data"""
    print("\n🗄️ اختبار بيانات قاعدة البيانات...")
    
    # Test users
    users_count = CustomUser.objects.count()
    print(f"👥 عدد المستخدمين: {users_count}")
    
    # Test categories
    categories_count = Category.objects.count()
    active_categories = Category.objects.filter(is_active=True).count()
    print(f"📂 عدد الأقسام: {categories_count} (نشط: {active_categories})")
    
    # Test advertisements
    ads_count = Advertisement.objects.count()
    approved_ads = Advertisement.objects.filter(status='approved').count()
    pending_ads = Advertisement.objects.filter(status='pending').count()
    print(f"📢 عدد الإعلانات: {ads_count} (موافق عليها: {approved_ads}, في الانتظار: {pending_ads})")

def test_user_authentication():
    """Test user authentication"""
    print("\n🔐 اختبار نظام المصادقة...")
    client = Client()
    
    # Test login page
    response = client.get('/accounts/login/')
    if response.status_code == 200:
        print("✅ صفحة تسجيل الدخول تعمل")
    else:
        print("❌ مشكلة في صفحة تسجيل الدخول")
    
    # Test registration page
    response = client.get('/accounts/register/')
    if response.status_code == 200:
        print("✅ صفحة التسجيل تعمل")
    else:
        print("❌ مشكلة في صفحة التسجيل")

def test_admin_panel():
    """Test admin panel accessibility"""
    print("\n⚙️ اختبار لوحة الإدارة...")
    client = Client()
    
    # Test admin panel redirect (should redirect to login)
    response = client.get('/admin-panel/')
    if response.status_code in [302, 403]:
        print("✅ لوحة الإدارة محمية بشكل صحيح")
    else:
        print(f"❌ مشكلة في حماية لوحة الإدارة - كود الاستجابة: {response.status_code}")

def test_ad_functionality():
    """Test advertisement functionality"""
    print("\n📢 اختبار وظائف الإعلانات...")
    client = Client()
    
    # Test ad list
    response = client.get('/ads/')
    if response.status_code == 200:
        print("✅ قائمة الإعلانات تعمل")
    else:
        print("❌ مشكلة في قائمة الإعلانات")
    
    # Test search
    response = client.get('/ads/search/')
    if response.status_code == 200:
        print("✅ صفحة البحث تعمل")
    else:
        print("❌ مشكلة في صفحة البحث")
    
    # Test individual ad (if exists)
    first_ad = Advertisement.objects.filter(status='approved').first()
    if first_ad:
        response = client.get(f'/ads/{first_ad.pk}/')
        if response.status_code == 200:
            print("✅ صفحة تفاصيل الإعلان تعمل")
        else:
            print("❌ مشكلة في صفحة تفاصيل الإعلان")
    else:
        print("⚠️ لا توجد إعلانات موافق عليها للاختبار")

def test_categories():
    """Test categories"""
    print("\n📂 اختبار الأقسام...")
    
    categories = Category.objects.filter(is_active=True)
    if categories.exists():
        print(f"✅ يوجد {categories.count()} أقسام نشطة:")
        for category in categories[:5]:  # Show first 5
            print(f"   - {category.name} ({category.advertisements.count()} إعلان)")
    else:
        print("❌ لا توجد أقسام نشطة")

def main():
    """Run all tests"""
    print("🚀 بدء اختبار موقع الإعلانات المبوبة")
    print("=" * 50)
    
    test_basic_pages()
    test_database_data()
    test_user_authentication()
    test_admin_panel()
    test_ad_functionality()
    test_categories()
    
    print("\n" + "=" * 50)
    print("✅ انتهى الاختبار!")
    print("\n📋 ملخص الموقع:")
    print("- الموقع الرئيسي: http://127.0.0.1:8000/")
    print("- لوحة الإدارة: http://127.0.0.1:8000/admin-panel/")
    print("- إدارة Django: http://127.0.0.1:8000/admin/")
    print("\n👤 حسابات تجريبية:")
    print("- المدير: admin / admin123")
    print("- مستخدم: user1 / password123")

if __name__ == '__main__':
    main()

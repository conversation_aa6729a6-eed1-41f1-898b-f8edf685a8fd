#!/usr/bin/env python
"""
Simple MySQL setup script
"""
import mysql.connector
from mysql.connector import Error

def create_mysql_database():
    """إنشاء قاعدة بيانات MySQL"""
    print("🚀 إعداد قاعدة بيانات MySQL")
    print("=" * 40)

    try:
        # الاتصال بـ MySQL
        print("📡 الاتصال بـ MySQL...")
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            port=3306
        )

        if connection.is_connected():
            print("✅ تم الاتصال بـ MySQL بنجاح!")

            cursor = connection.cursor()

            # حذف قاعدة البيانات إذا كانت موجودة
            print("🗑️ حذف قاعدة البيانات الموجودة...")
            cursor.execute("DROP DATABASE IF EXISTS classified_ads_db")

            # إنشاء قاعدة البيانات الجديدة
            print("🏗️ إنشاء قاعدة بيانات جديدة...")
            cursor.execute("""
                CREATE DATABASE classified_ads_db
                CHARACTER SET utf8mb4
                COLLATE utf8mb4_unicode_ci
            """)

            # التحقق من إنشاء قاعدة البيانات
            cursor.execute("SHOW DATABASES LIKE 'classified_ads_db'")
            result = cursor.fetchone()

            if result:
                print("✅ تم إنشاء قاعدة البيانات classified_ads_db")

                # عرض معلومات قاعدة البيانات
                cursor.execute("""
                    SELECT
                        SCHEMA_NAME as 'Database_Name',
                        DEFAULT_CHARACTER_SET_NAME as 'Character_Set',
                        DEFAULT_COLLATION_NAME as 'Collation'
                    FROM information_schema.SCHEMATA
                    WHERE SCHEMA_NAME = 'classified_ads_db'
                """)

                db_info = cursor.fetchone()
                if db_info:
                    print(f"📊 معلومات قاعدة البيانات:")
                    print(f"   - الاسم: {db_info[0]}")
                    print(f"   - ترميز الأحرف: {db_info[1]}")
                    print(f"   - ترتيب الأحرف: {db_info[2]}")

                return True
            else:
                print("❌ فشل في إنشاء قاعدة البيانات")
                return False

    except Error as e:
        print(f"❌ خطأ في MySQL: {e}")

        if "Can't connect to MySQL server" in str(e):
            print("\n💡 حلول مقترحة:")
            print("1. تأكد من تشغيل XAMPP")
            print("2. ابدأ خدمة MySQL من XAMPP Control Panel")
            print("3. تحقق من أن المنفذ 3306 غير مستخدم")

        return False

    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()
            print("🔌 تم إغلاق الاتصال بـ MySQL")

if __name__ == '__main__':
    create_mysql_database()

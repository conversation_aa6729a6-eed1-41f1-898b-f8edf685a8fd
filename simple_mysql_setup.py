#!/usr/bin/env python
"""
Simple MySQL setup for classified ads website
"""
import mysql.connector
from mysql.connector import Error

def setup_mysql():
    """إعداد قاعدة بيانات MySQL"""
    print("🚀 بدء إعداد قاعدة بيانات MySQL...")
    
    try:
        # محاولة الاتصال بـ MySQL
        print("📡 محاولة الاتصال بـ MySQL...")
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',  # كلمة مرور فارغة (افتراضي في XAMPP)
            port=3306
        )
        
        if connection.is_connected():
            print("✅ تم الاتصال بـ MySQL بنجاح!")
            
            cursor = connection.cursor()
            
            # إنشاء قاعدة البيانات
            print("📦 إنشاء قاعدة البيانات...")
            cursor.execute("""
                CREATE DATABASE IF NOT EXISTS classified_ads_db 
                CHARACTER SET utf8mb4 
                COLLATE utf8mb4_unicode_ci
            """)
            print("✅ تم إنشاء قاعدة البيانات classified_ads_db")
            
            # التحقق من إنشاء قاعدة البيانات
            cursor.execute("SHOW DATABASES LIKE 'classified_ads_db'")
            result = cursor.fetchone()
            
            if result:
                print("✅ تم التأكد من وجود قاعدة البيانات")
                
                # عرض معلومات قاعدة البيانات
                cursor.execute("""
                    SELECT 
                        SCHEMA_NAME as 'Database_Name',
                        DEFAULT_CHARACTER_SET_NAME as 'Character_Set',
                        DEFAULT_COLLATION_NAME as 'Collation'
                    FROM information_schema.SCHEMATA 
                    WHERE SCHEMA_NAME = 'classified_ads_db'
                """)
                
                db_info = cursor.fetchone()
                if db_info:
                    print(f"📊 معلومات قاعدة البيانات:")
                    print(f"   - الاسم: {db_info[0]}")
                    print(f"   - ترميز الأحرف: {db_info[1]}")
                    print(f"   - ترتيب الأحرف: {db_info[2]}")
                
                return True
            else:
                print("❌ فشل في إنشاء قاعدة البيانات")
                return False
                
    except Error as e:
        print(f"❌ خطأ في MySQL: {e}")
        
        if "Can't connect to MySQL server" in str(e):
            print("\n💡 نصائح لحل المشكلة:")
            print("1. تأكد من تشغيل XAMPP")
            print("2. تأكد من تشغيل خدمة MySQL في XAMPP")
            print("3. تحقق من أن المنفذ 3306 غير مستخدم")
        
        return False
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()
            print("🔌 تم إغلاق الاتصال بـ MySQL")

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🏗️ إعداد قاعدة بيانات MySQL لموقع الإعلانات")
    print("=" * 50)
    
    success = setup_mysql()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 تم إعداد قاعدة البيانات بنجاح!")
        print("\n📋 الخطوات التالية:")
        print("1. تشغيل: python manage.py migrate")
        print("2. إنشاء مستخدم إداري: python manage.py createsuperuser")
        print("3. تحميل البيانات التجريبية: python create_sample_data.py")
        print("4. تشغيل الخادم: python manage.py runserver")
        print("\n🔗 معلومات الاتصال:")
        print("- قاعدة البيانات: classified_ads_db")
        print("- المستخدم: root")
        print("- كلمة المرور: (فارغة)")
        print("- الخادم: localhost:3306")
    else:
        print("❌ فشل في إعداد قاعدة البيانات")
        print("\n🔧 تحقق من:")
        print("- تشغيل XAMPP")
        print("- تشغيل خدمة MySQL")
        print("- إعدادات الاتصال")
    
    print("=" * 50)

if __name__ == '__main__':
    main()

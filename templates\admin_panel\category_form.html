{% extends 'admin_panel/base.html' %}
{% load crispy_forms_tags %}

{% block title %}
    {% if object %}تعديل القسم{% else %}إضافة قسم جديد{% endif %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-{% if object %}edit{% else %}plus{% endif %} me-2"></i>
        {% if object %}تعديل القسم{% else %}إضافة قسم جديد{% endif %}
    </h1>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">اسم القسم *</label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="text-danger">{{ form.name.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.icon.id_for_label }}" class="form-label">أيقونة القسم</label>
                            {{ form.icon }}
                            {% if form.icon.errors %}
                                <div class="text-danger">{{ form.icon.errors }}</div>
                            {% endif %}
                            <small class="form-text text-muted">{{ form.icon.help_text }}</small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">وصف القسم</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger">{{ form.description.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active }}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                القسم نشط
                            </label>
                        </div>
                        {% if form.is_active.errors %}
                            <div class="text-danger">{{ form.is_active.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Icon Preview -->
                    <div class="mb-3">
                        <label class="form-label">معاينة الأيقونة</label>
                        <div class="border rounded p-3 text-center">
                            <i id="icon-preview" class="fas fa-tag fa-3x text-muted"></i>
                            <p class="mt-2 text-muted">معاينة الأيقونة</p>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'admin_panel:category_list' %}" class="btn btn-secondary me-md-2">إلغاء</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            {% if object %}حفظ التغييرات{% else %}إنشاء القسم{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const iconInput = document.getElementById('{{ form.icon.id_for_label }}');
    const iconPreview = document.getElementById('icon-preview');
    
    // Update icon preview when input changes
    iconInput.addEventListener('input', function() {
        const iconClass = this.value.trim();
        if (iconClass) {
            iconPreview.className = iconClass + ' fa-3x text-primary';
        } else {
            iconPreview.className = 'fas fa-tag fa-3x text-muted';
        }
    });
    
    // Set initial icon if editing
    {% if object and object.icon %}
        iconPreview.className = '{{ object.icon }} fa-3x text-primary';
    {% endif %}
});
</script>
{% endblock %}

from django.urls import path
from . import views
from .notification_views import (
    notification_list, notification_detail, mark_notification_read,
    mark_all_notifications_read, get_unread_notifications_count,
    get_recent_notifications
)

app_name = 'ads'

urlpatterns = [
    # عرض الإعلانات
    path('', views.AdListView.as_view(), name='list'),
    path('<int:pk>/', views.AdDetailView.as_view(), name='detail'),
    
    # إضافة وتعديل الإعلانات
    path('create/', views.AdCreateView.as_view(), name='create'),
    path('<int:pk>/edit/', views.AdUpdateView.as_view(), name='edit'),
    path('<int:pk>/delete/', views.AdDeleteView.as_view(), name='delete'),
    
    # الأقسام
    path('category/<int:pk>/', views.CategoryDetailView.as_view(), name='category_detail'),
    
    # البحث
    path('search/', views.AdSearchView.as_view(), name='search'),
    
    # التقارير
    path('<int:pk>/report/', views.ReportAdView.as_view(), name='report'),

    # الإشعارات
    path('notifications/', notification_list, name='notifications'),
    path('notifications/<int:notification_id>/', notification_detail, name='notification_detail'),
    path('notifications/<int:notification_id>/read/', mark_notification_read, name='mark_read'),
    path('notifications/mark-all-read/', mark_all_notifications_read, name='mark_all_read'),
    path('api/notifications/unread-count/', get_unread_notifications_count, name='unread_count'),
    path('api/notifications/recent/', get_recent_notifications, name='recent_notifications'),
]

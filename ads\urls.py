from django.urls import path
from . import views

app_name = 'ads'

urlpatterns = [
    # عرض الإعلانات
    path('', views.AdListView.as_view(), name='list'),
    path('<int:pk>/', views.AdDetailView.as_view(), name='detail'),
    
    # إضافة وتعديل الإعلانات
    path('create/', views.AdCreateView.as_view(), name='create'),
    path('<int:pk>/edit/', views.AdUpdateView.as_view(), name='edit'),
    path('<int:pk>/delete/', views.AdDeleteView.as_view(), name='delete'),
    
    # الأقسام
    path('category/<int:pk>/', views.CategoryDetailView.as_view(), name='category_detail'),
    
    # البحث
    path('search/', views.AdSearchView.as_view(), name='search'),
    
    # التقارير
    path('<int:pk>/report/', views.ReportAdView.as_view(), name='report'),
]

#!/usr/bin/env python
"""
Verify complete database integration
"""
import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

def test_home_page_data():
    """اختبار بيانات الصفحة الرئيسية"""
    print("🏠 اختبار الصفحة الرئيسية")
    print("-" * 30)
    
    try:
        from classified_ads_site.views import HomeView
        from django.test import RequestFactory
        
        # إنشاء request وهمي
        factory = RequestFactory()
        request = factory.get('/')
        
        # إنشاء view وجلب البيانات
        view = HomeView()
        view.request = request
        context = view.get_context_data()
        
        # التحقق من البيانات
        categories = context.get('categories', [])
        featured_ads = context.get('featured_ads', [])
        latest_ads = context.get('latest_ads', [])
        stats = context.get('stats', {})
        
        print(f"📂 الأقسام: {len(categories)} قسم")
        print(f"⭐ الإعلانات المميزة: {len(featured_ads)} إعلان")
        print(f"📢 أحدث الإعلانات: {len(latest_ads)} إعلان")
        print(f"📊 الإحصائيات:")
        print(f"   - إجمالي الإعلانات: {stats.get('total_ads', 0)}")
        print(f"   - إجمالي الأقسام: {stats.get('total_categories', 0)}")
        print(f"   - الإعلانات المميزة: {stats.get('featured_ads_count', 0)}")
        
        # التحقق من أن البيانات تأتي من قاعدة البيانات
        from ads.models import Category, Advertisement
        
        db_categories = Category.objects.filter(is_active=True).count()
        db_featured = Advertisement.objects.filter(status='approved', is_featured=True).count()
        db_total = Advertisement.objects.filter(status='approved').count()
        
        print(f"\n✅ مقارنة مع قاعدة البيانات:")
        print(f"   - الأقسام: View={len(categories)}, DB={db_categories} {'✅' if len(categories) == db_categories else '❌'}")
        print(f"   - المميزة: View={len(featured_ads)}, DB={db_featured} {'✅' if len(featured_ads) == db_featured else '❌'}")
        print(f"   - الإجمالي: View={stats.get('total_ads', 0)}, DB={db_total} {'✅' if stats.get('total_ads', 0) == db_total else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الصفحة الرئيسية: {e}")
        return False

def test_ads_list_page():
    """اختبار صفحة قائمة الإعلانات"""
    print(f"\n📋 اختبار صفحة قائمة الإعلانات")
    print("-" * 30)
    
    try:
        from ads.views import AdListView
        from django.test import RequestFactory
        
        # إنشاء request وهمي
        factory = RequestFactory()
        request = factory.get('/ads/')
        
        # إنشاء view وجلب البيانات
        view = AdListView()
        view.request = request
        queryset = view.get_queryset()
        context = view.get_context_data()
        
        print(f"📢 الإعلانات في القائمة: {len(queryset)}")
        
        categories = context.get('categories', [])
        featured_ads = context.get('featured_ads', [])
        
        print(f"📂 الأقسام المتاحة: {len(categories)}")
        print(f"⭐ الإعلانات المميزة: {len(featured_ads)}")
        
        # التحقق من أن البيانات تأتي من قاعدة البيانات
        from ads.models import Advertisement, Category
        
        db_approved = Advertisement.objects.filter(status='approved').count()
        db_categories = Category.objects.filter(is_active=True).count()
        
        print(f"\n✅ مقارنة مع قاعدة البيانات:")
        print(f"   - الإعلانات: View={len(queryset)}, DB={db_approved} {'✅' if len(queryset) == db_approved else '❌'}")
        print(f"   - الأقسام: View={len(categories)}, DB={db_categories} {'✅' if len(categories) == db_categories else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قائمة الإعلانات: {e}")
        return False

def test_search_functionality():
    """اختبار وظيفة البحث"""
    print(f"\n🔍 اختبار وظيفة البحث")
    print("-" * 30)
    
    try:
        from ads.views import AdSearchView
        from django.test import RequestFactory
        
        # إنشاء request وهمي مع استعلام بحث
        factory = RequestFactory()
        request = factory.get('/ads/search/?q=سيارة')
        
        # إنشاء view وجلب البيانات
        view = AdSearchView()
        view.request = request
        view.kwargs = {}
        
        # محاكاة get_queryset
        queryset = view.get_queryset()
        
        print(f"🔍 نتائج البحث عن 'سيارة': {len(queryset)}")
        
        # البحث في قاعدة البيانات مباشرة
        from ads.models import Advertisement
        from django.db.models import Q
        
        db_results = Advertisement.objects.filter(
            Q(title__icontains='سيارة') | Q(description__icontains='سيارة'),
            status='approved'
        ).count()
        
        print(f"✅ مقارنة مع قاعدة البيانات: View={len(queryset)}, DB={db_results}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار البحث: {e}")
        return False

def test_admin_dashboard():
    """اختبار لوحة الإدارة"""
    print(f"\n👑 اختبار لوحة الإدارة")
    print("-" * 30)
    
    try:
        from admin_panel.views import DashboardView
        from django.test import RequestFactory
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # إنشاء request وهمي
        factory = RequestFactory()
        request = factory.get('/admin-panel/')
        
        # إنشاء مستخدم وهمي للطلب
        admin_user = User.objects.get(username='admin')
        request.user = admin_user
        
        # إنشاء view وجلب البيانات
        view = DashboardView()
        view.request = request
        context = view.get_context_data()
        
        print(f"👥 إجمالي المستخدمين: {context.get('total_users', 0)}")
        print(f"📢 إجمالي الإعلانات: {context.get('total_ads', 0)}")
        print(f"⏳ الإعلانات المعلقة: {context.get('pending_ads', 0)}")
        print(f"✅ الإعلانات المعتمدة: {context.get('approved_ads', 0)}")
        print(f"📂 إجمالي الأقسام: {context.get('total_categories', 0)}")
        
        # التحقق من البيانات
        from ads.models import Advertisement, Category
        
        db_total_ads = Advertisement.objects.count()
        db_pending = Advertisement.objects.filter(status='pending').count()
        db_approved = Advertisement.objects.filter(status='approved').count()
        db_categories = Category.objects.count()
        db_users = User.objects.count()
        
        print(f"\n✅ مقارنة مع قاعدة البيانات:")
        print(f"   - المستخدمون: View={context.get('total_users', 0)}, DB={db_users}")
        print(f"   - الإعلانات: View={context.get('total_ads', 0)}, DB={db_total_ads}")
        print(f"   - المعلقة: View={context.get('pending_ads', 0)}, DB={db_pending}")
        print(f"   - المعتمدة: View={context.get('approved_ads', 0)}, DB={db_approved}")
        print(f"   - الأقسام: View={context.get('total_categories', 0)}, DB={db_categories}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار لوحة الإدارة: {e}")
        return False

def test_notifications_system():
    """اختبار نظام الإشعارات"""
    print(f"\n🔔 اختبار نظام الإشعارات")
    print("-" * 30)
    
    try:
        from ads.models import Notification
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        admin_user = User.objects.get(username='admin')
        
        # عد الإشعارات
        total_notifications = Notification.objects.filter(user=admin_user).count()
        unread_notifications = Notification.objects.filter(user=admin_user, is_read=False).count()
        
        print(f"📬 إجمالي الإشعارات: {total_notifications}")
        print(f"🔔 الإشعارات غير المقروءة: {unread_notifications}")
        
        # عرض أحدث الإشعارات
        recent_notifications = Notification.objects.filter(
            user=admin_user
        ).order_by('-created_at')[:3]
        
        print(f"📋 أحدث الإشعارات:")
        for notif in recent_notifications:
            read_status = "مقروء" if notif.is_read else "غير مقروء"
            print(f"   - {notif.title} ({notif.type}) - {read_status}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإشعارات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔗 التحقق من الربط الكامل بقاعدة البيانات")
    print("=" * 60)
    
    all_tests_passed = True
    
    # اختبار الصفحة الرئيسية
    if not test_home_page_data():
        all_tests_passed = False
    
    # اختبار صفحة قائمة الإعلانات
    if not test_ads_list_page():
        all_tests_passed = False
    
    # اختبار البحث
    if not test_search_functionality():
        all_tests_passed = False
    
    # اختبار لوحة الإدارة
    if not test_admin_dashboard():
        all_tests_passed = False
    
    # اختبار الإشعارات
    if not test_notifications_system():
        all_tests_passed = False
    
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ الموقع مربوط بالكامل بقاعدة البيانات")
        
        print("\n📋 ما تم التحقق منه:")
        print("- الصفحة الرئيسية تجلب البيانات من قاعدة البيانات")
        print("- صفحة الإعلانات تعرض البيانات الحقيقية")
        print("- البحث يعمل على قاعدة البيانات")
        print("- لوحة الإدارة تعرض إحصائيات حقيقية")
        print("- نظام الإشعارات يعمل")
        
        print("\n🌐 الموقع جاهز للاستخدام:")
        print("- جميع البيانات من قاعدة البيانات")
        print("- نظام الموافقة يعمل")
        print("- الإشعارات تعمل")
        print("- لا توجد بيانات مكتوبة يدوياً")
        
    else:
        print("❌ بعض الاختبارات فشلت")
    
    print("=" * 60)
    
    return all_tests_passed

if __name__ == '__main__':
    main()

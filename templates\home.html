{% extends 'base.html' %}
{% load static %}

{% block title %}الرئيسية - موقع الإعلانات المبوبة{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="row mb-5">
    <div class="col-12">
        <div class="jumbotron bg-primary text-white p-5 rounded">
            <div class="container text-center">
                <h1 class="display-4">مرحباً بك في موقع الإعلانات المبوبة</h1>
                <p class="lead">أفضل مكان لنشر وتصفح الإعلانات المجانية</p>
                <hr class="my-4">
                <p>ابحث عن ما تريد أو أضف إعلانك الآن</p>
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <form method="GET" action="{% url 'ads:search' %}" class="d-flex">
                            <input class="form-control me-2" type="search" name="q" placeholder="ابحث عن إعلان..." aria-label="Search">
                            <button class="btn btn-outline-light" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>
                </div>
                <div class="mt-3">
                    <a class="btn btn-light btn-lg me-2" href="{% url 'ads:create' %}" role="button">
                        <i class="fas fa-plus me-2"></i>أضف إعلانك
                    </a>
                    <a class="btn btn-outline-light btn-lg" href="{% url 'ads:list' %}" role="button">
                        <i class="fas fa-list me-2"></i>تصفح الإعلانات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Categories Section -->
<div class="row mb-5">
    <div class="col-12">
        <h2 class="text-center mb-4">الأقسام الرئيسية</h2>
        {% if categories %}
        <div class="row">
            {% for category in categories %}
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="{{ category.icon }} fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">{{ category.name }}</h5>
                        <p class="card-text">{{ category.description }}</p>
                        <a href="{% url 'ads:search' %}?category={{ category.id }}" class="btn btn-primary">تصفح</a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>لا توجد أقسام متاحة حالياً</strong><br>
                <small>سيتم إضافة الأقسام قريباً</small>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Featured Ads Section -->
<div class="row mb-5">
    <div class="col-12">
        <h2 class="text-center mb-4">الإعلانات المميزة</h2>
        {% if featured_ads %}
        <div class="row">
            {% for ad in featured_ads %}
            <div class="col-md-4 mb-3">
                <div class="card h-100">
                    {% if ad.images.exists %}
                        <img src="{{ ad.images.first.image.url }}" class="card-img-top" style="height: 200px; object-fit: cover;" alt="{{ ad.title }}">
                    {% else %}
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                            <i class="fas fa-image fa-3x text-muted"></i>
                        </div>
                    {% endif %}
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">{{ ad.title|truncatechars:50 }}</h5>
                        <p class="card-text flex-grow-1">{{ ad.description|truncatechars:100 }}</p>
                        <div class="d-flex justify-content-between align-items-center mt-auto">
                            {% if ad.price %}
                                <span class="text-primary fw-bold">{{ ad.price|floatformat:0 }} ريال</span>
                            {% else %}
                                <span class="text-muted">السعر غير محدد</span>
                            {% endif %}
                            <small class="text-muted">{{ ad.created_at|timesince }}</small>
                        </div>
                        <a href="{% url 'ads:detail' ad.pk %}" class="btn btn-primary btn-sm mt-2">عرض التفاصيل</a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center">
            <div class="alert alert-info">
                <i class="fas fa-star"></i>
                <strong>لا توجد إعلانات مميزة حالياً</strong><br>
                <small>كن أول من ينشر إعلاناً مميزاً!</small>
            </div>
        </div>
        {% endif %}
        <div class="text-center mt-4">
            <a href="{% url 'ads:list' %}" class="btn btn-outline-primary">عرض جميع الإعلانات</a>
        </div>
    </div>
</div>

<!-- Statistics Section -->
<div class="row mb-5">
    <div class="col-12">
        <div class="bg-light p-4 rounded">
            <div class="row text-center">
                <div class="col-md-4">
                    <h3 class="text-primary">{{ stats.total_ads }}{% if stats.total_ads > 0 %}+{% endif %}</h3>
                    <p>إعلان نشط</p>
                </div>
                <div class="col-md-4">
                    <h3 class="text-success">{{ stats.total_categories }}{% if stats.total_categories > 0 %}+{% endif %}</h3>
                    <p>قسم متاح</p>
                </div>
                <div class="col-md-4">
                    <h3 class="text-warning">{{ stats.featured_ads_count }}{% if stats.featured_ads_count > 0 %}+{% endif %}</h3>
                    <p>إعلان مميز</p>
                </div>
            </div>
            {% if stats.total_ads == 0 %}
            <div class="text-center mt-3">
                <div class="alert alert-info">
                    <i class="fas fa-rocket"></i>
                    <strong>الموقع جديد وجاهز لاستقبال إعلاناتك!</strong><br>
                    <small>كن أول من ينشر إعلاناً على موقعنا</small>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

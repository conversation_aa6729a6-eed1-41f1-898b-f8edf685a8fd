#!/usr/bin/env python
"""
إضافة صور تجريبية للإعلانات
"""
import os
import django
from PIL import Image, ImageDraw, ImageFont
import random

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

from ads.models import Advertisement, AdImage
from django.core.files.base import ContentFile
from io import BytesIO

def create_sample_image(title, category_icon, color_scheme, size=(400, 300)):
    """إنشاء صورة تجريبية للإعلان"""
    
    # ألوان مختلفة حسب الفئة
    colors = {
        'car': ['#3B82F6', '#1E40AF'],      # أزرق للسيارات
        'home': ['#10B981', '#047857'],     # أخضر للعقارات
        'laptop': ['#8B5CF6', '#5B21B6'],  # بنفسجي للإلكترونيات
        'briefcase': ['#F59E0B', '#D97706'], # برتقالي للوظائف
        'tshirt': ['#EF4444', '#DC2626'],   # أحمر للأزياء
        'couch': ['#6B7280', '#374151'],    # رمادي للأثاث
        'gamepad': ['#EC4899', '#BE185D'],  # وردي للألعاب
        'tools': ['#84CC16', '#65A30D']     # أخضر فاتح للخدمات
    }
    
    # اختيار الألوان
    color_pair = colors.get(color_scheme, ['#6B7280', '#374151'])
    
    # إنشاء الصورة
    img = Image.new('RGB', size, color=color_pair[0])
    draw = ImageDraw.Draw(img)
    
    # إضافة تدرج بسيط
    for i in range(size[1]):
        alpha = i / size[1]
        r1, g1, b1 = tuple(int(color_pair[0][1:][i:i+2], 16) for i in (0, 2, 4))
        r2, g2, b2 = tuple(int(color_pair[1][1:][i:i+2], 16) for i in (0, 2, 4))
        
        r = int(r1 * (1 - alpha) + r2 * alpha)
        g = int(g1 * (1 - alpha) + g2 * alpha)
        b = int(b1 * (1 - alpha) + b2 * alpha)
        
        draw.line([(0, i), (size[0], i)], fill=(r, g, b))
    
    # إضافة نص العنوان
    try:
        # محاولة استخدام خط عربي إذا كان متاحاً
        font_size = 24
        font = ImageFont.load_default()
    except:
        font = ImageFont.load_default()
    
    # حساب موضع النص
    text_bbox = draw.textbbox((0, 0), title, font=font)
    text_width = text_bbox[2] - text_bbox[0]
    text_height = text_bbox[3] - text_bbox[1]
    
    text_x = (size[0] - text_width) // 2
    text_y = (size[1] - text_height) // 2
    
    # إضافة خلفية للنص
    padding = 10
    draw.rectangle([
        text_x - padding, 
        text_y - padding, 
        text_x + text_width + padding, 
        text_y + text_height + padding
    ], fill=(255, 255, 255, 200))
    
    # إضافة النص
    draw.text((text_x, text_y), title, fill=(0, 0, 0), font=font)
    
    # إضافة أيقونة (نص بسيط)
    icon_text = f"📱" if category_icon == "laptop" else "🚗" if category_icon == "car" else "🏠" if category_icon == "home" else "💼"
    icon_bbox = draw.textbbox((0, 0), icon_text, font=font)
    icon_width = icon_bbox[2] - icon_bbox[0]
    draw.text((size[0] - icon_width - 20, 20), icon_text, fill=(255, 255, 255), font=font)
    
    return img

def add_images_to_ads():
    """إضافة صور للإعلانات الموجودة"""
    print("🖼️ إضافة صور تجريبية للإعلانات")
    print("=" * 50)
    
    # الحصول على الإعلانات بدون صور
    ads_without_images = Advertisement.objects.filter(images__isnull=True).distinct()
    
    if not ads_without_images.exists():
        print("ℹ️ جميع الإعلانات لديها صور بالفعل")
        # الحصول على جميع الإعلانات لإضافة صور إضافية
        ads_without_images = Advertisement.objects.all()[:10]
    
    print(f"📊 عدد الإعلانات لإضافة صور لها: {ads_without_images.count()}")
    
    # خريطة الأيقونات والألوان
    category_mapping = {
        'سيارات': ('car', 'car'),
        'عقارات': ('home', 'home'),
        'إلكترونيات': ('laptop', 'laptop'),
        'وظائف': ('briefcase', 'briefcase'),
        'أزياء': ('tshirt', 'tshirt'),
        'أثاث': ('couch', 'couch'),
        'ألعاب': ('gamepad', 'gamepad'),
        'خدمات': ('tools', 'tools')
    }
    
    added_count = 0
    
    for ad in ads_without_images:
        try:
            # تحديد نوع الصورة بناءً على الفئة
            category_name = ad.category.name if ad.category else 'عام'
            icon, color = category_mapping.get(
                next((key for key in category_mapping.keys() if key in category_name), 'عام'),
                ('tools', 'tools')
            )
            
            # إنشاء الصورة
            title = ad.title[:20] + "..." if len(ad.title) > 20 else ad.title
            img = create_sample_image(title, icon, color)
            
            # حفظ الصورة في الذاكرة
            img_io = BytesIO()
            img.save(img_io, format='JPEG', quality=85)
            img_io.seek(0)
            
            # إنشاء اسم الملف
            filename = f"ad_{ad.id}_{random.randint(1000, 9999)}.jpg"
            
            # إنشاء كائن AdImage
            ad_image = AdImage.objects.create(
                advertisement=ad,
                image=ContentFile(img_io.getvalue(), filename),
                is_primary=True
            )
            
            added_count += 1
            print(f"✅ تم إضافة صورة للإعلان: {ad.title[:30]}...")
            
        except Exception as e:
            print(f"❌ خطأ في إضافة صورة للإعلان {ad.title}: {e}")
    
    print(f"\n📊 النتائج:")
    print(f"   ✅ صور مضافة: {added_count}")
    print(f"   📁 إجمالي الصور: {AdImage.objects.count()}")
    
    return added_count > 0

def test_image_display():
    """اختبار عرض الصور"""
    print(f"\n🧪 اختبار عرض الصور")
    print("-" * 30)
    
    try:
        # فحص الإعلانات التي لديها صور
        ads_with_images = Advertisement.objects.filter(images__isnull=False).distinct()
        print(f"📊 إعلانات بصور: {ads_with_images.count()}")
        
        # فحص أول إعلان بصورة
        if ads_with_images.exists():
            ad = ads_with_images.first()
            first_image = ad.images.first()
            
            print(f"🔍 اختبار الإعلان: {ad.title}")
            print(f"   📁 مسار الصورة: {first_image.image.name}")
            print(f"   🌐 URL الصورة: {first_image.image.url}")
            
            # فحص وجود الملف
            if os.path.exists(first_image.image.path):
                print(f"   ✅ الملف موجود على الخادم")
                file_size = os.path.getsize(first_image.image.path)
                print(f"   📏 حجم الملف: {file_size} بايت")
            else:
                print(f"   ❌ الملف غير موجود على الخادم")
                print(f"   📍 المسار المتوقع: {first_image.image.path}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الصور: {e}")
        return False

def create_media_structure():
    """إنشاء هيكل مجلدات media"""
    print(f"\n📁 إنشاء هيكل مجلدات media")
    print("-" * 35)
    
    media_dirs = [
        'media/ads',
        'media/categories',
        'media/users',
        'media/temp'
    ]
    
    for dir_path in media_dirs:
        try:
            os.makedirs(dir_path, exist_ok=True)
            print(f"✅ تم إنشاء المجلد: {dir_path}")
        except Exception as e:
            print(f"❌ خطأ في إنشاء المجلد {dir_path}: {e}")
    
    # إنشاء ملف .gitkeep للمجلدات الفارغة
    for dir_path in media_dirs:
        gitkeep_path = os.path.join(dir_path, '.gitkeep')
        try:
            with open(gitkeep_path, 'w') as f:
                f.write('# Keep this directory in git\n')
        except:
            pass
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🖼️ إصلاح مشكلة عدم ظهور الصور")
    print("=" * 60)
    
    try:
        # إنشاء هيكل المجلدات
        create_media_structure()
        
        # إضافة صور للإعلانات
        success = add_images_to_ads()
        
        if success:
            # اختبار عرض الصور
            test_image_display()
            
            print("\n🎉 تم إصلاح مشكلة الصور بنجاح!")
            print("🌐 يمكنك الآن زيارة الموقع لرؤية الصور")
            print("📍 رابط الموقع: http://127.0.0.1:8000/")
        else:
            print("\n⚠️ لم يتم إضافة صور جديدة")
            
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        return False
    
    return True

if __name__ == '__main__':
    main()

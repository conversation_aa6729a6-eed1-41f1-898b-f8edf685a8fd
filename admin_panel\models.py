from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()

class ActivityLog(models.Model):
    """نموذج لتسجيل أنشطة المستخدمين"""

    ACTION_CHOICES = [
        ('login', 'تسجيل دخول'),
        ('logout', 'تسجيل خروج'),
        ('create', 'إنشاء'),
        ('update', 'تحديث'),
        ('delete', 'حذف'),
        ('approve', 'موافقة'),
        ('reject', 'رفض'),
        ('view', 'عرض'),
        ('download', 'تحميل'),
        ('upload', 'رفع'),
        ('other', 'أخرى'),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='activity_logs',
        verbose_name='المستخدم'
    )
    action = models.Char<PERSON>ield(
        max_length=20,
        choices=ACTION_CHOICES,
        verbose_name='النشاط'
    )
    description = models.TextField(verbose_name='الوصف')
    object_type = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name='نوع الكائن'
    )
    object_id = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name='معرف الكائن'
    )
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name='عنوان IP'
    )
    user_agent = models.TextField(
        null=True,
        blank=True,
        verbose_name='معلومات المتصفح'
    )
    timestamp = models.DateTimeField(
        default=timezone.now,
        verbose_name='الوقت'
    )

    class Meta:
        verbose_name = 'سجل النشاط'
        verbose_name_plural = 'سجلات الأنشطة'
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['action', 'timestamp']),
            models.Index(fields=['timestamp']),
        ]

    def __str__(self):
        user_name = self.user.get_full_name() if self.user else 'مجهول'
        return f'{user_name} - {self.get_action_display()} - {self.timestamp.strftime("%Y-%m-%d %H:%M")}'

    @classmethod
    def log_activity(cls, user, action, description, object_type=None, object_id=None, request=None):
        """دالة مساعدة لتسجيل النشاط"""
        ip_address = None
        user_agent = None

        if request:
            # الحصول على عنوان IP
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip_address = x_forwarded_for.split(',')[0]
            else:
                ip_address = request.META.get('REMOTE_ADDR')

            # الحصول على معلومات المتصفح
            user_agent = request.META.get('HTTP_USER_AGENT', '')

        return cls.objects.create(
            user=user,
            action=action,
            description=description,
            object_type=object_type,
            object_id=object_id,
            ip_address=ip_address,
            user_agent=user_agent
        )

class SystemSettings(models.Model):
    """نموذج لإعدادات النظام"""

    key = models.CharField(
        max_length=100,
        unique=True,
        verbose_name='المفتاح'
    )
    value = models.TextField(verbose_name='القيمة')
    description = models.TextField(
        null=True,
        blank=True,
        verbose_name='الوصف'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )

    class Meta:
        verbose_name = 'إعداد النظام'
        verbose_name_plural = 'إعدادات النظام'
        ordering = ['key']

    def __str__(self):
        return f'{self.key}: {self.value[:50]}'

    @classmethod
    def get_setting(cls, key, default=None):
        """الحصول على قيمة إعداد"""
        try:
            setting = cls.objects.get(key=key)
            return setting.value
        except cls.DoesNotExist:
            return default

    @classmethod
    def set_setting(cls, key, value, description=None):
        """تعيين قيمة إعداد"""
        setting, created = cls.objects.get_or_create(
            key=key,
            defaults={'value': value, 'description': description}
        )
        if not created:
            setting.value = value
            if description:
                setting.description = description
            setting.save()
        return setting

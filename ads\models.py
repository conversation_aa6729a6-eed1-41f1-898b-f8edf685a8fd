from django.db import models
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone

User = get_user_model()

class Category(models.Model):
    """نموذج أقسام الإعلانات"""
    name = models.CharField(max_length=100, unique=True, verbose_name="اسم القسم")
    description = models.TextField(blank=True, verbose_name="وصف القسم")
    icon = models.CharField(max_length=50, blank=True, verbose_name="أيقونة القسم")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "قسم"
        verbose_name_plural = "الأقسام"
        ordering = ['name']

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('ads:category_detail', kwargs={'pk': self.pk})

class Advertisement(models.Model):
    """نموذج الإعلانات"""
    STATUS_CHOICES = [
        ('pending', 'في انتظار المراجعة'),
        ('approved', 'موافق عليه'),
        ('rejected', 'مرفوض'),
        ('expired', 'منتهي الصلاحية'),
    ]

    title = models.CharField(max_length=200, verbose_name="عنوان الإعلان")
    description = models.TextField(verbose_name="وصف الإعلان")
    category = models.ForeignKey(
        Category,
        on_delete=models.CASCADE,
        related_name='advertisements',
        verbose_name="القسم"
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='advertisements',
        verbose_name="المستخدم"
    )
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name="السعر"
    )
    location = models.CharField(max_length=200, blank=True, verbose_name="الموقع")
    phone = models.CharField(max_length=20, blank=True, verbose_name="رقم الهاتف")
    email = models.EmailField(blank=True, verbose_name="البريد الإلكتروني")
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name="حالة الإعلان"
    )
    is_featured = models.BooleanField(default=False, verbose_name="إعلان مميز")
    views_count = models.PositiveIntegerField(default=0, verbose_name="عدد المشاهدات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    expires_at = models.DateTimeField(blank=True, null=True, verbose_name="تاريخ انتهاء الصلاحية")

    class Meta:
        verbose_name = "إعلان"
        verbose_name_plural = "الإعلانات"
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return reverse('ads:detail', kwargs={'pk': self.pk})

    def is_expired(self):
        """فحص انتهاء صلاحية الإعلان"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

class AdImage(models.Model):
    """نموذج صور الإعلانات"""
    advertisement = models.ForeignKey(
        Advertisement,
        on_delete=models.CASCADE,
        related_name='images',
        verbose_name="الإعلان"
    )
    image = models.ImageField(upload_to='ads/', verbose_name="الصورة")
    is_main = models.BooleanField(default=False, verbose_name="صورة رئيسية")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإضافة")

    class Meta:
        verbose_name = "صورة إعلان"
        verbose_name_plural = "صور الإعلانات"
        ordering = ['-is_main', 'created_at']

    def __str__(self):
        return f"صورة {self.advertisement.title}"

class Report(models.Model):
    """نموذج التقارير"""
    REPORT_TYPES = [
        ('spam', 'محتوى مزعج'),
        ('inappropriate', 'محتوى غير مناسب'),
        ('fake', 'إعلان وهمي'),
        ('other', 'أخرى'),
    ]

    advertisement = models.ForeignKey(
        Advertisement,
        on_delete=models.CASCADE,
        related_name='reports',
        verbose_name="الإعلان"
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='reports',
        verbose_name="المستخدم"
    )
    report_type = models.CharField(
        max_length=20,
        choices=REPORT_TYPES,
        verbose_name="نوع التقرير"
    )
    description = models.TextField(blank=True, verbose_name="وصف التقرير")
    is_resolved = models.BooleanField(default=False, verbose_name="تم الحل")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ التقرير")

    class Meta:
        verbose_name = "تقرير"
        verbose_name_plural = "التقارير"
        ordering = ['-created_at']
        unique_together = ['advertisement', 'user']

    def __str__(self):
        return f"تقرير على {self.advertisement.title}"

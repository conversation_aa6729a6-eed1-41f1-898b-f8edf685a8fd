"""
Fix for MariaDB 10.4 RETURNING clause issue
This script patches Django to work with MariaDB 10.4
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')

def patch_mysql_backend():
    """
    Patch MySQL backend to disable RETURNING clause for MariaDB 10.4
    """
    try:
        from django.db.backends.mysql import features
        from django.db.backends.mysql.base import DatabaseWrapper
        
        # Patch the features class
        original_init = features.DatabaseFeatures.__init__
        
        def patched_features_init(self, connection):
            original_init(self, connection)
            # Disable RETURNING support for MariaDB 10.4
            self.can_return_columns_from_insert = False
            self.can_return_rows_from_bulk_insert = False
            self.supports_over_clause = False
        
        features.DatabaseFeatures.__init__ = patched_features_init
        
        # Patch the database wrapper
        original_wrapper_init = DatabaseWrapper.__init__
        
        def patched_wrapper_init(self, *args, **kwargs):
            original_wrapper_init(self, *args, **kwargs)
            # Force disable RETURNING features
            if hasattr(self, 'features'):
                self.features.can_return_columns_from_insert = False
                self.features.can_return_rows_from_bulk_insert = False
        
        DatabaseWrapper.__init__ = patched_wrapper_init
        
        print("✅ تم تطبيق إصلاح MariaDB 10.4 بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ فشل في تطبيق الإصلاح: {e}")
        return False

def test_database_operations():
    """
    Test database operations after applying the patch
    """
    try:
        django.setup()
        
        from accounts.models import CustomUser
        from django.db import connection
        
        # Test database connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result:
                print("✅ اتصال قاعدة البيانات يعمل")
        
        # Test model operations
        user_count = CustomUser.objects.count()
        print(f"✅ عدد المستخدمين: {user_count}")
        
        # Test creating a new user (this should work now)
        test_username = f"test_user_{user_count + 1}"
        
        # Check if user already exists
        if not CustomUser.objects.filter(username=test_username).exists():
            test_user = CustomUser.objects.create_user(
                username=test_username,
                email=f"{test_username}@example.com",
                password="testpass123",
                first_name="اختبار",
                last_name="المستخدم"
            )
            print(f"✅ تم إنشاء مستخدم اختبار: {test_user.username}")
            
            # Delete the test user
            test_user.delete()
            print("✅ تم حذف مستخدم الاختبار")
        else:
            print("✅ اختبار المستخدم موجود بالفعل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار العمليات: {e}")
        return False

def create_custom_user_manager():
    """
    Create a custom user manager that works with MariaDB 10.4
    """
    try:
        # Create a custom manager file
        manager_code = '''
from django.contrib.auth.models import BaseUserManager
from django.db import transaction

class MariaDBCompatibleUserManager(BaseUserManager):
    """
    Custom user manager compatible with MariaDB 10.4
    """
    
    def create_user(self, username, email=None, password=None, **extra_fields):
        """
        Create and save a regular user
        """
        if not username:
            raise ValueError('The Username field must be set')
        
        email = self.normalize_email(email)
        
        # Use transaction to handle the creation properly
        with transaction.atomic():
            user = self.model(
                username=username,
                email=email,
                **extra_fields
            )
            user.set_password(password)
            user.save(using=self._db)
            return user
    
    def create_superuser(self, username, email=None, password=None, **extra_fields):
        """
        Create and save a superuser
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_verified', True)
        
        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')
        
        return self.create_user(username, email, password, **extra_fields)
'''
        
        with open('accounts/managers.py', 'w', encoding='utf-8') as f:
            f.write(manager_code)
        
        print("✅ تم إنشاء مدير المستخدمين المتوافق مع MariaDB")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء مدير المستخدمين: {e}")
        return False

def main():
    """
    Main function to apply all fixes
    """
    print("🔧 تطبيق إصلاحات MariaDB 10.4")
    print("=" * 50)
    
    # 1. Apply MySQL backend patch
    print("1️⃣ تطبيق إصلاح MySQL backend...")
    if not patch_mysql_backend():
        return False
    
    # 2. Create custom user manager
    print("\n2️⃣ إنشاء مدير مستخدمين متوافق...")
    create_custom_user_manager()
    
    # 3. Test database operations
    print("\n3️⃣ اختبار عمليات قاعدة البيانات...")
    if not test_database_operations():
        return False
    
    print("\n" + "=" * 50)
    print("🎉 تم تطبيق جميع الإصلاحات بنجاح!")
    print("=" * 50)
    
    print("\n📋 ما تم إصلاحه:")
    print("✅ إزالة دعم RETURNING clause")
    print("✅ إصلاح إنشاء المستخدمين")
    print("✅ تحسين التوافق مع MariaDB 10.4")
    
    print("\n🌐 يمكنك الآن تشغيل الموقع:")
    print("python manage.py runserver")
    
    return True

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        sys.exit(1)

{% load static %}

<!-- Dubizzle Style Header -->
<header class="dubizzle-header">
    <!-- Header Top Bar -->
    <div class="header-top">
        <div class="container">
            <div class="header-links">
                <a href="{% url 'home' %}">الرئيسية</a>
                <a href="{% url 'ads:list' %}">تصفح الإعلانات</a>
                <a href="#help">المساعدة</a>
                <a href="#about">من نحن</a>
            </div>
            <div class="header-links">
                {% if user.is_authenticated %}
                    <a href="{% url 'accounts:profile' %}">
                        <i class="fas fa-user"></i> {{ user.get_full_name|default:user.username }}
                    </a>
                    <a href="{% url 'accounts:logout' %}">تسجيل الخروج</a>
                {% else %}
                    <a href="{% url 'accounts:login' %}">تسجيل الدخول</a>
                    <a href="{% url 'accounts:register' %}">إنشاء حساب</a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Header Main -->
    <div class="header-main">
        <div class="container">
            <!-- Logo -->
            <a href="{% url 'home' %}" class="dubizzle-logo">
                <i class="fas fa-bullhorn"></i>
                <span>إعلاناتي</span>
            </a>

            <!-- Central Search Bar -->
            <div class="dubizzle-search">
                <form method="GET" action="{% url 'ads:search' %}" class="search-form" id="main-search-form">
                    <input 
                        type="text" 
                        name="q" 
                        class="search-input" 
                        placeholder="ابحث عن أي شيء... سيارات، عقارات، وظائف، إلكترونيات"
                        value="{{ request.GET.q }}"
                        id="main-search-input"
                        autocomplete="off"
                    >
                    <select name="category" class="search-category-select" id="search-category">
                        <option value="">جميع الأقسام</option>
                        {% for category in categories %}
                            <option value="{{ category.id }}" 
                                {% if request.GET.category == category.id|stringformat:"s" %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                        {% endfor %}
                    </select>
                    <button type="submit" class="search-btn">
                        <i class="fas fa-search"></i>
                        بحث
                    </button>
                </form>

                <!-- Search Suggestions -->
                <div class="search-suggestions" id="search-suggestions">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>

            <!-- Header Actions -->
            <div class="header-actions">
                {% if user.is_authenticated %}
                    <a href="{% url 'ads:my_ads' %}" class="header-btn">
                        <i class="fas fa-list"></i>
                        إعلاناتي
                    </a>
                    <a href="{% url 'ads:favorites' %}" class="header-btn">
                        <i class="fas fa-heart"></i>
                        المفضلة
                    </a>
                {% endif %}
                <a href="{% url 'ads:create' %}" class="header-btn primary">
                    <i class="fas fa-plus"></i>
                    أضف إعلان
                </a>
            </div>
        </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="dubizzle-nav">
        <div class="nav-container">
            <ul class="nav-menu" id="nav-menu">
                {% for category in categories|slice:":8" %}
                <li class="nav-item">
                    <a href="{% url 'ads:search' %}?category={{ category.id }}" class="nav-link">
                        <i class="{{ category.icon|default:'fas fa-tag' }}"></i>
                        <span>{{ category.name }}</span>
                    </a>
                </li>
                {% endfor %}
                {% if categories|length > 8 %}
                <li class="nav-item">
                    <a href="{% url 'ads:categories' %}" class="nav-link">
                        <i class="fas fa-ellipsis-h"></i>
                        <span>المزيد</span>
                    </a>
                </li>
                {% endif %}
            </ul>
        </div>
    </nav>
</header>

<!-- Header JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('main-search-input');
    const searchSuggestions = document.getElementById('search-suggestions');
    const searchForm = document.getElementById('main-search-form');
    
    // Search suggestions data
    const suggestions = [
        { text: 'سيارات للبيع', icon: 'fas fa-car', category: 'سيارات' },
        { text: 'شقق للإيجار', icon: 'fas fa-home', category: 'عقارات' },
        { text: 'هواتف ذكية', icon: 'fas fa-mobile-alt', category: 'إلكترونيات' },
        { text: 'وظائف في الرياض', icon: 'fas fa-briefcase', category: 'وظائف' },
        { text: 'أثاث مستعمل', icon: 'fas fa-couch', category: 'أثاث' },
        { text: 'لابتوب للبيع', icon: 'fas fa-laptop', category: 'إلكترونيات' },
        { text: 'عقارات جدة', icon: 'fas fa-building', category: 'عقارات' },
        { text: 'دراجات نارية', icon: 'fas fa-motorcycle', category: 'سيارات' }
    ];
    
    // Show suggestions on focus
    searchInput.addEventListener('focus', function() {
        showSuggestions(suggestions);
    });
    
    // Filter suggestions on input
    searchInput.addEventListener('input', function() {
        const query = this.value.toLowerCase().trim();
        if (query.length > 0) {
            const filtered = suggestions.filter(s => 
                s.text.includes(query) || s.category.includes(query)
            );
            showSuggestions(filtered);
        } else {
            showSuggestions(suggestions);
        }
    });
    
    // Hide suggestions on blur (with delay for clicks)
    searchInput.addEventListener('blur', function() {
        setTimeout(() => {
            hideSuggestions();
        }, 200);
    });
    
    function showSuggestions(items) {
        if (items.length === 0) {
            hideSuggestions();
            return;
        }
        
        const html = items.slice(0, 6).map(item => `
            <div class="suggestion-item" data-text="${item.text}">
                <i class="${item.icon} suggestion-icon"></i>
                <span>${item.text}</span>
            </div>
        `).join('');
        
        searchSuggestions.innerHTML = html;
        searchSuggestions.classList.add('show');
        
        // Add click handlers
        searchSuggestions.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', function() {
                const text = this.getAttribute('data-text');
                searchInput.value = text;
                hideSuggestions();
                searchForm.submit();
            });
        });
    }
    
    function hideSuggestions() {
        searchSuggestions.classList.remove('show');
    }
    
    // Keyboard navigation
    let selectedIndex = -1;
    
    searchInput.addEventListener('keydown', function(e) {
        const items = searchSuggestions.querySelectorAll('.suggestion-item');
        
        if (e.key === 'ArrowDown') {
            e.preventDefault();
            selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
            updateSelection(items);
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            selectedIndex = Math.max(selectedIndex - 1, -1);
            updateSelection(items);
        } else if (e.key === 'Enter' && selectedIndex >= 0) {
            e.preventDefault();
            items[selectedIndex].click();
        } else if (e.key === 'Escape') {
            hideSuggestions();
            selectedIndex = -1;
        }
    });
    
    function updateSelection(items) {
        items.forEach((item, index) => {
            if (index === selectedIndex) {
                item.style.background = 'var(--neutral-100)';
            } else {
                item.style.background = '';
            }
        });
    }
    
    // Mobile menu toggle (if needed)
    const navMenu = document.getElementById('nav-menu');
    if (window.innerWidth <= 768) {
        // Add mobile-specific functionality here
    }
    
    // Search form enhancements
    searchForm.addEventListener('submit', function(e) {
        const query = searchInput.value.trim();
        if (!query) {
            e.preventDefault();
            searchInput.focus();
            return false;
        }
    });
    
    // Auto-focus search on page load (desktop only)
    if (window.innerWidth > 768 && !searchInput.value) {
        setTimeout(() => {
            searchInput.focus();
        }, 500);
    }
});
</script>

<style>
/* Additional header-specific styles */
.dubizzle-header .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

/* Active navigation highlighting */
.nav-link.active {
    background: var(--dubizzle-primary);
    color: white;
}

/* Search input focus effects */
.search-input:focus {
    outline: none;
}

/* Suggestion hover effects */
.suggestion-item:hover {
    background: var(--neutral-100);
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .header-top {
        display: none;
    }
    
    .dubizzle-search {
        order: 3;
        width: 100%;
    }
    
    .search-form {
        border-radius: var(--radius-lg);
    }
}
</style>

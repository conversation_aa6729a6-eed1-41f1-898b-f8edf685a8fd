{% extends 'admin_panel/base.html' %}
{% load static %}

{% block title %}رفض الإعلان{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-times-circle text-danger me-2"></i>رفض الإعلان
                </h2>
                <a href="{% url 'admin_panel:ad_detail' ad.pk %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>العودة
                </a>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                تأكيد رفض الإعلان
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>تنبيه:</strong> سيتم إرسال إشعار للمستخدم بسبب الرفض.
                            </div>

                            <form method="post">
                                {% csrf_token %}
                                
                                <div class="mb-3">
                                    <label for="reason" class="form-label">
                                        <i class="fas fa-comment me-1"></i>سبب الرفض <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="reason" name="reason" required>
                                        <option value="">اختر سبب الرفض</option>
                                        <option value="محتوى غير مناسب">محتوى غير مناسب</option>
                                        <option value="معلومات غير صحيحة">معلومات غير صحيحة</option>
                                        <option value="صور غير واضحة">صور غير واضحة</option>
                                        <option value="سعر غير معقول">سعر غير معقول</option>
                                        <option value="إعلان مكرر">إعلان مكرر</option>
                                        <option value="مخالف للشروط والأحكام">مخالف للشروط والأحكام</option>
                                        <option value="قسم خاطئ">قسم خاطئ</option>
                                        <option value="معلومات اتصال غير صحيحة">معلومات اتصال غير صحيحة</option>
                                        <option value="أخرى">أخرى</option>
                                    </select>
                                </div>

                                <div class="mb-3" id="customReasonDiv" style="display: none;">
                                    <label for="customReason" class="form-label">
                                        <i class="fas fa-edit me-1"></i>تفاصيل السبب
                                    </label>
                                    <textarea class="form-control" id="customReason" name="custom_reason" rows="3" 
                                              placeholder="يرجى توضيح سبب الرفض بالتفصيل..."></textarea>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'admin_panel:ad_detail' ad.pk %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-times-circle me-1"></i>رفض الإعلان
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>معلومات الإعلان
                            </h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>العنوان:</strong></td>
                                    <td>{{ ad.title }}</td>
                                </tr>
                                <tr>
                                    <td><strong>المعلن:</strong></td>
                                    <td>{{ ad.user.get_full_name|default:ad.user.username }}</td>
                                </tr>
                                <tr>
                                    <td><strong>القسم:</strong></td>
                                    <td>{{ ad.category.name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>السعر:</strong></td>
                                    <td>
                                        {% if ad.price %}
                                            {{ ad.price }} ريال
                                        {% else %}
                                            غير محدد
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ الإنشاء:</strong></td>
                                    <td>{{ ad.created_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الحالة الحالية:</strong></td>
                                    <td>
                                        <span class="badge bg-warning">{{ ad.get_status_display }}</span>
                                    </td>
                                </tr>
                            </table>

                            {% if ad.images.exists %}
                                <div class="mt-3">
                                    <h6>الصور:</h6>
                                    <div class="row">
                                        {% for image in ad.images.all|slice:":3" %}
                                            <div class="col-4 mb-2">
                                                <img src="{{ image.image.url }}" class="img-fluid rounded" 
                                                     style="height: 60px; object-fit: cover;">
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-lightbulb me-2"></i>نصائح للمراجعة
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-check text-success me-1"></i> تحقق من وضوح الصور</li>
                                <li><i class="fas fa-check text-success me-1"></i> راجع صحة المعلومات</li>
                                <li><i class="fas fa-check text-success me-1"></i> تأكد من القسم المناسب</li>
                                <li><i class="fas fa-check text-success me-1"></i> راجع الشروط والأحكام</li>
                                <li><i class="fas fa-check text-success me-1"></i> تحقق من معقولية السعر</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('reason').addEventListener('change', function() {
    const customDiv = document.getElementById('customReasonDiv');
    const customTextarea = document.getElementById('customReason');
    
    if (this.value === 'أخرى') {
        customDiv.style.display = 'block';
        customTextarea.required = true;
    } else {
        customDiv.style.display = 'none';
        customTextarea.required = false;
        customTextarea.value = '';
    }
});

// تحديث قيمة السبب عند الإرسال
document.querySelector('form').addEventListener('submit', function(e) {
    const reasonSelect = document.getElementById('reason');
    const customReason = document.getElementById('customReason');
    
    if (reasonSelect.value === 'أخرى' && customReason.value.trim()) {
        reasonSelect.value = customReason.value.trim();
    }
});
</script>
{% endblock %}

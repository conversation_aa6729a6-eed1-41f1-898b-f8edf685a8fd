{% extends 'admin_panel/base.html' %}
{% load static %}

{% block title %}حذف المستخدم{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-user-times text-danger me-2"></i>حذف المستخدم
                </h2>
                <a href="{% url 'admin_panel:user_detail' target_user.pk %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>العودة
                </a>
            </div>

            <div class="row justify-content-center">
                <div class="col-md-10">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                تأكيد حذف المستخدم
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-danger">
                                <i class="fas fa-warning me-2"></i>
                                <strong>تحذير شديد:</strong> هذا الإجراء خطير ولا يمكن التراجع عنه. سيتم حذف المستخدم وجميع بياناته نهائياً.
                            </div>

                            <div class="row">
                                <div class="col-md-8">
                                    <h6 class="text-danger mb-3">هل أنت متأكد من حذف هذا المستخدم؟</h6>
                                    
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-3">
                                                {% if target_user.profile_picture %}
                                                    <img src="{{ target_user.profile_picture.url }}" class="rounded-circle me-3" width="60" height="60">
                                                {% else %}
                                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px;">
                                                        <i class="fas fa-user text-white fa-2x"></i>
                                                    </div>
                                                {% endif %}
                                                <div>
                                                    <h5 class="mb-1">{{ target_user.get_full_name|default:target_user.username }}</h5>
                                                    <p class="text-muted mb-0">{{ target_user.email }}</p>
                                                </div>
                                            </div>
                                            
                                            <div class="row text-sm">
                                                <div class="col-6">
                                                    <strong>اسم المستخدم:</strong> {{ target_user.username }}
                                                </div>
                                                <div class="col-6">
                                                    <strong>الهاتف:</strong> {{ target_user.phone|default:"غير محدد" }}
                                                </div>
                                                <div class="col-6">
                                                    <strong>المدينة:</strong> {{ target_user.location|default:"غير محدد" }}
                                                </div>
                                                <div class="col-6">
                                                    <strong>تاريخ التسجيل:</strong> {{ target_user.date_joined|date:"Y-m-d" }}
                                                </div>
                                                <div class="col-6">
                                                    <strong>الحالة:</strong> 
                                                    {% if target_user.is_active %}
                                                        <span class="badge bg-success">نشط</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">غير نشط</span>
                                                    {% endif %}
                                                </div>
                                                <div class="col-6">
                                                    <strong>الصلاحيات:</strong>
                                                    {% if target_user.is_superuser %}
                                                        <span class="badge bg-danger">مدير عام</span>
                                                    {% elif target_user.is_staff %}
                                                        <span class="badge bg-warning">مدير</span>
                                                    {% else %}
                                                        <span class="badge bg-info">مستخدم عادي</span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- إحصائيات المستخدم -->
                                    <div class="card mb-3">
                                        <div class="card-header">
                                            <h6 class="mb-0">إحصائيات المستخدم</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-3 text-center">
                                                    <div class="h4 text-primary">{{ target_user.advertisements.count }}</div>
                                                    <small>إعلان</small>
                                                </div>
                                                <div class="col-3 text-center">
                                                    <div class="h4 text-success">{{ target_user.advertisements.filter.status='approved'.count }}</div>
                                                    <small>معتمد</small>
                                                </div>
                                                <div class="col-3 text-center">
                                                    <div class="h4 text-warning">{{ target_user.advertisements.filter.status='pending'.count }}</div>
                                                    <small>معلق</small>
                                                </div>
                                                <div class="col-3 text-center">
                                                    <div class="h4 text-info">{{ target_user.reports_made.count }}</div>
                                                    <small>تقرير</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="alert alert-warning">
                                        <h6><i class="fas fa-exclamation-triangle me-1"></i>ما سيحدث عند الحذف:</h6>
                                        <ul class="mb-0">
                                            <li><strong>حذف المستخدم:</strong> سيتم حذف الحساب نهائياً</li>
                                            <li><strong>الإعلانات:</strong> سيتم حذف جميع إعلانات المستخدم ({{ target_user.advertisements.count }} إعلان)</li>
                                            <li><strong>الصور:</strong> سيتم حذف جميع الصور المرتبطة</li>
                                            <li><strong>التقارير:</strong> ستبقى التقارير لكن بدون ربط بالمستخدم</li>
                                            <li><strong>سجل الأنشطة:</strong> ستبقى الأنشطة لكن بدون ربط بالمستخدم</li>
                                        </ul>
                                    </div>

                                    <form method="post" class="mt-4">
                                        {% csrf_token %}
                                        <div class="d-flex justify-content-between">
                                            <a href="{% url 'admin_panel:user_detail' target_user.pk %}" class="btn btn-secondary">
                                                <i class="fas fa-times me-1"></i>إلغاء
                                            </a>
                                            <button type="submit" class="btn btn-danger">
                                                <i class="fas fa-user-times me-1"></i>نعم، احذف المستخدم
                                            </button>
                                        </div>
                                    </form>
                                </div>

                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">آخر الإعلانات</h6>
                                        </div>
                                        <div class="card-body">
                                            {% if target_user.advertisements.exists %}
                                                {% for ad in target_user.advertisements.all|slice:":3" %}
                                                    <div class="d-flex mb-2 pb-2 border-bottom">
                                                        {% if ad.images.exists %}
                                                            <img src="{{ ad.images.first.image.url }}" class="rounded me-2" width="40" height="40" style="object-fit: cover;">
                                                        {% else %}
                                                            <div class="bg-light rounded d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                                                                <i class="fas fa-image text-muted"></i>
                                                            </div>
                                                        {% endif %}
                                                        <div class="flex-grow-1">
                                                            <div class="small fw-bold">{{ ad.title|truncatechars:25 }}</div>
                                                            <div class="small text-muted">{{ ad.created_at|date:"Y-m-d" }}</div>
                                                        </div>
                                                    </div>
                                                {% endfor %}
                                            {% else %}
                                                <div class="text-center text-muted">
                                                    <i class="fas fa-bullhorn fa-2x mb-2"></i>
                                                    <p>لا توجد إعلانات</p>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تأكيد إضافي قبل الحذف
document.querySelector('form').addEventListener('submit', function(e) {
    const username = '{{ target_user.username }}';
    if (!confirm(`هل أنت متأكد تماماً من حذف المستخدم "${username}"؟ هذا الإجراء لا يمكن التراجع عنه وسيحذف جميع بياناته.`)) {
        e.preventDefault();
    }
});
</script>
{% endblock %}

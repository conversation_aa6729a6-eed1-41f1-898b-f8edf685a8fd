<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <!-- SEO Meta Tags -->
    <title>{% block title %}أفضل موقع للإعلانات المبوبة في المملكة العربية السعودية{% endblock %}</title>
    <meta name="description" content="{% block description %}موقع الإعلانات المبوبة الأول في السعودية. اكتشف آلاف الإعلانات للسيارات، العقارات، الوظائف، الإلكترونيات وأكثر. انشر إعلانك مجاناً واصل لملايين المشترين.{% endblock %}">
    <meta name="keywords" content="{% block keywords %}إعلانات مبوبة، سيارات للبيع، عقارات، وظائف، إلكترونيات، أثاث، خدمات، السعودية، الرياض، جدة، الدمام{% endblock %}">
    <meta name="author" content="موقع الإعلانات المبوبة">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="{% block canonical %}{{ request.build_absolute_uri }}{% endblock %}">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{% block og_title %}أفضل موقع للإعلانات المبوبة في السعودية{% endblock %}">
    <meta property="og:description" content="{% block og_description %}اكتشف آلاف الإعلانات المتنوعة أو انشر إعلانك مجاناً. موقع موثوق وآمن للبيع والشراء في السعودية.{% endblock %}">
    <meta property="og:image" content="{% block og_image %}{% load static %}{% static 'images/og-image.jpg' %}{% endblock %}">
    <meta property="og:url" content="{{ request.build_absolute_uri }}">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="موقع الإعلانات المبوبة">
    <meta property="og:locale" content="ar_SA">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{% block twitter_title %}أفضل موقع للإعلانات المبوبة في السعودية{% endblock %}">
    <meta name="twitter:description" content="{% block twitter_description %}اكتشف آلاف الإعلانات المتنوعة أو انشر إعلانك مجاناً{% endblock %}">
    <meta name="twitter:image" content="{% block twitter_image %}{% load static %}{% static 'images/twitter-card.jpg' %}{% endblock %}">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'images/favicon.ico' %}">
    <link rel="apple-touch-icon" sizes="180x180" href="{% static 'images/apple-touch-icon.png' %}">
    <link rel="icon" type="image/png" sizes="32x32" href="{% static 'images/favicon-32x32.png' %}">
    <link rel="icon" type="image/png" sizes="16x16" href="{% static 'images/favicon-16x16.png' %}">
    
    <!-- Preconnect for Performance -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- CSS Resources -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet" integrity="sha384-+qdLaIRZfNu4cVPK/PxJJEy0B0f3Ugv8i482AkY7YQZo7VTZmdQy+qXR0xNgTHOR" crossorigin="anonymous">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" crossorigin="anonymous">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --text-dark: #2d3748;
            --text-light: #718096;
            --bg-light: #f7fafc;
            --white: #ffffff;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
            --border-radius: 12px;
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            background-color: var(--bg-light);
            overflow-x: hidden;
        }
        
        /* Enhanced Navigation */
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow);
            transition: var(--transition);
            padding: 1rem 0;
        }
        
        .navbar.scrolled {
            padding: 0.5rem 0;
            background: rgba(255, 255, 255, 0.98) !important;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .navbar-nav .nav-link {
            font-weight: 500;
            color: var(--text-dark) !important;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem !important;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }
        
        .navbar-nav .nav-link:hover {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            color: white !important;
            transform: translateY(-2px);
        }
        
        /* Enhanced Buttons */
        .btn {
            border-radius: 25px;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            transition: var(--transition);
            border: none;
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            box-shadow: var(--shadow);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
        }
        
        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            background: transparent;
        }
        
        .btn-outline-primary:hover {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            border-color: transparent;
            color: white;
            transform: translateY(-2px);
        }
        
        /* Enhanced Cards */
        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            transition: var(--transition);
            overflow: hidden;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }
        
        /* Loading States */
        .loading {
            opacity: 0.7;
            pointer-events: none;
        }
        
        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid var(--primary-color);
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .navbar-nav {
                background: white;
                border-radius: var(--border-radius);
                padding: 1rem;
                margin-top: 1rem;
                box-shadow: var(--shadow);
            }
        }
        
        /* Accessibility */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }
        
        /* Focus States */
        .btn:focus,
        .form-control:focus,
        .nav-link:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }
        
        /* Print Styles */
        @media print {
            .navbar,
            .btn,
            .floating-element {
                display: none !important;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "موقع الإعلانات المبوبة",
        "url": "{{ request.build_absolute_uri }}",
        "description": "أفضل موقع للإعلانات المبوبة في المملكة العربية السعودية",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ request.build_absolute_uri }}search?q={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>
</head>
<body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only">تخطي إلى المحتوى الرئيسي</a>
    
    <!-- Enhanced Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top" id="mainNavbar">
        <div class="container">
            <a class="navbar-brand" href="{% url 'home' %}">
                <i class="fas fa-bullhorn me-2"></i>
                الإعلانات المبوبة
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="تبديل التنقل">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'home' %}">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'ads:list' %}">
                            <i class="fas fa-list me-1"></i>تصفح الإعلانات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'ads:create' %}">
                            <i class="fas fa-plus me-1"></i>أضف إعلان
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" 
                               data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user-circle me-1"></i>
                                {{ user.get_full_name|default:user.username }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">
                                    <i class="fas fa-user me-2"></i>الملف الشخصي
                                </a></li>
                                <li><a class="dropdown-item" href="{% url 'ads:my_ads' %}">
                                    <i class="fas fa-bullhorn me-2"></i>إعلاناتي
                                </a></li>
                                {% if user.is_staff %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'admin_panel:dashboard' %}">
                                    <i class="fas fa-cogs me-2"></i>لوحة الإدارة
                                </a></li>
                                {% endif %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'accounts:logout' %}">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'accounts:login' %}">
                                <i class="fas fa-sign-in-alt me-1"></i>تسجيل الدخول
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary ms-2" href="{% url 'accounts:register' %}">
                                <i class="fas fa-user-plus me-1"></i>إنشاء حساب
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main id="main-content" role="main">
        {% block content %}{% endblock %}
    </main>
    
    <!-- Enhanced Footer -->
    <footer class="bg-dark text-white py-5 mt-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <h5 class="mb-3">
                        <i class="fas fa-bullhorn me-2"></i>
                        الإعلانات المبوبة
                    </h5>
                    <p class="text-light">
                        أفضل موقع للإعلانات المبوبة في المملكة العربية السعودية. 
                        نربط بين البائعين والمشترين بطريقة آمنة وموثوقة.
                    </p>
                    <div class="social-links">
                        <a href="#" class="text-white me-3" aria-label="فيسبوك">
                            <i class="fab fa-facebook fa-lg"></i>
                        </a>
                        <a href="#" class="text-white me-3" aria-label="تويتر">
                            <i class="fab fa-twitter fa-lg"></i>
                        </a>
                        <a href="#" class="text-white me-3" aria-label="إنستغرام">
                            <i class="fab fa-instagram fa-lg"></i>
                        </a>
                        <a href="#" class="text-white" aria-label="لينكد إن">
                            <i class="fab fa-linkedin fa-lg"></i>
                        </a>
                    </div>
                </div>
                
                <div class="col-lg-2 col-md-6">
                    <h6 class="mb-3">روابط سريعة</h6>
                    <ul class="list-unstyled">
                        <li><a href="{% url 'home' %}" class="text-light text-decoration-none">الرئيسية</a></li>
                        <li><a href="{% url 'ads:list' %}" class="text-light text-decoration-none">تصفح الإعلانات</a></li>
                        <li><a href="{% url 'ads:create' %}" class="text-light text-decoration-none">أضف إعلان</a></li>
                        <li><a href="#" class="text-light text-decoration-none">من نحن</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <h6 class="mb-3">الدعم والمساعدة</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-light text-decoration-none">مركز المساعدة</a></li>
                        <li><a href="#" class="text-light text-decoration-none">الشروط والأحكام</a></li>
                        <li><a href="#" class="text-light text-decoration-none">سياسة الخصوصية</a></li>
                        <li><a href="#" class="text-light text-decoration-none">اتصل بنا</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <h6 class="mb-3">معلومات التواصل</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-phone me-2"></i>
                            <a href="tel:+966123456789" class="text-light text-decoration-none">
                                +966 12 345 6789
                            </a>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-envelope me-2"></i>
                            <a href="mailto:<EMAIL>" class="text-light text-decoration-none">
                                <EMAIL>
                            </a>
                        </li>
                        <li>
                            <i class="fas fa-map-marker-alt me-2"></i>
                            الرياض، المملكة العربية السعودية
                        </li>
                    </ul>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 موقع الإعلانات المبوبة. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <small class="text-light">
                        تم التطوير بـ <i class="fas fa-heart text-danger"></i> في المملكة العربية السعودية
                    </small>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript Resources -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" 
            integrity="sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p" 
            crossorigin="anonymous"></script>
    
    <!-- Enhanced Navigation Script -->
    <script>
        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('mainNavbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
        
        // Enhanced accessibility
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });
        
        document.addEventListener('mousedown', function() {
            document.body.classList.remove('keyboard-navigation');
        });
    </script>
    
    {% block extra_js %}{% endblock %}
    
    <!-- Performance and Analytics -->
    <script>
        // Performance monitoring
        window.addEventListener('load', function() {
            if ('performance' in window) {
                const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
                console.log('Page load time:', loadTime + 'ms');
            }
        });
    </script>
</body>
</html>

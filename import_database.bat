@echo off
echo ========================================
echo استيراد قاعدة بيانات موقع الإعلانات المبوبة
echo ========================================

echo.
echo 🔍 التحقق من ملف قاعدة البيانات...
if not exist "classified_ads_database.sql" (
    echo ❌ ملف قاعدة البيانات غير موجود: classified_ads_database.sql
    echo يرجى التأكد من وجود الملف في نفس المجلد
    pause
    exit /b 1
)
echo ✅ تم العثور على ملف قاعدة البيانات

echo.
echo 🔍 التحقق من MySQL...
if exist "C:\xampp2\mysql\bin\mysql.exe" (
    echo ✅ تم العثور على MySQL في XAMPP2
    set MYSQL_PATH=C:\xampp2\mysql\bin\mysql.exe
) else if exist "C:\xampp\mysql\bin\mysql.exe" (
    echo ✅ تم العثور على MySQL في XAMPP
    set MYSQL_PATH=C:\xampp\mysql\bin\mysql.exe
) else (
    echo ❌ لم يتم العثور على MySQL
    echo يرجى التأكد من تثبيت XAMPP
    pause
    exit /b 1
)

echo.
echo 🚀 بدء عملية الاستيراد...
echo يرجى إدخال كلمة مرور MySQL (اتركها فارغة إذا لم تكن موجودة):

"%MYSQL_PATH%" -u root -p < classified_ads_database.sql

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo ✅ تم استيراد قاعدة البيانات بنجاح!
    echo ========================================
    echo.
    echo 📊 معلومات قاعدة البيانات:
    echo - الاسم: classified_ads_db
    echo - الجداول: 13 جدول
    echo - المستخدمون: 4 مستخدمين
    echo - الأقسام: 8 أقسام
    echo - الإعلانات: 5 إعلانات تجريبية
    echo.
    echo 🔑 حسابات الدخول:
    echo - المدير: admin / admin123
    echo - مستخدم: user1 / password123
    echo.
    echo 📋 الخطوات التالية:
    echo 1. تحديث إعدادات Django لاستخدام MySQL
    echo 2. تشغيل: python manage.py runserver
    echo 3. زيارة: http://127.0.0.1:8000/
    echo.
) else (
    echo.
    echo ❌ فشل في استيراد قاعدة البيانات
    echo.
    echo 🔧 تحقق من:
    echo - تشغيل خدمة MySQL في XAMPP
    echo - صحة كلمة المرور
    echo - صلاحيات المستخدم
    echo.
)

echo اضغط أي مفتاح للخروج...
pause > nul

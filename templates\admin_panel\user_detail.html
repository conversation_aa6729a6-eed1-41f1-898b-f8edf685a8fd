{% extends 'admin_panel/base.html' %}
{% load static %}

{% block title %}تفاصيل المستخدم - {{ user.get_full_name|default:user.username }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>
                        <i class="fas fa-user me-2"></i>تفاصيل المستخدم
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'admin_panel:dashboard' %}">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'admin_panel:user_list' %}">المستخدمون</a></li>
                            <li class="breadcrumb-item active">{{ user.get_full_name|default:user.username }}</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'admin_panel:user_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>العودة للقائمة
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- معلومات المستخدم الأساسية -->
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-id-card me-2"></i>المعلومات الأساسية
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- صورة المستخدم -->
                            <div class="text-center mb-3">
                                {% if user.profile_picture %}
                                    <img src="{{ user.profile_picture.url }}" class="rounded-circle" width="100" height="100" alt="صورة المستخدم">
                                {% else %}
                                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 100px; height: 100px;">
                                        <i class="fas fa-user fa-3x text-white"></i>
                                    </div>
                                {% endif %}
                            </div>

                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>اسم المستخدم:</strong></td>
                                    <td>{{ user.username }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الاسم الكامل:</strong></td>
                                    <td>{{ user.get_full_name|default:"غير محدد" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>البريد الإلكتروني:</strong></td>
                                    <td>{{ user.email|default:"غير محدد" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>رقم الهاتف:</strong></td>
                                    <td>{{ user.phone|default:"غير محدد" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ التسجيل:</strong></td>
                                    <td>{{ user.date_joined|date:"Y-m-d H:i" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>آخر دخول:</strong></td>
                                    <td>{{ user.last_login|date:"Y-m-d H:i"|default:"لم يسجل دخول بعد" }}</td>
                                </tr>
                            </table>

                            <!-- حالة المستخدم -->
                            <div class="mt-3">
                                <h6>حالة الحساب:</h6>
                                <div class="d-flex flex-wrap gap-2">
                                    {% if user.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-danger">غير نشط</span>
                                    {% endif %}

                                    {% if user.is_verified %}
                                        <span class="badge bg-info">موثق</span>
                                    {% else %}
                                        <span class="badge bg-warning">غير موثق</span>
                                    {% endif %}

                                    {% if user.is_staff %}
                                        <span class="badge bg-primary">موظف</span>
                                    {% endif %}

                                    {% if user.is_superuser %}
                                        <span class="badge bg-dark">مدير عام</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات المستخدم -->
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>إحصائيات النشاط
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <div class="text-center p-3 bg-primary bg-opacity-10 rounded">
                                        <i class="fas fa-bullhorn fa-2x text-primary mb-2"></i>
                                        <h4 class="text-primary">{{ user.advertisements.count }}</h4>
                                        <small>إجمالي الإعلانات</small>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="text-center p-3 bg-success bg-opacity-10 rounded">
                                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                        <h4 class="text-success">{{ approved_ads_count }}</h4>
                                        <small>إعلانات معتمدة</small>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="text-center p-3 bg-warning bg-opacity-10 rounded">
                                        <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                        <h4 class="text-warning">{{ pending_ads_count }}</h4>
                                        <small>في الانتظار</small>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="text-center p-3 bg-info bg-opacity-10 rounded">
                                        <i class="fas fa-bell fa-2x text-info mb-2"></i>
                                        <h4 class="text-info">{{ user.notifications.count }}</h4>
                                        <small>الإشعارات</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أحدث الإعلانات -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-list me-2"></i>أحدث الإعلانات
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if user.advertisements.exists %}
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>العنوان</th>
                                                <th>القسم</th>
                                                <th>الحالة</th>
                                                <th>تاريخ الإنشاء</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for ad in user.advertisements.all|slice:":5" %}
                                            <tr>
                                                <td>{{ ad.title|truncatechars:40 }}</td>
                                                <td>{{ ad.category.name }}</td>
                                                <td>
                                                    {% if ad.status == 'approved' %}
                                                        <span class="badge bg-success">معتمد</span>
                                                    {% elif ad.status == 'pending' %}
                                                        <span class="badge bg-warning">معلق</span>
                                                    {% elif ad.status == 'rejected' %}
                                                        <span class="badge bg-danger">مرفوض</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">{{ ad.status }}</span>
                                                    {% endif %}
                                                </td>
                                                <td>{{ ad.created_at|date:"Y-m-d" }}</td>
                                                <td>
                                                    <a href="{% url 'ads:detail' ad.pk %}" class="btn btn-sm btn-outline-primary" target="_blank">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                
                                {% if user.advertisements.count > 5 %}
                                <div class="text-center mt-3">
                                    <a href="{% url 'admin_panel:ad_list' %}?user={{ user.id }}" class="btn btn-outline-primary">
                                        عرض جميع الإعلانات ({{ user.advertisements.count }})
                                    </a>
                                </div>
                                {% endif %}
                            {% else %}
                                <div class="text-center py-4">
                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">لا توجد إعلانات لهذا المستخدم</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- أحدث الإشعارات -->
            {% if user.notifications.exists %}
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bell me-2"></i>أحدث الإشعارات
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                {% for notification in user.notifications.all|slice:":5" %}
                                <div class="list-group-item {% if not notification.is_read %}list-group-item-primary{% endif %}">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">{{ notification.title }}</h6>
                                            <p class="mb-1">{{ notification.message|truncatechars:100 }}</p>
                                            <small class="text-muted">{{ notification.created_at|timesince }} مضت</small>
                                        </div>
                                        {% if not notification.is_read %}
                                            <span class="badge bg-primary">جديد</span>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

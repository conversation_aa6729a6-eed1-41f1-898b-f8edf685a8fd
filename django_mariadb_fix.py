"""
Django MariaDB 10.4 Compatibility Fix
This module provides a complete fix for the RETURNING clause issue
"""

def apply_mariadb_fix():
    """
    Apply comprehensive MariaDB 10.4 compatibility fixes
    """
    try:
        # Import Django modules
        from django.db.backends.mysql import features
        from django.db.backends.mysql import base
        from django.db.backends.mysql import operations
        
        # Create a custom features class
        class MariaDBCompatibleFeatures(features.DatabaseFeatures):
            """Custom features class for MariaDB 10.4"""
            
            def __init__(self, connection):
                super().__init__(connection)
                # Override problematic features
                self._can_return_columns_from_insert = False
                self._can_return_rows_from_bulk_insert = False
            
            @property
            def can_return_columns_from_insert(self):
                return False
            
            @property
            def can_return_rows_from_bulk_insert(self):
                return False
            
            @property
            def supports_over_clause(self):
                return False
        
        # Create a custom database wrapper
        class MariaDBCompatibleWrapper(base.DatabaseWrapper):
            """Custom database wrapper for MariaDB 10.4"""
            
            def __init__(self, *args, **kwargs):
                super().__init__(*args, **kwargs)
                # Use our custom features
                self.features_class = MariaDBCompatibleFeatures
                self._features = None
            
            @property
            def features(self):
                if self._features is None:
                    self._features = self.features_class(self)
                return self._features
        
        # Replace the default wrapper
        base.DatabaseWrapper = MariaDBCompatibleWrapper
        
        print("✅ تم تطبيق إصلاح MariaDB 10.4 الشامل")
        return True
        
    except Exception as e:
        print(f"❌ فشل في تطبيق الإصلاح: {e}")
        return False

# Apply the fix when this module is imported
apply_mariadb_fix()

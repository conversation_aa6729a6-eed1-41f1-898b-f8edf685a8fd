/**
 * مكونات متجاوبة متخصصة
 * Specialized Responsive Components
 */

/* ===== HERO SECTION RESPONSIVE ===== */
.hero-responsive {
    min-height: 60vh;
    display: flex;
    align-items: center;
    padding: var(--spacing-2xl) 0;
    position: relative;
    overflow: hidden;
}

.hero-content {
    text-align: center;
    width: 100%;
    z-index: 2;
    position: relative;
}

.hero-title {
    font-size: var(--text-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    line-height: 1.2;
}

.hero-subtitle {
    font-size: var(--text-base);
    margin-bottom: var(--spacing-xl);
    opacity: 0.9;
}

/* ===== SEARCH BAR RESPONSIVE ===== */
.search-responsive {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    padding: var(--spacing-sm);
    margin: var(--spacing-lg) auto;
    max-width: 100%;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.search-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    padding: var(--spacing-md);
    font-size: var(--text-base);
    background: transparent;
    min-height: var(--touch-target-min);
}

.search-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 20px;
    padding: var(--spacing-md) var(--spacing-xl);
    font-weight: 600;
    min-height: var(--touch-target-min);
    cursor: pointer;
    transition: all 0.3s ease;
}

/* ===== NAVIGATION RESPONSIVE ===== */
.navbar-responsive {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    max-width: var(--container-xxl);
    margin: 0 auto;
}

.navbar-brand {
    font-size: var(--text-lg);
    font-weight: 700;
    text-decoration: none;
    color: #667eea;
}

.navbar-toggle {
    display: flex;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
    min-height: var(--touch-target-min);
    min-width: var(--touch-target-min);
}

.navbar-toggle span {
    width: 24px;
    height: 2px;
    background: #333;
    transition: all 0.3s ease;
}

.navbar-menu {
    position: fixed;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    max-height: calc(100vh - 80px);
    overflow-y: auto;
}

.navbar-menu.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
}

.navbar-nav {
    display: flex;
    flex-direction: column;
    padding: var(--spacing-lg);
    gap: var(--spacing-sm);
}

.nav-link-responsive {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    text-decoration: none;
    color: #333;
    border-radius: 8px;
    transition: all 0.2s ease;
    min-height: var(--touch-target-min);
}

.nav-link-responsive:hover {
    background: #f3f4f6;
    color: #667eea;
}

/* ===== CARD GRID RESPONSIVE ===== */
.card-grid-responsive {
    display: grid;
    gap: var(--spacing-lg);
    grid-template-columns: 1fr;
}

.card-item-responsive {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card-item-responsive:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.card-image-responsive {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-content-responsive {
    padding: var(--spacing-lg);
}

.card-title-responsive {
    font-size: var(--text-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    line-height: 1.3;
}

.card-description-responsive {
    color: #6b7280;
    margin-bottom: var(--spacing-md);
    line-height: 1.5;
}

.card-footer-responsive {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

/* ===== STATS SECTION RESPONSIVE ===== */
.stats-responsive {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: var(--spacing-3xl) 0;
}

.stats-grid {
    display: grid;
    gap: var(--spacing-xl);
    grid-template-columns: 1fr;
    text-align: center;
}

.stat-item-responsive {
    padding: var(--spacing-lg);
}

.stat-number {
    font-size: var(--text-3xl);
    font-weight: 700;
    display: block;
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    font-size: var(--text-base);
    opacity: 0.9;
}

/* ===== TESTIMONIALS RESPONSIVE ===== */
.testimonials-responsive {
    padding: var(--spacing-3xl) 0;
    background: #f9fafb;
}

.testimonial-grid {
    display: grid;
    gap: var(--spacing-xl);
    grid-template-columns: 1fr;
}

.testimonial-item {
    background: white;
    padding: var(--spacing-xl);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.testimonial-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin: 0 auto var(--spacing-md);
    background: #e5e7eb;
}

.testimonial-name {
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.testimonial-text {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.testimonial-rating {
    color: #fbbf24;
}

/* ===== FOOTER RESPONSIVE ===== */
.footer-responsive {
    background: #1f2937;
    color: white;
    padding: var(--spacing-3xl) 0 var(--spacing-xl);
}

.footer-grid {
    display: grid;
    gap: var(--spacing-xl);
    grid-template-columns: 1fr;
    margin-bottom: var(--spacing-xl);
}

.footer-section {
    text-align: center;
}

.footer-title {
    font-size: var(--text-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: var(--spacing-sm);
}

.footer-links a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.2s ease;
}

.footer-links a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: var(--spacing-lg);
    text-align: center;
    color: #9ca3af;
}

/* ===== FORM RESPONSIVE ===== */
.form-container-responsive {
    background: white;
    padding: var(--spacing-xl);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    max-width: 100%;
}

.form-title-responsive {
    font-size: var(--text-2xl);
    font-weight: 700;
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.form-row-responsive {
    display: grid;
    gap: var(--spacing-md);
    grid-template-columns: 1fr;
    margin-bottom: var(--spacing-lg);
}

.flex-around {
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.flex-start {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.flex-end {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.form-label-responsive {
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
    color: #374151;
}

.form-input-responsive {
    width: 100%;
    min-height: var(--touch-target-min);
    padding: var(--spacing-md);
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: var(--text-base);
    transition: all 0.2s ease;
}

.form-input-responsive:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-textarea-responsive {
    min-height: 120px;
    resize: vertical;
}

.form-submit-responsive {
    width: 100%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 8px;
    padding: var(--spacing-lg);
    font-size: var(--text-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: var(--touch-target-large);
}

.form-submit-responsive:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

/* ===== TABLET RESPONSIVE (768px+) ===== */
@media (min-width: 768px) {
    .hero-responsive {
        min-height: 70vh;
        padding: var(--spacing-3xl) 0;
    }
    
    .hero-title {
        font-size: var(--text-4xl);
    }
    
    .hero-subtitle {
        font-size: var(--text-lg);
    }
    
    .search-responsive {
        max-width: 600px;
    }
    
    .search-form {
        flex-direction: row;
        align-items: center;
    }
    
    .navbar-toggle {
        display: none;
    }
    
    .navbar-menu {
        position: static;
        transform: none;
        opacity: 1;
        visibility: visible;
        background: transparent;
        box-shadow: none;
        max-height: none;
        overflow: visible;
    }
    
    .navbar-nav {
        flex-direction: row;
        padding: 0;
        align-items: center;
    }
    
    .card-grid-responsive {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .testimonial-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .footer-grid {
        grid-template-columns: repeat(2, 1fr);
        text-align: left;
    }
    
    .footer-section {
        text-align: left;
    }
    
    .form-row-responsive {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* ===== DESKTOP RESPONSIVE (1024px+) ===== */
@media (min-width: 1024px) {
    .hero-responsive {
        min-height: 80vh;
    }
    
    .hero-title {
        font-size: var(--text-5xl);
    }
    
    .card-grid-responsive {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .testimonial-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .footer-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* ===== LARGE DESKTOP (1440px+) ===== */
@media (min-width: 1440px) {
    .card-grid-responsive {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .hero-title {
        font-size: 4rem;
    }
}

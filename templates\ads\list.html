{% extends 'base_landing.html' %}

{% block title %}جميع الإعلانات - أفضل موقع للإعلانات المبوبة{% endblock %}

{% block content %}
<div style="padding-top: 100px;"> <!-- Space for fixed navbar -->

<!-- Header Section Responsive -->
<div class="container-responsive" style="margin-bottom: var(--spacing-xl);">
    <div class="flex-responsive flex-between" style="align-items: center; margin-bottom: var(--spacing-lg);">
        <h2 class="heading-responsive" style="margin: 0;">
            <i class="fas fa-list me-2"></i>جميع الإعلانات
        </h2>
        <a href="{% url 'ads:create' %}" class="btn-responsive" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white;">
            <i class="fas fa-plus me-2"></i>
            <span class="hidden-mobile">أضف إعلان</span>
            <span class="visible-mobile">إضافة</span>
        </a>
    </div>

    <!-- Search and Filter Bar Responsive -->
    <div class="card-responsive" style="margin-bottom: var(--spacing-xl);">
        <div class="card-body">
            <form method="GET" action="{% url 'ads:search' %}" class="form-responsive">
                <div class="form-row-responsive">
                    <div class="form-group">
                        <input type="text" class="form-control form-input-responsive" name="q" placeholder="ابحث في الإعلانات..." value="{{ request.GET.q }}" aria-label="البحث في الإعلانات">
                    </div>
                    <div class="form-group">
                        <select class="form-control form-input-responsive" name="category" aria-label="اختيار القسم">
                            <option value="">جميع الأقسام</option>
                            {% for category in categories %}
                                <option value="{{ category.id }}" {% if request.GET.category == category.id|stringformat:"s" %}selected{% endif %}>
                                    {{ category.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="form-row-responsive">
                    <div class="form-group">
                        <input type="number" class="form-control form-input-responsive" name="min_price" placeholder="السعر من" value="{{ request.GET.min_price }}" aria-label="السعر الأدنى">
                    </div>
                    <div class="form-group">
                        <input type="number" class="form-control form-input-responsive" name="max_price" placeholder="السعر إلى" value="{{ request.GET.max_price }}" aria-label="السعر الأعلى">
                    </div>
                </div>
                <button type="submit" class="btn-responsive" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; width: 100%;">
                    <i class="fas fa-search me-2"></i>بحث متقدم
                </button>
            </form>
        </div>
    </div>
</div>

<!-- Featured Ads Responsive -->
{% if featured_ads %}
<div class="container-responsive" style="margin-bottom: var(--spacing-3xl);">
    <h4 class="heading-responsive" style="margin-bottom: var(--spacing-lg);">
        <i class="fas fa-star" style="color: #fbbf24; margin-left: var(--spacing-sm);"></i>الإعلانات المميزة
    </h4>
    <div class="card-grid-responsive">
        {% for ad in featured_ads %}
        <div class="card-item-responsive" style="position: relative; border: 2px solid #fbbf24;">
            <div style="position: absolute; top: var(--spacing-md); right: var(--spacing-md); background: #fbbf24; color: #1f2937; padding: var(--spacing-sm) var(--spacing-md); border-radius: 20px; font-size: var(--text-xs); font-weight: 600; z-index: 2;">
                <i class="fas fa-star me-1"></i>مميز
            </div>
            {% if ad.images.first %}
                <img src="{{ ad.images.first.image.url }}" class="card-image-responsive img-cover" alt="{{ ad.title }}">
            {% else %}
                <div class="card-image-responsive bg-light d-flex align-items-center justify-content-center">
                    <i class="fas fa-image fa-3x text-muted"></i>
                </div>
            {% endif %}
            <div class="card-content-responsive">
                <h5 class="card-title-responsive">{{ ad.title }}</h5>
                <p class="card-description-responsive">{{ ad.description|truncatewords:10 }}</p>
                <div class="card-footer-responsive">
                    {% if ad.price %}
                        <span style="font-size: var(--text-lg); font-weight: 700; color: #667eea;">{{ ad.price }} ريال</span>
                    {% else %}
                        <span class="text-muted">السعر غير محدد</span>
                    {% endif %}
                    <small class="text-muted">{{ ad.created_at|timesince }}</small>
                </div>
                <div style="margin-bottom: var(--spacing-md);">
                    <small class="text-muted">
                        <i class="fas fa-map-marker-alt me-1"></i>{{ ad.location|default:"غير محدد" }}
                    </small>
                </div>
                <a href="{{ ad.get_absolute_url }}" class="btn-responsive" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; width: 100%;">
                    <i class="fas fa-eye me-2"></i>عرض التفاصيل
                </a>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}

<!-- Regular Ads Responsive -->
<div class="container-responsive">
    <h4 class="heading-responsive" style="margin-bottom: var(--spacing-lg);">
        <i class="fas fa-bullhorn me-2"></i>جميع الإعلانات
    </h4>

    {% if ads %}
        <div class="card-grid-responsive">
            {% for ad in ads %}
            <div class="card-item-responsive">
                {% if ad.images.first %}
                    <img src="{{ ad.images.first.image.url }}" class="card-image-responsive img-cover" alt="{{ ad.title }}">
                {% else %}
                    <div class="card-image-responsive bg-light d-flex align-items-center justify-content-center">
                        <i class="fas fa-image fa-3x text-muted"></i>
                    </div>
                {% endif %}

                <div class="card-content-responsive">
                    <h5 class="card-title-responsive">{{ ad.title }}</h5>
                    <p class="card-description-responsive">{{ ad.description|truncatewords:15 }}</p>

                    <div style="margin-bottom: var(--spacing-md);">
                        <span style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: 15px; font-size: var(--text-xs); font-weight: 500;">
                            {{ ad.category.name }}
                        </span>
                    </div>

                    <div class="card-footer-responsive">
                        {% if ad.price %}
                            <span style="font-size: var(--text-lg); font-weight: 700; color: #667eea;">{{ ad.price }} ريال</span>
                        {% else %}
                            <span class="text-muted">السعر غير محدد</span>
                        {% endif %}
                        <small class="text-muted">{{ ad.created_at|timesince }}</small>
                    </div>

                    <div style="margin-bottom: var(--spacing-md); display: flex; flex-direction: column; gap: var(--spacing-xs);">
                        <small class="text-muted">
                            <i class="fas fa-map-marker-alt me-1"></i>{{ ad.location|default:"غير محدد" }}
                        </small>
                        <small class="text-muted">
                            <i class="fas fa-eye me-1"></i>{{ ad.views_count }} مشاهدة
                        </small>
                    </div>

                    <a href="{{ ad.get_absolute_url }}" class="btn-responsive" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; width: 100%;">
                        <i class="fas fa-eye me-2"></i>عرض التفاصيل
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
    
    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="Page navigation">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1">الأولى</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                </li>
            {% endif %}
            
            <li class="page-item active">
                <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
            </li>
            
            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
    
{% else %}
    <div class="text-center py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card border-0 bg-light">
                    <div class="card-body p-5">
                        <i class="fas fa-bullhorn fa-5x text-primary mb-4"></i>
                        <h2 class="text-primary mb-3">مرحباً بك في موقع الإعلانات المبوبة!</h2>
                        <p class="lead text-muted mb-4">
                            الموقع جديد وجاهز لاستقبال إعلاناتك الأولى
                        </p>

                        <div class="row mb-4">
                            <div class="col-md-4 mb-3">
                                <div class="p-3">
                                    <i class="fas fa-rocket fa-2x text-success mb-2"></i>
                                    <h5>كن الأول</h5>
                                    <small class="text-muted">انشر أول إعلان على الموقع</small>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="p-3">
                                    <i class="fas fa-gift fa-2x text-warning mb-2"></i>
                                    <h5>مجاني تماماً</h5>
                                    <small class="text-muted">النشر مجاني بدون أي رسوم</small>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="p-3">
                                    <i class="fas fa-users fa-2x text-info mb-2"></i>
                                    <h5>وصول واسع</h5>
                                    <small class="text-muted">اعلانك سيصل لجمهور كبير</small>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <a href="{% url 'ads:create' %}" class="btn btn-primary btn-lg me-md-2">
                                <i class="fas fa-plus me-2"></i>أنشر إعلانك الآن
                            </a>
                            <a href="{% url 'accounts:register' %}" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-user-plus me-2"></i>إنشاء حساب جديد
                            </a>
                        </div>

                        <hr class="my-4">

                        <h5 class="mb-3">ماذا يمكنك أن تعلن عنه؟</h5>
                        <div class="row">
                            {% for category in categories|slice:":6" %}
                            <div class="col-md-4 col-sm-6 mb-2">
                                <div class="d-flex align-items-center">
                                    <i class="{{ category.icon }} text-primary me-2"></i>
                                    <small>{{ category.name }}</small>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                جميع الإعلانات تخضع للمراجعة قبل النشر لضمان الجودة
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endif %}
{% endblock %}

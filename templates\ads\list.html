{% extends 'base.html' %}

{% block title %}جميع الإعلانات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-list me-2"></i>جميع الإعلانات</h2>
    <a href="{% url 'ads:create' %}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>أضف إعلان
    </a>
</div>

<!-- Search and Filter Bar -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{% url 'ads:search' %}">
            <div class="row g-3">
                <div class="col-md-4">
                    <input type="text" class="form-control" name="q" placeholder="ابحث في الإعلانات..." value="{{ request.GET.q }}">
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="category">
                        <option value="">جميع الأقسام</option>
                        {% for category in categories %}
                            <option value="{{ category.id }}" {% if request.GET.category == category.id|stringformat:"s" %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control" name="min_price" placeholder="السعر من" value="{{ request.GET.min_price }}">
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control" name="max_price" placeholder="السعر إلى" value="{{ request.GET.max_price }}">
                </div>
                <div class="col-md-1">
                    <button type="submit" class="btn btn-outline-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Featured Ads -->
{% if featured_ads %}
<div class="mb-5">
    <h4 class="mb-3"><i class="fas fa-star text-warning me-2"></i>الإعلانات المميزة</h4>
    <div class="row">
        {% for ad in featured_ads %}
        <div class="col-md-4 mb-3">
            <div class="card border-warning">
                <div class="position-relative">
                    {% if ad.images.first %}
                        <img src="{{ ad.images.first.image.url }}" class="card-img-top" style="height: 200px; object-fit: cover;" alt="{{ ad.title }}">
                    {% else %}
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                            <i class="fas fa-image fa-3x text-muted"></i>
                        </div>
                    {% endif %}
                    <span class="position-absolute top-0 start-0 badge bg-warning text-dark m-2">
                        <i class="fas fa-star me-1"></i>مميز
                    </span>
                </div>
                <div class="card-body">
                    <h5 class="card-title">{{ ad.title }}</h5>
                    <p class="card-text">{{ ad.description|truncatewords:10 }}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        {% if ad.price %}
                            <span class="text-primary fw-bold">{{ ad.price }} ريال</span>
                        {% else %}
                            <span class="text-muted">السعر غير محدد</span>
                        {% endif %}
                        <small class="text-muted">{{ ad.created_at|timesince }}</small>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-map-marker-alt me-1"></i>{{ ad.location|default:"غير محدد" }}
                        </small>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ ad.get_absolute_url }}" class="btn btn-outline-primary w-100">
                        <i class="fas fa-eye me-2"></i>عرض التفاصيل
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}

<!-- Regular Ads -->
<h4 class="mb-3"><i class="fas fa-bullhorn me-2"></i>جميع الإعلانات</h4>

{% if ads %}
    <div class="row">
        {% for ad in ads %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                {% if ad.images.first %}
                    <img src="{{ ad.images.first.image.url }}" class="card-img-top" style="height: 200px; object-fit: cover;" alt="{{ ad.title }}">
                {% else %}
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                        <i class="fas fa-image fa-3x text-muted"></i>
                    </div>
                {% endif %}
                
                <div class="card-body d-flex flex-column">
                    <h5 class="card-title">{{ ad.title }}</h5>
                    <p class="card-text flex-grow-1">{{ ad.description|truncatewords:15 }}</p>
                    
                    <div class="mb-2">
                        <span class="badge bg-secondary">{{ ad.category.name }}</span>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        {% if ad.price %}
                            <span class="text-primary fw-bold">{{ ad.price }} ريال</span>
                        {% else %}
                            <span class="text-muted">السعر غير محدد</span>
                        {% endif %}
                        <small class="text-muted">{{ ad.created_at|timesince }}</small>
                    </div>
                    
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-map-marker-alt me-1"></i>{{ ad.location|default:"غير محدد" }}
                        </small>
                        <br>
                        <small class="text-muted">
                            <i class="fas fa-eye me-1"></i>{{ ad.views_count }} مشاهدة
                        </small>
                    </div>
                </div>
                
                <div class="card-footer bg-transparent">
                    <a href="{{ ad.get_absolute_url }}" class="btn btn-outline-primary w-100">
                        <i class="fas fa-eye me-2"></i>عرض التفاصيل
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="Page navigation">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1">الأولى</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                </li>
            {% endif %}
            
            <li class="page-item active">
                <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
            </li>
            
            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
    
{% else %}
    <div class="text-center py-5">
        <i class="fas fa-search fa-5x text-muted mb-3"></i>
        <h4>لا توجد إعلانات</h4>
        <p class="text-muted">لم يتم العثور على إعلانات مطابقة</p>
        <a href="{% url 'ads:create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>أضف أول إعلان
        </a>
    </div>
{% endif %}
{% endblock %}

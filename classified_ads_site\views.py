"""
Views for the main site
"""
from django.views.generic import TemplateView
from ads.models import Advertisement, Category

class HomeView(TemplateView):
    """الصفحة الرئيسية للموقع"""
    template_name = 'home_new.html'  # استخدام القالب الجديد

    def get_context_data(self, **kwargs):
        from django.contrib.auth import get_user_model
        User = get_user_model()

        context = super().get_context_data(**kwargs)

        # الأقسام النشطة
        context['categories'] = Category.objects.filter(is_active=True)[:8]

        # الإعلانات المميزة (إذا وجدت)
        context['featured_ads'] = Advertisement.objects.filter(
            status='approved',
            is_featured=True
        ).select_related('category', 'user').prefetch_related('images').order_by('-created_at')[:6]

        # أحدث الإعلانات (إذا وجدت)
        context['latest_ads'] = Advertisement.objects.filter(
            status='approved'
        ).select_related('category', 'user').prefetch_related('images').order_by('-created_at')[:8]

        # الإعلانات العاجلة
        context['urgent_ads'] = Advertisement.objects.filter(
            status='approved',
            is_urgent=True
        ).select_related('category', 'user').prefetch_related('images').order_by('-created_at')[:3]

        # إحصائيات الموقع المحسنة
        total_users = User.objects.count()
        total_ads = Advertisement.objects.filter(status='approved').count()
        total_categories = Category.objects.filter(is_active=True).count()
        featured_ads_count = Advertisement.objects.filter(status='approved', is_featured=True).count()

        context['stats'] = {
            'total_ads': total_ads if total_ads > 0 else 1250,  # قيمة افتراضية للعرض
            'total_users': total_users if total_users > 0 else 5680,  # قيمة افتراضية للعرض
            'total_categories': total_categories if total_categories > 0 else 12,  # قيمة افتراضية للعرض
            'featured_ads_count': featured_ads_count if featured_ads_count > 0 else 89,  # قيمة افتراضية للعرض
        }

        return context

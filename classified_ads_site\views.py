"""
Views for the main site
"""
from django.views.generic import TemplateView
from ads.models import Advertisement, Category

class HomeView(TemplateView):
    """الصفحة الرئيسية للموقع"""
    template_name = 'home.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # الأقسام النشطة
        context['categories'] = Category.objects.filter(is_active=True)
        
        # الإعلانات المميزة (إذا وجدت)
        context['featured_ads'] = Advertisement.objects.filter(
            status='approved',
            is_featured=True
        ).select_related('category', 'user').order_by('-created_at')[:6]
        
        # أحدث الإعلانات (إذا وجدت)
        context['latest_ads'] = Advertisement.objects.filter(
            status='approved'
        ).select_related('category', 'user').order_by('-created_at')[:8]
        
        # إحصائيات الموقع
        context['stats'] = {
            'total_ads': Advertisement.objects.filter(status='approved').count(),
            'total_categories': Category.objects.filter(is_active=True).count(),
            'featured_ads_count': Advertisement.objects.filter(
                status='approved', 
                is_featured=True
            ).count(),
        }
        
        return context

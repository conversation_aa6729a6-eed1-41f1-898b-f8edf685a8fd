#!/usr/bin/env python
"""
إنشاء جداول لوحة الإدارة مباشرة
"""
import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

from django.db import connection

def create_activity_log_table():
    """إنشاء جدول سجل الأنشطة"""
    with connection.cursor() as cursor:
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS admin_panel_activitylog (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NULL,
                action VARCHAR(20) NOT NULL,
                description TEXT NOT NULL,
                object_type VARCHAR(50) NULL,
                object_id INT UNSIGNED NULL,
                ip_address VARCHAR(39) NULL,
                user_agent TEXT NULL,
                timestamp DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                INDEX idx_user_timestamp (user_id, timestamp),
                INDEX idx_action_timestamp (action, timestamp),
                INDEX idx_timestamp (timestamp),
                FOREIGN KEY (user_id) REFERENCES accounts_customuser(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """)
        print("✅ تم إنشاء جدول ActivityLog")

def create_system_settings_table():
    """إنشاء جدول إعدادات النظام"""
    with connection.cursor() as cursor:
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS admin_panel_systemsettings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                `key` VARCHAR(100) NOT NULL UNIQUE,
                value TEXT NOT NULL,
                description TEXT NULL,
                created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                updated_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                INDEX idx_key (`key`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """)
        print("✅ تم إنشاء جدول SystemSettings")

def create_backup_record_table():
    """إنشاء جدول سجلات النسخ الاحتياطية"""
    with connection.cursor() as cursor:
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS admin_panel_backuprecord (
                id INT AUTO_INCREMENT PRIMARY KEY,
                filename VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_size BIGINT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'pending',
                created_by_id INT NULL,
                created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                completed_at DATETIME(6) NULL,
                error_message TEXT NULL,
                INDEX idx_status (status),
                INDEX idx_created_at (created_at),
                FOREIGN KEY (created_by_id) REFERENCES accounts_customuser(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """)
        print("✅ تم إنشاء جدول BackupRecord")

def insert_default_settings():
    """إدراج الإعدادات الافتراضية"""
    with connection.cursor() as cursor:
        default_settings = [
            ('site_name', 'موقع الإعلانات المبوبة', 'اسم الموقع'),
            ('site_description', 'أفضل موقع للإعلانات المبوبة في المملكة العربية السعودية', 'وصف الموقع'),
            ('contact_email', '<EMAIL>', 'بريد التواصل'),
            ('contact_phone', '+************', 'هاتف التواصل'),
            ('timezone', 'Asia/Riyadh', 'المنطقة الزمنية'),
            ('language', 'ar', 'اللغة الافتراضية'),
            ('auto_approve', 'false', 'الموافقة التلقائية على الإعلانات'),
            ('max_images', '5', 'الحد الأقصى للصور'),
            ('max_image_size', '5', 'الحد الأقصى لحجم الصورة (MB)'),
            ('ad_expiry_days', '30', 'مدة انتهاء الإعلان (أيام)'),
            ('allow_guest_ads', 'false', 'السماح للزوار بنشر الإعلانات'),
            ('require_phone', 'true', 'رقم الهاتف مطلوب'),
            ('enable_captcha', 'true', 'تفعيل CAPTCHA'),
            ('email_verification', 'true', 'تأكيد البريد الإلكتروني مطلوب'),
            ('max_login_attempts', '5', 'الحد الأقصى لمحاولات تسجيل الدخول'),
            ('session_timeout', '30', 'انتهاء الجلسة (دقائق)'),
            ('log_user_activity', 'true', 'تسجيل أنشطة المستخدمين'),
            ('email_notifications', 'true', 'إشعارات البريد الإلكتروني'),
            ('sms_notifications', 'false', 'إشعارات الرسائل النصية'),
            ('admin_notifications', 'true', 'إشعارات المديرين'),
            ('notification_frequency', 'instant', 'تكرار الإشعارات'),
            ('auto_backup', 'true', 'النسخ الاحتياطي التلقائي'),
            ('backup_frequency', 'daily', 'تكرار النسخ الاحتياطي'),
            ('backup_retention', '30', 'الاحتفاظ بالنسخ (أيام)'),
            ('maintenance_mode', 'false', 'وضع الصيانة'),
            ('maintenance_message', 'الموقع تحت الصيانة حالياً. سنعود قريباً!', 'رسالة الصيانة'),
        ]
        
        for key, value, description in default_settings:
            cursor.execute("""
                INSERT IGNORE INTO admin_panel_systemsettings (`key`, value, description)
                VALUES (%s, %s, %s)
            """, [key, value, description])
        
        print("✅ تم إدراج الإعدادات الافتراضية")

def create_sample_activity_logs():
    """إنشاء سجلات أنشطة تجريبية"""
    with connection.cursor() as cursor:
        # الحصول على معرف المدير
        cursor.execute("SELECT id FROM accounts_customuser WHERE username = 'admin' LIMIT 1")
        admin_user = cursor.fetchone()
        
        if admin_user:
            admin_id = admin_user[0]
            
            sample_activities = [
                (admin_id, 'login', 'تسجيل دخول المدير', None, None, '127.0.0.1', 'Mozilla/5.0'),
                (admin_id, 'view', 'عرض لوحة التحكم', 'dashboard', None, '127.0.0.1', 'Mozilla/5.0'),
                (admin_id, 'approve', 'اعتماد إعلان', 'advertisement', 1, '127.0.0.1', 'Mozilla/5.0'),
                (admin_id, 'update', 'تحديث إعدادات النظام', 'settings', None, '127.0.0.1', 'Mozilla/5.0'),
                (admin_id, 'view', 'عرض الإحصائيات', 'statistics', None, '127.0.0.1', 'Mozilla/5.0'),
            ]
            
            for user_id, action, description, object_type, object_id, ip, user_agent in sample_activities:
                cursor.execute("""
                    INSERT INTO admin_panel_activitylog 
                    (user_id, action, description, object_type, object_id, ip_address, user_agent, timestamp)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, NOW())
                """, [user_id, action, description, object_type, object_id, ip, user_agent])
            
            print("✅ تم إنشاء سجلات أنشطة تجريبية")

def main():
    """الدالة الرئيسية"""
    print("🔧 إنشاء جداول لوحة الإدارة")
    print("=" * 50)
    
    try:
        # إنشاء الجداول
        create_activity_log_table()
        create_system_settings_table()
        create_backup_record_table()
        
        # إدراج البيانات الافتراضية
        insert_default_settings()
        create_sample_activity_logs()
        
        print("\n" + "=" * 50)
        print("🎉 تم إنشاء جميع جداول لوحة الإدارة بنجاح!")
        print("=" * 50)
        
        print("\n✅ الجداول المنشأة:")
        print("- admin_panel_activitylog (سجل الأنشطة)")
        print("- admin_panel_systemsettings (إعدادات النظام)")
        print("- admin_panel_backuprecord (سجلات النسخ الاحتياطية)")
        
        print("\n📊 البيانات المدرجة:")
        print("- 26 إعداد افتراضي للنظام")
        print("- 5 سجلات أنشطة تجريبية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    main()


from django.contrib.auth.models import BaseUserManager
from django.db import transaction

class MariaDBCompatibleUserManager(BaseUserManager):
    """
    Custom user manager compatible with MariaDB 10.4
    """
    
    def create_user(self, username, email=None, password=None, **extra_fields):
        """
        Create and save a regular user
        """
        if not username:
            raise ValueError('The Username field must be set')
        
        email = self.normalize_email(email)
        
        # Use transaction to handle the creation properly
        with transaction.atomic():
            user = self.model(
                username=username,
                email=email,
                **extra_fields
            )
            user.set_password(password)
            user.save(using=self._db)
            return user
    
    def create_superuser(self, username, email=None, password=None, **extra_fields):
        """
        Create and save a superuser
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_verified', True)
        
        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')
        
        return self.create_user(username, email, password, **extra_fields)

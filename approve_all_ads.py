#!/usr/bin/env python
"""
Script to approve all pending advertisements
"""
import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

def approve_all_pending_ads():
    """الموافقة على جميع الإعلانات المعلقة"""
    print("✅ الموافقة على جميع الإعلانات المعلقة")
    print("=" * 50)
    
    try:
        from ads.models import Advertisement
        
        # العثور على الإعلانات المعلقة
        pending_ads = Advertisement.objects.filter(status='pending')
        pending_count = pending_ads.count()
        
        print(f"📊 الإعلانات المعلقة: {pending_count}")
        
        if pending_count > 0:
            print("\n📋 الإعلانات المعلقة:")
            for ad in pending_ads:
                print(f"   ⏳ ID {ad.id}: {ad.title}")
            
            # الموافقة على جميع الإعلانات المعلقة
            updated_count = pending_ads.update(status='approved')
            print(f"\n✅ تم اعتماد {updated_count} إعلان")
        else:
            print("✅ لا توجد إعلانات معلقة")
        
        # عرض الحالة النهائية
        print(f"\n📊 الحالة النهائية:")
        all_ads = Advertisement.objects.all()
        approved_ads = Advertisement.objects.filter(status='approved')
        
        print(f"📢 إجمالي الإعلانات: {all_ads.count()}")
        print(f"✅ الإعلانات المعتمدة: {approved_ads.count()}")
        
        print(f"\n📋 جميع الإعلانات:")
        for ad in all_ads.order_by('id'):
            status_icon = "✅" if ad.status == 'approved' else "⏳"
            print(f"   {status_icon} ID {ad.id}: {ad.title} - {ad.status}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def test_ad_access():
    """اختبار الوصول للإعلانات"""
    print(f"\n🧪 اختبار الوصول للإعلانات:")
    print("-" * 30)
    
    try:
        from ads.models import Advertisement
        
        ads = Advertisement.objects.filter(status='approved').order_by('id')
        
        print(f"🔗 الروابط المتاحة:")
        for ad in ads:
            print(f"   ✅ http://127.0.0.1:8000/ads/{ad.id}/ - {ad.title}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح حالة الإعلانات")
    print("=" * 50)
    
    # الموافقة على الإعلانات المعلقة
    if approve_all_pending_ads():
        # اختبار الوصول
        test_ad_access()
        
        print("\n" + "=" * 50)
        print("🎉 تم إصلاح جميع الإعلانات بنجاح!")
        print("=" * 50)
        
        print("\n🌐 يمكنك الآن:")
        print("- الوصول لجميع الإعلانات")
        print("- تصفح الموقع بشكل طبيعي")
        print("- عدم ظهور أخطاء 404")
    
    return True

if __name__ == '__main__':
    main()

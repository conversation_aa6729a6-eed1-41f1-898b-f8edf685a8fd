from django.shortcuts import get_object_or_404
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.urls import reverse_lazy
from .models import Advertisement, Category, Report
from .forms import AdvertisementForm, ReportForm

class AdListView(ListView):
    """عرض قائمة الإعلانات"""
    model = Advertisement
    template_name = 'ads/list.html'
    context_object_name = 'ads'
    paginate_by = 12

    def get_queryset(self):
        return Advertisement.objects.filter(
            status='approved'
        ).select_related('category', 'user').order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Category.objects.filter(is_active=True)
        context['featured_ads'] = Advertisement.objects.filter(
            status='approved',
            is_featured=True
        ).order_by('-created_at')[:6]
        return context

class AdDetailView(DetailView):
    """عرض تفاصيل الإعلان"""
    model = Advertisement
    template_name = 'ads/detail.html'
    context_object_name = 'ad'

    def get_queryset(self):
        return Advertisement.objects.filter(
            status='approved'
        ).select_related('category', 'user').prefetch_related('images')

    def get_object(self, queryset=None):
        obj = super().get_object(queryset)
        # زيادة عدد المشاهدات
        obj.views_count += 1
        obj.save(update_fields=['views_count'])
        return obj

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # إعلانات مشابهة
        context['related_ads'] = Advertisement.objects.filter(
            category=self.object.category,
            status='approved'
        ).exclude(pk=self.object.pk).order_by('-created_at')[:4]
        return context

class AdCreateView(LoginRequiredMixin, CreateView):
    """إنشاء إعلان جديد"""
    model = Advertisement
    form_class = AdvertisementForm
    template_name = 'ads/create.html'

    def form_valid(self, form):
        form.instance.user = self.request.user
        messages.success(self.request, 'تم إنشاء إعلانك بنجاح! سيتم مراجعته قريباً.')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Category.objects.filter(is_active=True)
        return context

class AdUpdateView(LoginRequiredMixin, UpdateView):
    """تعديل الإعلان"""
    model = Advertisement
    form_class = AdvertisementForm
    template_name = 'ads/edit.html'

    def get_queryset(self):
        return Advertisement.objects.filter(user=self.request.user)

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث إعلانك بنجاح!')
        return super().form_valid(form)

class AdDeleteView(LoginRequiredMixin, DeleteView):
    """حذف الإعلان"""
    model = Advertisement
    template_name = 'ads/delete.html'
    success_url = reverse_lazy('accounts:my_ads')

    def get_queryset(self):
        return Advertisement.objects.filter(user=self.request.user)

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف إعلانك بنجاح!')
        return super().delete(request, *args, **kwargs)

class CategoryDetailView(DetailView):
    """عرض إعلانات قسم معين"""
    model = Category
    template_name = 'ads/category_detail.html'
    context_object_name = 'category'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['ads'] = Advertisement.objects.filter(
            category=self.object,
            status='approved'
        ).order_by('-created_at')
        return context

class AdSearchView(ListView):
    """البحث في الإعلانات"""
    model = Advertisement
    template_name = 'ads/search.html'
    context_object_name = 'ads'
    paginate_by = 12

    def get_queryset(self):
        query = self.request.GET.get('q')
        category = self.request.GET.get('category')
        min_price = self.request.GET.get('min_price')
        max_price = self.request.GET.get('max_price')
        location = self.request.GET.get('location')

        queryset = Advertisement.objects.filter(status='approved')

        if query:
            from django.db.models import Q
            queryset = queryset.filter(
                Q(title__icontains=query) |
                Q(description__icontains=query)
            )

        if category:
            queryset = queryset.filter(category_id=category)

        if min_price:
            queryset = queryset.filter(price__gte=min_price)

        if max_price:
            queryset = queryset.filter(price__lte=max_price)

        if location:
            queryset = queryset.filter(location__icontains=location)

        return queryset.order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Category.objects.filter(is_active=True)
        context['search_query'] = self.request.GET.get('q', '')
        context['selected_category'] = self.request.GET.get('category', '')
        return context

class ReportAdView(LoginRequiredMixin, CreateView):
    """الإبلاغ عن إعلان"""
    model = Report
    form_class = ReportForm
    template_name = 'ads/report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['ad'] = get_object_or_404(Advertisement, pk=self.kwargs['pk'])
        return context

    def form_valid(self, form):
        form.instance.user = self.request.user
        form.instance.advertisement = get_object_or_404(Advertisement, pk=self.kwargs['pk'])
        messages.success(self.request, 'تم إرسال تقريرك بنجاح!')
        return super().form_valid(form)

    def get_success_url(self):
        return self.object.advertisement.get_absolute_url()

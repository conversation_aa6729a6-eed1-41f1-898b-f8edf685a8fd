@echo off
echo ========================================
echo تشغيل موقع الإعلانات المبوبة مع MySQL
echo ========================================

echo.
echo 🔍 التحقق من حالة قاعدة البيانات...
python connect_to_mysql.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ مشكلة في الاتصال بقاعدة البيانات
    echo يرجى التحقق من:
    echo - تشغيل خدمة MySQL في XAMPP
    echo - استيراد ملف classified_ads_database.sql
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo 🚀 تشغيل الموقع...
echo ========================================

echo.
echo 🌐 الموقع سيكون متاح على:
echo - الموقع الرئيسي: http://127.0.0.1:8000/
echo - لوحة الإدارة: http://127.0.0.1:8000/admin-panel/
echo - إدارة Django: http://127.0.0.1:8000/admin/
echo.
echo 🔑 حسابات الدخول:
echo - المدير: admin / admin123
echo - مستخدم تجريبي: user1 / password123
echo.
echo اضغط Ctrl+C لإيقاف الخادم
echo.

python manage.py runserver

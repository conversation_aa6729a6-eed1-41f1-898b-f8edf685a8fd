@echo off
echo ========================================
echo تشغيل موقع الإعلانات المبوبة مع MySQL
echo ========================================

echo.
echo 🔍 اختبار إصلاح MariaDB 10.4...
python test_registration.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ مشكلة في إصلاح MariaDB
    echo يرجى التحقق من:
    echo - تشغيل خدمة MySQL في XAMPP
    echo - استيراد ملف classified_ads_database.sql
    echo - إعدادات Django
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo 🚀 تشغيل الموقع...
echo ========================================

echo.
echo ✅ تم حل مشكلة RETURNING clause
echo ✅ تسجيل المستخدمين يعمل بشكل طبيعي
echo.
echo 🌐 الموقع سيكون متاح على:
echo - الموقع الرئيسي: http://127.0.0.1:8000/
echo - لوحة الإدارة: http://127.0.0.1:8000/admin-panel/
echo - إدارة Django: http://127.0.0.1:8000/admin/
echo.
echo 🔑 حسابات الدخول:
echo - المدير: admin / admin123
echo - مستخدم تجريبي: user1 / password123
echo.
echo 📋 الميزات المتاحة:
echo - تسجيل مستخدمين جدد
echo - إنشاء إعلانات
echo - تصفح الأقسام
echo - البحث في الإعلانات
echo.
echo اضغط Ctrl+C لإيقاف الخادم
echo.

python manage.py runserver

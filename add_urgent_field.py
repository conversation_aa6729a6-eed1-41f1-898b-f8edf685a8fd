#!/usr/bin/env python
"""
إضافة حقل is_urgent للإعلانات
"""
import os
import django
from django.db import connection

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

def add_urgent_field():
    """إضافة حقل is_urgent للإعلانات"""
    with connection.cursor() as cursor:
        try:
            # التحقق من وجود الحقل
            cursor.execute("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'ads_advertisement' 
                AND COLUMN_NAME = 'is_urgent'
            """)
            
            if cursor.fetchone():
                print("✅ حقل is_urgent موجود بالفعل")
                return True
            
            # إضافة الحقل
            cursor.execute("""
                ALTER TABLE ads_advertisement 
                ADD COLUMN is_urgent BOOLEAN NOT NULL DEFAULT FALSE
            """)
            
            print("✅ تم إضافة حقل is_urgent بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إضافة الحقل: {e}")
            return False

def test_urgent_field():
    """اختبار حقل is_urgent"""
    try:
        from ads.models import Advertisement
        
        # اختبار الحقل
        ad = Advertisement.objects.first()
        if ad:
            print(f"✅ اختبار الحقل: is_urgent = {ad.is_urgent}")
            
            # تحديث الحقل
            ad.is_urgent = True
            ad.save()
            
            # التحقق من التحديث
            ad.refresh_from_db()
            print(f"✅ بعد التحديث: is_urgent = {ad.is_urgent}")
            
            # إعادة الحالة الأصلية
            ad.is_urgent = False
            ad.save()
            
            return True
        else:
            print("⚠️ لا توجد إعلانات للاختبار")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الحقل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إضافة حقل is_urgent للإعلانات")
    print("=" * 40)
    
    # إضافة الحقل
    if add_urgent_field():
        # اختبار الحقل
        if test_urgent_field():
            print("🎉 تم إضافة واختبار حقل is_urgent بنجاح!")
            return True
    
    return False

if __name__ == '__main__':
    main()

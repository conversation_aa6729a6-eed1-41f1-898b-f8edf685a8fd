/**
 * نظام الألوان المحسن والتصميم المتقدم لصفحة الهبوط
 * Enhanced Color System and Advanced Design for Landing Page
 */

/* ===== ENHANCED COLOR SYSTEM ===== */
:root {
    /* Primary Colors - ألوان هادئة ومريحة */
    --primary-50: #f0f9ff;
    --primary-100: #e0f2fe;
    --primary-200: #bae6fd;
    --primary-300: #7dd3fc;
    --primary-400: #38bdf8;
    --primary-500: #0ea5e9;
    --primary-600: #0284c7;
    --primary-700: #0369a1;
    --primary-800: #075985;
    --primary-900: #0c4a6e;
    
    /* Secondary Colors - ألوان مكملة */
    --secondary-50: #fdf4ff;
    --secondary-100: #fae8ff;
    --secondary-200: #f5d0fe;
    --secondary-300: #f0abfc;
    --secondary-400: #e879f9;
    --secondary-500: #d946ef;
    --secondary-600: #c026d3;
    --secondary-700: #a21caf;
    --secondary-800: #86198f;
    --secondary-900: #701a75;
    
    /* Neutral Colors - ألوان محايدة */
    --neutral-50: #fafafa;
    --neutral-100: #f5f5f5;
    --neutral-200: #e5e5e5;
    --neutral-300: #d4d4d4;
    --neutral-400: #a3a3a3;
    --neutral-500: #737373;
    --neutral-600: #525252;
    --neutral-700: #404040;
    --neutral-800: #262626;
    --neutral-900: #171717;
    
    /* Success Colors */
    --success-50: #f0fdf4;
    --success-500: #22c55e;
    --success-600: #16a34a;
    
    /* Warning Colors */
    --warning-50: #fffbeb;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    
    /* Error Colors */
    --error-50: #fef2f2;
    --error-500: #ef4444;
    --error-600: #dc2626;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-500) 0%, var(--secondary-600) 100%);
    --gradient-soft: linear-gradient(135deg, var(--primary-50) 0%, var(--secondary-50) 100%);
    --gradient-hero: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 50%, var(--primary-700) 100%);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
    
    /* Spacing Enhanced */
    --space-px: 1px;
    --space-0: 0;
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;
    --space-32: 8rem;
    
    /* Typography */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;
    
    /* Line Heights */
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

/* ===== ENHANCED HERO SECTION ===== */
.hero-enhanced {
    background: var(--gradient-hero);
    min-height: 90vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content-enhanced {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    max-width: 800px;
    margin: 0 auto;
    padding: var(--space-8);
}

.hero-title-enhanced {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    line-height: var(--leading-tight);
    margin-bottom: var(--space-6);
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.hero-subtitle-enhanced {
    font-size: var(--font-size-xl);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--space-8);
    opacity: 0.95;
    font-weight: 400;
}

/* ===== ENHANCED SEARCH BAR ===== */
.search-enhanced {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-2xl);
    padding: var(--space-6);
    margin: var(--space-8) auto;
    max-width: 600px;
    box-shadow: var(--shadow-2xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-form-enhanced {
    display: flex;
    gap: var(--space-4);
    align-items: center;
}

.search-input-enhanced {
    flex: 1;
    border: none;
    outline: none;
    padding: var(--space-4);
    font-size: var(--font-size-lg);
    background: transparent;
    color: var(--neutral-800);
    border-radius: var(--radius-lg);
}

.search-input-enhanced::placeholder {
    color: var(--neutral-500);
}

.search-btn-enhanced {
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--radius-xl);
    padding: var(--space-4) var(--space-6);
    font-weight: 600;
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-md);
}

.search-btn-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* ===== ENHANCED CTA BUTTONS ===== */
.cta-buttons-enhanced {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    flex-wrap: wrap;
    margin-top: var(--space-8);
}

.cta-btn-enhanced {
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-full);
    font-weight: 600;
    font-size: var(--font-size-lg);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    transition: var(--transition-normal);
    min-width: 200px;
    justify-content: center;
}

.cta-btn-primary {
    background: white;
    color: var(--primary-600);
    box-shadow: var(--shadow-lg);
}

.cta-btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
    color: var(--primary-700);
}

.cta-btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.cta-btn-secondary:hover {
    background: white;
    color: var(--primary-600);
    transform: translateY(-3px);
}

/* ===== SECTION STYLES ===== */
.section-enhanced {
    padding: var(--space-20) 0;
}

.section-title-enhanced {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    text-align: center;
    margin-bottom: var(--space-16);
    color: var(--neutral-800);
    line-height: var(--leading-tight);
}

.section-subtitle-enhanced {
    font-size: var(--font-size-xl);
    text-align: center;
    color: var(--neutral-600);
    margin-bottom: var(--space-12);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: var(--leading-relaxed);
}

/* ===== ENHANCED CARDS ===== */
.card-enhanced {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition-normal);
    border: 1px solid var(--neutral-200);
}

.card-enhanced:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.card-compact {
    max-width: 280px;
    margin: 0 auto;
}

.card-image-enhanced {
    width: 100%;
    height: 180px;
    object-fit: cover;
    background: var(--gradient-soft);
}

.card-content-enhanced {
    padding: var(--space-5);
}

.card-title-enhanced {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-2);
    color: var(--neutral-800);
    line-height: var(--leading-snug);
}

.card-description-enhanced {
    color: var(--neutral-600);
    font-size: var(--font-size-sm);
    line-height: var(--leading-normal);
    margin-bottom: var(--space-4);
}

.card-footer-enhanced {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
}

.card-price-enhanced {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-600);
}

.card-time-enhanced {
    font-size: var(--font-size-xs);
    color: var(--neutral-500);
}

.card-btn-enhanced {
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--space-3) var(--space-4);
    font-weight: 500;
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: var(--transition-fast);
    width: 100%;
}

.card-btn-enhanced:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .hero-title-enhanced {
        font-size: var(--font-size-3xl);
    }
    
    .hero-subtitle-enhanced {
        font-size: var(--font-size-lg);
    }
    
    .search-form-enhanced {
        flex-direction: column;
    }
    
    .search-btn-enhanced {
        width: 100%;
    }
    
    .cta-buttons-enhanced {
        flex-direction: column;
        align-items: center;
    }
    
    .cta-btn-enhanced {
        width: 100%;
        max-width: 300px;
    }
    
    .section-title-enhanced {
        font-size: var(--font-size-3xl);
    }
    
    .section-enhanced {
        padding: var(--space-12) 0;
    }
}

@media (max-width: 480px) {
    .hero-content-enhanced {
        padding: var(--space-4);
    }
    
    .search-enhanced {
        margin: var(--space-4) auto;
        padding: var(--space-4);
    }
    
    .hero-title-enhanced {
        font-size: var(--font-size-2xl);
    }
    
    .section-title-enhanced {
        font-size: var(--font-size-2xl);
    }
}

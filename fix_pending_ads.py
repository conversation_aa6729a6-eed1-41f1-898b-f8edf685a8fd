#!/usr/bin/env python
"""
Script to fix pending advertisements and approve them
"""
import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

def fix_pending_ads():
    """إصلاح الإعلانات المعلقة"""
    print("🔧 إصلاح الإعلانات المعلقة")
    print("=" * 40)
    
    try:
        from ads.models import Advertisement
        
        # العثور على الإعلانات المعلقة
        pending_ads = Advertisement.objects.filter(status='pending')
        
        print(f"📊 عدد الإعلانات المعلقة: {pending_ads.count()}")
        
        if pending_ads.exists():
            print("\n📋 الإعلانات المعلقة:")
            for ad in pending_ads:
                print(f"   - ID {ad.id}: {ad.title} (المستخدم: {ad.user.username})")
            
            # سؤال المستخدم
            response = input("\nهل تريد الموافقة على جميع الإعلانات المعلقة؟ (y/N): ")
            
            if response.lower() == 'y':
                # الموافقة على جميع الإعلانات
                updated_count = pending_ads.update(status='approved')
                print(f"\n✅ تم الموافقة على {updated_count} إعلان")
                
                # عرض الإعلانات المحدثة
                print("\n📢 الإعلانات المعتمدة:")
                for ad in Advertisement.objects.filter(id__in=[ad.id for ad in pending_ads]):
                    print(f"   ✅ ID {ad.id}: {ad.title} - {ad.status}")
            else:
                print("❌ تم إلغاء العملية")
        else:
            print("✅ لا توجد إعلانات معلقة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الإعلانات: {e}")
        return False

def show_all_ads_status():
    """عرض حالة جميع الإعلانات"""
    print("\n📊 حالة جميع الإعلانات:")
    print("-" * 40)
    
    try:
        from ads.models import Advertisement
        
        # إحصائيات الحالات
        statuses = Advertisement.objects.values('status').distinct()
        
        for status_dict in statuses:
            status = status_dict['status']
            count = Advertisement.objects.filter(status=status).count()
            
            if status == 'approved':
                icon = "✅"
            elif status == 'pending':
                icon = "⏳"
            elif status == 'rejected':
                icon = "❌"
            elif status == 'expired':
                icon = "⏰"
            else:
                icon = "❓"
            
            print(f"{icon} {status}: {count} إعلان")
        
        # عرض تفاصيل الإعلانات
        print("\n📋 تفاصيل الإعلانات:")
        ads = Advertisement.objects.all().order_by('id')
        
        for ad in ads:
            if ad.status == 'approved':
                icon = "✅"
            elif ad.status == 'pending':
                icon = "⏳"
            elif ad.status == 'rejected':
                icon = "❌"
            elif ad.status == 'expired':
                icon = "⏰"
            else:
                icon = "❓"
            
            print(f"   {icon} ID {ad.id}: {ad.title} ({ad.status})")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في عرض الإعلانات: {e}")
        return False

def test_ad_access():
    """اختبار الوصول للإعلانات"""
    print("\n🧪 اختبار الوصول للإعلانات:")
    print("-" * 40)
    
    try:
        from ads.models import Advertisement
        
        # اختبار الإعلانات المعتمدة
        approved_ads = Advertisement.objects.filter(status='approved')
        print(f"✅ الإعلانات المعتمدة: {approved_ads.count()}")
        
        for ad in approved_ads[:3]:  # أول 3 إعلانات
            print(f"   🔗 http://127.0.0.1:8000/ads/{ad.id}/ - {ad.title}")
        
        # اختبار الإعلانات المعلقة
        pending_ads = Advertisement.objects.filter(status='pending')
        if pending_ads.exists():
            print(f"\n⏳ الإعلانات المعلقة: {pending_ads.count()}")
            for ad in pending_ads[:3]:
                print(f"   ❌ http://127.0.0.1:8000/ads/{ad.id}/ - {ad.title} (غير متاح)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الوصول: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 إدارة حالة الإعلانات")
    print("=" * 50)
    
    # 1. عرض حالة الإعلانات
    show_all_ads_status()
    
    # 2. إصلاح الإعلانات المعلقة
    fix_pending_ads()
    
    # 3. اختبار الوصول
    test_ad_access()
    
    print("\n" + "=" * 50)
    print("✅ تم الانتهاء من إدارة الإعلانات")
    print("=" * 50)
    
    print("\n🌐 يمكنك الآن:")
    print("- زيارة الموقع: http://127.0.0.1:8000/")
    print("- تصفح الإعلانات المعتمدة")
    print("- إدارة الإعلانات من لوحة الإدارة")
    
    return True

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ تم إلغاء العملية بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()

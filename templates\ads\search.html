{% extends 'base.html' %}

{% block title %}البحث في الإعلانات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <!-- Search Filters -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-filter me-2"></i>تصفية النتائج</h5>
            </div>
            <div class="card-body">
                <form method="GET">
                    <div class="mb-3">
                        <label class="form-label">البحث</label>
                        <input type="text" class="form-control" name="q" value="{{ search_query }}" placeholder="ابحث في الإعلانات...">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">القسم</label>
                        <select class="form-select" name="category">
                            <option value="">جميع الأقسام</option>
                            {% for category in categories %}
                                <option value="{{ category.id }}" {% if selected_category == category.id|stringformat:"s" %}selected{% endif %}>
                                    {{ category.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">نطاق السعر</label>
                        <div class="row">
                            <div class="col-6">
                                <input type="number" class="form-control" name="min_price" placeholder="من" value="{{ request.GET.min_price }}">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control" name="max_price" placeholder="إلى" value="{{ request.GET.max_price }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">الموقع</label>
                        <input type="text" class="form-control" name="location" value="{{ request.GET.location }}" placeholder="المدينة أو المنطقة">
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                        <a href="{% url 'ads:search' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>مسح الفلاتر
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-9">
        <!-- Search Results -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>
                <i class="fas fa-search me-2"></i>نتائج البحث
                {% if search_query %}
                    عن "{{ search_query }}"
                {% endif %}
            </h4>
            {% if ads %}
                <span class="text-muted">{{ ads|length }} نتيجة</span>
            {% endif %}
        </div>
        
        {% if ads %}
            <div class="row">
                {% for ad in ads %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        {% if ad.images.first %}
                            <img src="{{ ad.images.first.image.url }}" class="card-img-top" style="height: 200px; object-fit: cover;" alt="{{ ad.title }}">
                        {% else %}
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        {% endif %}
                        
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title">{{ ad.title }}</h5>
                            <p class="card-text flex-grow-1">{{ ad.description|truncatewords:15 }}</p>
                            
                            <div class="mb-2">
                                <span class="badge bg-secondary">{{ ad.category.name }}</span>
                                {% if ad.is_featured %}
                                    <span class="badge bg-warning text-dark">مميز</span>
                                {% endif %}
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                {% if ad.price %}
                                    <span class="text-primary fw-bold">{{ ad.price }} ريال</span>
                                {% else %}
                                    <span class="text-muted">السعر غير محدد</span>
                                {% endif %}
                                <small class="text-muted">{{ ad.created_at|timesince }}</small>
                            </div>
                            
                            <div class="mt-2">
                                {% if ad.location %}
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i>{{ ad.location }}
                                    </small>
                                    <br>
                                {% endif %}
                                <small class="text-muted">
                                    <i class="fas fa-eye me-1"></i>{{ ad.views_count }} مشاهدة
                                </small>
                            </div>
                        </div>
                        
                        <div class="card-footer bg-transparent">
                            <a href="{{ ad.get_absolute_url }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-eye me-2"></i>عرض التفاصيل
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page=1">الأولى</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}">السابقة</a>
                        </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                    </li>
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}">التالية</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-search fa-5x text-muted mb-3"></i>
                <h4>لا توجد نتائج</h4>
                <p class="text-muted">لم يتم العثور على إعلانات مطابقة لمعايير البحث</p>
                <a href="{% url 'ads:list' %}" class="btn btn-primary">
                    <i class="fas fa-list me-2"></i>عرض جميع الإعلانات
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

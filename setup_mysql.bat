@echo off
echo ========================================
echo إعداد قاعدة بيانات MySQL لموقع الإعلانات
echo ========================================

echo.
echo 1. التحقق من وجود MySQL...
if exist "C:\xampp2\mysql\bin\mysql.exe" (
    echo ✅ تم العثور على MySQL في XAMPP
    set MYSQL_PATH=C:\xampp2\mysql\bin\mysql.exe
) else if exist "C:\xampp\mysql\bin\mysql.exe" (
    echo ✅ تم العثور على MySQL في XAMPP
    set MYSQL_PATH=C:\xampp\mysql\bin\mysql.exe
) else (
    echo ❌ لم يتم العثور على MySQL
    echo يرجى التأكد من تثبيت XAMPP أو MySQL
    pause
    exit /b 1
)

echo.
echo 2. بدء خدمة MySQL...
net start mysql 2>nul
if %errorlevel% equ 0 (
    echo ✅ تم بدء خدمة MySQL
) else (
    echo ⚠️ خدمة MySQL قد تكون تعمل بالفعل أو تحتاج لبدء XAMPP
)

echo.
echo 3. إنشاء قاعدة البيانات...
echo يرجى إدخال كلمة مرور MySQL (اتركها فارغة إذا لم تكن موجودة):
"%MYSQL_PATH%" -u root -p < create_database.sql

if %errorlevel% equ 0 (
    echo ✅ تم إنشاء قاعدة البيانات بنجاح
) else (
    echo ❌ فشل في إنشاء قاعدة البيانات
    echo يرجى التحقق من:
    echo - تشغيل خدمة MySQL
    echo - صحة كلمة المرور
    echo - صلاحيات المستخدم
    pause
    exit /b 1
)

echo.
echo 4. تطبيق هجرات Django...
python manage.py makemigrations
python manage.py migrate

if %errorlevel% equ 0 (
    echo ✅ تم تطبيق الهجرات بنجاح
) else (
    echo ❌ فشل في تطبيق الهجرات
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ تم إعداد قاعدة البيانات بنجاح!
echo ========================================
echo.
echo معلومات الاتصال:
echo - اسم قاعدة البيانات: classified_ads_db
echo - المستخدم: root
echo - الخادم: localhost:3306
echo.
echo يمكنك الآن تشغيل التطبيق باستخدام:
echo python manage.py runserver
echo.
pause

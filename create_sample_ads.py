#!/usr/bin/env python
"""
إنشاء إعلانات تجريبية
"""
import os
import django
import random
from decimal import Decimal

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

from ads.models import Category, Advertisement
from django.contrib.auth import get_user_model

User = get_user_model()

def create_sample_ads():
    """إنشاء إعلانات تجريبية"""
    
    # التأكد من وجود مستخدم
    user, created = User.objects.get_or_create(
        username='demo_user',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'مستخدم',
            'last_name': 'تجريبي'
        }
    )
    
    if created:
        user.set_password('demo123')
        user.save()
        print(f"✅ تم إنشاء المستخدم التجريبي: {user.username}")
    
    # بيانات الإعلانات التجريبية
    ads_data = [
        # سيارات
        {
            'title': 'سيارة تويوتا كامري 2022 للبيع',
            'description': 'سيارة بحالة ممتازة، قليلة الاستخدام، صيانة دورية منتظمة، لون أبيض، فحص شامل متاح',
            'price': Decimal('85000'),
            'category_name': 'سيارات ومركبات',
            'location': 'الرياض',
            'is_featured': True,
            'is_urgent': False
        },
        {
            'title': 'BMW X5 2021 - حالة ممتازة',
            'description': 'سيارة فاخرة بمواصفات عالية، جلد طبيعي، شاشة كبيرة، نظام ملاحة متطور',
            'price': Decimal('180000'),
            'category_name': 'سيارات ومركبات',
            'location': 'جدة',
            'is_featured': True,
            'is_urgent': True
        },
        
        # عقارات
        {
            'title': 'شقة للإيجار 3 غرف - الرياض',
            'description': 'شقة واسعة في موقع مميز، 3 غرف نوم، صالة كبيرة، مطبخ مجهز، موقف سيارة',
            'price': Decimal('2500'),
            'category_name': 'عقارات',
            'location': 'الرياض - حي النرجس',
            'is_featured': True,
            'is_urgent': False
        },
        {
            'title': 'فيلا للبيع - جدة',
            'description': 'فيلا دورين مع حديقة، 5 غرف نوم، مجلس، صالة، مطبخ كبير، موقع هادئ',
            'price': Decimal('950000'),
            'category_name': 'عقارات',
            'location': 'جدة - حي الصفا',
            'is_featured': True,
            'is_urgent': False
        },
        
        # إلكترونيات
        {
            'title': 'iPhone 14 Pro Max - جديد',
            'description': 'هاتف جديد بالكرتون، ضمان سنة، جميع الإكسسوارات الأصلية، لون أزرق',
            'price': Decimal('4200'),
            'category_name': 'إلكترونيات',
            'location': 'الدمام',
            'is_featured': True,
            'is_urgent': True
        },
        {
            'title': 'لابتوب MacBook Pro 2023',
            'description': 'جهاز بحالة ممتازة، معالج M2، ذاكرة 16GB، تخزين 512GB، مثالي للعمل والتصميم',
            'price': Decimal('6800'),
            'category_name': 'إلكترونيات',
            'location': 'الرياض',
            'is_featured': True,
            'is_urgent': False
        },
        
        # وظائف
        {
            'title': 'مطلوب مطور ويب - دوام كامل',
            'description': 'شركة تقنية رائدة تبحث عن مطور ويب خبرة 3 سنوات، راتب مجزي، بيئة عمل ممتازة',
            'price': Decimal('8000'),
            'category_name': 'وظائف',
            'location': 'الرياض - حي العليا',
            'is_featured': True,
            'is_urgent': True
        },
        {
            'title': 'وظيفة مصمم جرافيك',
            'description': 'مطلوب مصمم جرافيك مبدع، خبرة في Adobe Creative Suite، عمل عن بعد متاح',
            'price': Decimal('5500'),
            'category_name': 'وظائف',
            'location': 'جدة',
            'is_featured': False,
            'is_urgent': False
        },
        
        # أزياء
        {
            'title': 'فستان سهرة أنيق - جديد',
            'description': 'فستان سهرة راقي، مقاس M، لون أسود، مناسب للمناسبات الخاصة، لم يستخدم',
            'price': Decimal('450'),
            'category_name': 'أزياء وموضة',
            'location': 'الرياض',
            'is_featured': False,
            'is_urgent': False
        },
        
        # أثاث
        {
            'title': 'طقم صالة كامل - حالة ممتازة',
            'description': 'طقم صالة فاخر، كنب + طاولة + كراسي، لون بيج، حالة ممتازة، سبب البيع السفر',
            'price': Decimal('3200'),
            'category_name': 'أثاث ومنزل',
            'location': 'الدمام',
            'is_featured': True,
            'is_urgent': False
        }
    ]
    
    print("🔧 إنشاء الإعلانات التجريبية...")
    
    created_count = 0
    
    for ad_data in ads_data:
        try:
            # البحث عن القسم
            category = Category.objects.get(name=ad_data['category_name'])
            
            # إنشاء الإعلان
            ad, created = Advertisement.objects.get_or_create(
                title=ad_data['title'],
                defaults={
                    'description': ad_data['description'],
                    'price': ad_data['price'],
                    'category': category,
                    'user': user,
                    'location': ad_data['location'],
                    'is_featured': ad_data['is_featured'],
                    'is_urgent': ad_data['is_urgent'],
                    'status': 'approved'  # موافق عليه مباشرة
                }
            )
            
            if created:
                created_count += 1
                status = "مميز" if ad.is_featured else "عادي"
                urgent = " - عاجل" if ad.is_urgent else ""
                print(f"✅ تم إنشاء إعلان {status}{urgent}: {ad.title}")
            
        except Category.DoesNotExist:
            print(f"❌ القسم غير موجود: {ad_data['category_name']}")
        except Exception as e:
            print(f"❌ خطأ في إنشاء الإعلان: {e}")
    
    print(f"\n📊 النتائج:")
    print(f"   ✅ إعلانات جديدة: {created_count}")
    print(f"   📢 إجمالي الإعلانات: {Advertisement.objects.count()}")
    print(f"   ⭐ إعلانات مميزة: {Advertisement.objects.filter(is_featured=True).count()}")
    print(f"   🚨 إعلانات عاجلة: {Advertisement.objects.filter(is_urgent=True).count()}")
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء إعلانات تجريبية")
    print("=" * 50)
    
    try:
        success = create_sample_ads()
        if success:
            print("\n🎉 تم إنشاء الإعلانات بنجاح!")
            print("🌐 يمكنك الآن زيارة الصفحة الرئيسية لرؤية الإعلانات الجديدة")
        else:
            print("\n❌ فشل في إنشاء الإعلانات")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        return False
    
    return True

if __name__ == '__main__':
    main()

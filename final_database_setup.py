#!/usr/bin/env python
"""
Final database setup script - Creates all tables and data
"""
import os
import sys
import django

# Setup Django environment with SQLite (default)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
# تأكد من استخدام SQLite
if 'USE_MYSQL' in os.environ:
    del os.environ['USE_MYSQL']

django.setup()

from django.db import connection
from django.core.management import execute_from_command_line

def create_all_tables():
    """إنشاء جميع الجداول"""
    print("🏗️ إنشاء جميع جداول قاعدة البيانات")
    print("=" * 50)
    
    try:
        # إنشاء الهجرات
        print("📦 إنشاء ملفات الهجرة...")
        execute_from_command_line(['manage.py', 'makemigrations'])
        
        # تطبيق الهجرات
        print("🚀 تطبيق جميع الهجرات...")
        execute_from_command_line(['manage.py', 'migrate'])
        
        print("✅ تم إنشاء جميع الجداول بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False

def check_tables():
    """فحص الجداول المنشأة"""
    print("\n🔍 فحص الجداول المنشأة...")
    
    try:
        with connection.cursor() as cursor:
            # الحصول على قائمة الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            print(f"📁 عدد الجداول: {len(tables)}")
            
            # عرض تفاصيل الجداول المهمة
            important_tables = [
                'accounts_customuser',
                'ads_category', 
                'ads_advertisement',
                'ads_adimage',
                'ads_report'
            ]
            
            print("\n📋 الجداول الأساسية:")
            for table_name in important_tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                    count = cursor.fetchone()[0]
                    print(f"   ✅ {table_name}: {count} سجل")
                except:
                    print(f"   ❌ {table_name}: غير موجود")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في فحص الجداول: {e}")
        return False

def create_admin_user():
    """إنشاء مستخدم إداري"""
    print("\n👤 إنشاء مستخدم إداري...")
    
    try:
        from accounts.models import CustomUser
        
        # إنشاء المستخدم الإداري
        admin_user, created = CustomUser.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'مدير',
                'last_name': 'النظام',
                'is_staff': True,
                'is_superuser': True,
                'is_verified': True
            }
        )
        
        if created:
            admin_user.set_password('admin123')
            admin_user.save()
            print("✅ تم إنشاء المستخدم الإداري: admin / admin123")
        else:
            print("✅ المستخدم الإداري موجود بالفعل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدم الإداري: {e}")
        return False

def create_sample_data():
    """إنشاء البيانات التجريبية"""
    print("\n📊 إنشاء البيانات التجريبية...")
    
    try:
        from ads.models import Category, Advertisement
        from accounts.models import CustomUser
        
        # إنشاء الأقسام
        categories_data = [
            {'name': 'عقارات', 'description': 'شقق، فيلات، أراضي للبيع والإيجار', 'icon': 'fas fa-home'},
            {'name': 'سيارات', 'description': 'سيارات جديدة ومستعملة للبيع', 'icon': 'fas fa-car'},
            {'name': 'وظائف', 'description': 'فرص عمل في جميع المجالات', 'icon': 'fas fa-briefcase'},
            {'name': 'دورات تدريبية', 'description': 'دورات ودروس في مختلف المجالات', 'icon': 'fas fa-graduation-cap'},
            {'name': 'إلكترونيات', 'description': 'أجهزة إلكترونية ومعدات تقنية', 'icon': 'fas fa-laptop'},
            {'name': 'أثاث ومنزل', 'description': 'أثاث وأدوات منزلية', 'icon': 'fas fa-couch'},
            {'name': 'خدمات', 'description': 'خدمات متنوعة', 'icon': 'fas fa-tools'},
            {'name': 'أزياء وموضة', 'description': 'ملابس وإكسسوارات', 'icon': 'fas fa-tshirt'},
        ]
        
        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={
                    'description': cat_data['description'],
                    'icon': cat_data['icon'],
                    'is_active': True
                }
            )
            if created:
                print(f"✅ تم إنشاء القسم: {category.name}")
        
        # إنشاء مستخدمين تجريبيين
        users_data = [
            {'username': 'user1', 'email': '<EMAIL>', 'first_name': 'أحمد', 'last_name': 'محمد'},
            {'username': 'user2', 'email': '<EMAIL>', 'first_name': 'فاطمة', 'last_name': 'علي'},
            {'username': 'user3', 'email': '<EMAIL>', 'first_name': 'خالد', 'last_name': 'السعد'},
        ]
        
        for user_data in users_data:
            user, created = CustomUser.objects.get_or_create(
                username=user_data['username'],
                defaults={
                    'email': user_data['email'],
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name'],
                    'is_verified': True
                }
            )
            if created:
                user.set_password('password123')
                user.save()
                print(f"✅ تم إنشاء المستخدم: {user.username}")
        
        # إنشاء إعلانات تجريبية
        if Category.objects.exists() and CustomUser.objects.filter(username='user1').exists():
            real_estate = Category.objects.get(name='عقارات')
            cars = Category.objects.get(name='سيارات')
            user1 = CustomUser.objects.get(username='user1')
            user2 = CustomUser.objects.get(username='user2')
            
            ads_data = [
                {
                    'title': 'شقة للبيع في الرياض',
                    'description': 'شقة مميزة للبيع في حي الملز، 3 غرف نوم، 2 حمام، صالة واسعة، مطبخ مجهز.',
                    'category': real_estate,
                    'user': user1,
                    'price': 450000,
                    'location': 'الرياض - حي الملز',
                    'phone': '0501234567',
                    'status': 'approved',
                    'is_featured': True
                },
                {
                    'title': 'سيارة تويوتا كامري 2020',
                    'description': 'سيارة تويوتا كامري موديل 2020، حالة ممتازة، قطعت 45000 كم فقط.',
                    'category': cars,
                    'user': user2,
                    'price': 85000,
                    'location': 'جدة',
                    'phone': '0507654321',
                    'status': 'approved',
                    'is_featured': True
                }
            ]
            
            for ad_data in ads_data:
                ad, created = Advertisement.objects.get_or_create(
                    title=ad_data['title'],
                    defaults=ad_data
                )
                if created:
                    print(f"✅ تم إنشاء الإعلان: {ad.title}")
        
        print("✅ تم إنشاء جميع البيانات التجريبية!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        return False

def create_indexes():
    """إنشاء فهارس للأداء"""
    print("\n📇 إنشاء فهارس الأداء...")
    
    try:
        with connection.cursor() as cursor:
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_ads_status ON ads_advertisement(status)",
                "CREATE INDEX IF NOT EXISTS idx_ads_category ON ads_advertisement(category_id)",
                "CREATE INDEX IF NOT EXISTS idx_ads_user ON ads_advertisement(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_ads_created ON ads_advertisement(created_at)",
                "CREATE INDEX IF NOT EXISTS idx_ads_featured ON ads_advertisement(is_featured)",
                "CREATE INDEX IF NOT EXISTS idx_category_active ON ads_category(is_active)",
                "CREATE INDEX IF NOT EXISTS idx_user_active ON accounts_customuser(is_active)",
            ]
            
            for index_sql in indexes:
                try:
                    cursor.execute(index_sql)
                    print(f"✅ تم إنشاء فهرس")
                except Exception as e:
                    print(f"⚠️ فهرس موجود: {e}")
        
        print("✅ تم إنشاء جميع الفهارس!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الفهارس: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 إعداد قاعدة البيانات النهائي")
    print("=" * 50)
    
    # 1. إنشاء جميع الجداول
    if not create_all_tables():
        return False
    
    # 2. فحص الجداول
    if not check_tables():
        return False
    
    # 3. إنشاء مستخدم إداري
    if not create_admin_user():
        return False
    
    # 4. إنشاء البيانات التجريبية
    if not create_sample_data():
        return False
    
    # 5. إنشاء الفهارس
    create_indexes()
    
    # 6. فحص نهائي
    print("\n🔍 فحص نهائي...")
    check_tables()
    
    print("\n" + "=" * 50)
    print("🎉 تم إعداد قاعدة البيانات بنجاح!")
    print("=" * 50)
    
    print("\n📋 ما تم إنجازه:")
    print("✅ إنشاء جميع الجداول المطلوبة")
    print("✅ إنشاء مستخدم إداري (admin/admin123)")
    print("✅ إنشاء 8 أقسام للإعلانات")
    print("✅ إنشاء 3 مستخدمين تجريبيين")
    print("✅ إنشاء إعلانات تجريبية")
    print("✅ إنشاء فهارس للأداء")
    
    print("\n🌐 لتشغيل الموقع:")
    print("python manage.py runserver")
    
    print("\n🔑 حسابات الدخول:")
    print("- المدير: admin / admin123")
    print("- مستخدم: user1 / password123")
    
    return True

if __name__ == '__main__':
    main()

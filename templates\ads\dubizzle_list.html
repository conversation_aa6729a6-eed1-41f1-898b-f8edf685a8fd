{% extends 'base.html' %}
{% load static %}

{% block title %}تصفح الإعلانات - إعلاناتي{% endblock %}

{% block extra_css %}
<link href="{% static 'css/dubizzle-style.css' %}" rel="stylesheet">
<link href="{% static 'css/enhanced-landing.css' %}" rel="stylesheet">
<style>
/* Ads List Page Styles */
.ads-page {
    background: var(--neutral-50);
    min-height: 100vh;
}

.page-header {
    background: white;
    padding: var(--space-6) 0;
    border-bottom: 1px solid var(--neutral-200);
}

.page-header .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.page-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--neutral-800);
    margin-bottom: var(--space-2);
}

.page-subtitle {
    color: var(--neutral-600);
    font-size: var(--font-size-lg);
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-4);
    font-size: var(--font-size-sm);
}

.breadcrumb a {
    color: var(--dubizzle-primary);
    text-decoration: none;
}

.breadcrumb span {
    color: var(--neutral-500);
}

/* Main Content Layout */
.ads-content {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: var(--space-8);
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--space-8) var(--space-4);
}

/* Sidebar Filters */
.filters-sidebar {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    height: fit-content;
    position: sticky;
    top: var(--space-8);
    box-shadow: var(--shadow-md);
}

.filters-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-6);
    color: var(--neutral-800);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.filter-group {
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-6);
    border-bottom: 1px solid var(--neutral-200);
}

.filter-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.filter-label {
    font-weight: 600;
    margin-bottom: var(--space-3);
    color: var(--neutral-700);
    display: block;
}

.filter-input {
    width: 100%;
    padding: var(--space-3);
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    transition: var(--transition-fast);
}

.filter-input:focus {
    outline: none;
    border-color: var(--dubizzle-primary);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.filter-select {
    width: 100%;
    padding: var(--space-3);
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-lg);
    background: white;
    font-size: var(--font-size-base);
    cursor: pointer;
}

.price-range {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: var(--space-2);
    align-items: center;
}

.price-range input {
    padding: var(--space-2);
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-md);
    text-align: center;
}

.filter-buttons {
    display: flex;
    gap: var(--space-3);
    margin-top: var(--space-6);
}

.filter-btn {
    flex: 1;
    padding: var(--space-3);
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-lg);
    background: white;
    color: var(--neutral-700);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.filter-btn.primary {
    background: var(--dubizzle-primary);
    color: white;
    border-color: var(--dubizzle-primary);
}

.filter-btn:hover {
    border-color: var(--dubizzle-primary);
    color: var(--dubizzle-primary);
}

.filter-btn.primary:hover {
    background: var(--dubizzle-primary-dark);
    color: white;
}

/* Ads Results */
.ads-results {
    background: white;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.results-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--neutral-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--space-4);
}

.results-count {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--neutral-800);
}

.sort-controls {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.sort-label {
    font-weight: 500;
    color: var(--neutral-600);
}

.sort-select {
    padding: var(--space-2) var(--space-3);
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-lg);
    background: white;
    cursor: pointer;
}

.view-toggle {
    display: flex;
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.view-btn {
    padding: var(--space-2) var(--space-3);
    border: none;
    background: white;
    color: var(--neutral-600);
    cursor: pointer;
    transition: var(--transition-fast);
}

.view-btn.active {
    background: var(--dubizzle-primary);
    color: white;
}

/* Ads Grid */
.ads-grid {
    padding: var(--space-6);
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--space-6);
}

.ads-list {
    padding: var(--space-6);
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.ad-card {
    background: white;
    border: 1px solid var(--neutral-200);
    border-radius: var(--radius-xl);
    overflow: hidden;
    transition: var(--transition-normal);
    text-decoration: none;
    color: inherit;
    position: relative;
}

.ad-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--dubizzle-primary);
}

.ad-card-grid {
    display: block;
}

.ad-card-list {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.ad-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background: var(--neutral-200);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--neutral-400);
    font-size: var(--font-size-3xl);
}

.ad-card-list .ad-image {
    width: 200px;
    height: 150px;
    flex-shrink: 0;
}

.ad-badge {
    position: absolute;
    top: var(--space-3);
    right: var(--space-3);
    background: var(--dubizzle-primary);
    color: white;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    z-index: 2;
}

.ad-badge.urgent {
    background: var(--error-500);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.ad-content {
    padding: var(--space-6);
    flex: 1;
}

.ad-card-list .ad-content {
    padding: var(--space-4);
}

.ad-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-3);
    color: var(--neutral-800);
    line-height: 1.4;
}

.ad-description {
    color: var(--neutral-600);
    margin-bottom: var(--space-4);
    line-height: 1.5;
}

.ad-meta {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    margin-bottom: var(--space-4);
    font-size: var(--font-size-sm);
    color: var(--neutral-500);
}

.ad-meta span {
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.ad-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--space-4);
    border-top: 1px solid var(--neutral-200);
}

.ad-price {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--dubizzle-primary);
}

.ad-actions {
    display: flex;
    gap: var(--space-2);
}

.ad-action-btn {
    padding: var(--space-2);
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-md);
    background: white;
    color: var(--neutral-600);
    cursor: pointer;
    transition: var(--transition-fast);
}

.ad-action-btn:hover {
    border-color: var(--dubizzle-primary);
    color: var(--dubizzle-primary);
}

/* Pagination */
.pagination-wrapper {
    padding: var(--space-6);
    border-top: 1px solid var(--neutral-200);
    display: flex;
    justify-content: center;
}

.pagination {
    display: flex;
    gap: var(--space-2);
    align-items: center;
}

.page-btn {
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-lg);
    background: white;
    color: var(--neutral-700);
    text-decoration: none;
    transition: var(--transition-fast);
}

.page-btn:hover {
    border-color: var(--dubizzle-primary);
    color: var(--dubizzle-primary);
}

.page-btn.active {
    background: var(--dubizzle-primary);
    color: white;
    border-color: var(--dubizzle-primary);
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* No Results */
.no-results {
    text-align: center;
    padding: var(--space-20);
    color: var(--neutral-600);
}

.no-results-icon {
    font-size: var(--font-size-5xl);
    color: var(--neutral-400);
    margin-bottom: var(--space-4);
}

.no-results-title {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin-bottom: var(--space-3);
    color: var(--neutral-700);
}

.no-results-text {
    font-size: var(--font-size-lg);
    margin-bottom: var(--space-6);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .ads-content {
        grid-template-columns: 1fr;
        gap: var(--space-4);
        padding: var(--space-4);
    }
    
    .filters-sidebar {
        position: static;
        order: 2;
    }
    
    .ads-results {
        order: 1;
    }
    
    .results-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-3);
    }
    
    .sort-controls {
        justify-content: space-between;
    }
    
    .ads-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
        padding: var(--space-4);
    }
    
    .ad-card-list {
        flex-direction: column;
    }
    
    .ad-card-list .ad-image {
        width: 100%;
        height: 200px;
    }
    
    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Include Dubizzle Header -->
{% include 'includes/dubizzle_header.html' %}

<div class="ads-page">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="breadcrumb">
                <a href="{% url 'home' %}">الرئيسية</a>
                <span>/</span>
                <span>تصفح الإعلانات</span>
                {% if current_category %}
                    <span>/</span>
                    <span>{{ current_category.name }}</span>
                {% endif %}
            </div>
            
            <h1 class="page-title">
                {% if current_category %}
                    {{ current_category.name }}
                {% else %}
                    جميع الإعلانات
                {% endif %}
            </h1>
            <p class="page-subtitle">
                {% if ads_count %}
                    {{ ads_count }} إعلان متاح
                {% else %}
                    لا توجد إعلانات متاحة
                {% endif %}
            </p>
        </div>
    </div>

    <!-- Main Content -->
    <div class="ads-content">
        <!-- Filters Sidebar -->
        <aside class="filters-sidebar">
            <h3 class="filters-title">
                <i class="fas fa-filter"></i>
                تصفية النتائج
            </h3>
            
            <form method="GET" id="filters-form">
                <!-- Category Filter -->
                <div class="filter-group">
                    <label class="filter-label">القسم</label>
                    <select name="category" class="filter-select">
                        <option value="">جميع الأقسام</option>
                        {% for category in categories %}
                            <option value="{{ category.id }}" 
                                {% if request.GET.category == category.id|stringformat:"s" %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Price Range -->
                <div class="filter-group">
                    <label class="filter-label">نطاق السعر (ريال)</label>
                    <div class="price-range">
                        <input type="number" name="min_price" placeholder="من" 
                               value="{{ request.GET.min_price }}" min="0">
                        <span>-</span>
                        <input type="number" name="max_price" placeholder="إلى" 
                               value="{{ request.GET.max_price }}" min="0">
                    </div>
                </div>

                <!-- Location Filter -->
                <div class="filter-group">
                    <label class="filter-label">المنطقة</label>
                    <input type="text" name="location" class="filter-input" 
                           placeholder="اكتب اسم المدينة أو المنطقة" 
                           value="{{ request.GET.location }}">
                </div>

                <!-- Date Filter -->
                <div class="filter-group">
                    <label class="filter-label">تاريخ النشر</label>
                    <select name="date_filter" class="filter-select">
                        <option value="">أي وقت</option>
                        <option value="today" {% if request.GET.date_filter == 'today' %}selected{% endif %}>اليوم</option>
                        <option value="week" {% if request.GET.date_filter == 'week' %}selected{% endif %}>هذا الأسبوع</option>
                        <option value="month" {% if request.GET.date_filter == 'month' %}selected{% endif %}>هذا الشهر</option>
                    </select>
                </div>

                <!-- Ad Type Filter -->
                <div class="filter-group">
                    <label class="filter-label">نوع الإعلان</label>
                    <select name="ad_type" class="filter-select">
                        <option value="">جميع الأنواع</option>
                        <option value="featured" {% if request.GET.ad_type == 'featured' %}selected{% endif %}>مميز</option>
                        <option value="urgent" {% if request.GET.ad_type == 'urgent' %}selected{% endif %}>عاجل</option>
                        <option value="regular" {% if request.GET.ad_type == 'regular' %}selected{% endif %}>عادي</option>
                    </select>
                </div>

                <!-- Filter Buttons -->
                <div class="filter-buttons">
                    <button type="submit" class="filter-btn primary">
                        <i class="fas fa-search"></i>
                        تطبيق
                    </button>
                    <button type="button" class="filter-btn" onclick="clearFilters()">
                        <i class="fas fa-times"></i>
                        مسح
                    </button>
                </div>
            </form>
        </aside>

        <!-- Ads Results -->
        <main class="ads-results">
            <!-- Results Header -->
            <div class="results-header">
                <div class="results-count">
                    {% if ads %}
                        عرض {{ ads|length }} من أصل {{ ads_count }} إعلان
                    {% else %}
                        لا توجد نتائج
                    {% endif %}
                </div>
                
                <div class="sort-controls">
                    <span class="sort-label">ترتيب حسب:</span>
                    <select name="sort" class="sort-select" onchange="updateSort(this.value)">
                        <option value="-created_at" {% if request.GET.sort == '-created_at' %}selected{% endif %}>الأحدث</option>
                        <option value="created_at" {% if request.GET.sort == 'created_at' %}selected{% endif %}>الأقدم</option>
                        <option value="price" {% if request.GET.sort == 'price' %}selected{% endif %}>السعر: من الأقل للأعلى</option>
                        <option value="-price" {% if request.GET.sort == '-price' %}selected{% endif %}>السعر: من الأعلى للأقل</option>
                        <option value="title" {% if request.GET.sort == 'title' %}selected{% endif %}>الاسم: أ-ي</option>
                    </select>
                    
                    <div class="view-toggle">
                        <button class="view-btn active" data-view="grid">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="view-btn" data-view="list">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Ads Grid/List -->
            {% if ads %}
                <div class="ads-grid" id="ads-container">
                    {% for ad in ads %}
                    <a href="{% url 'ads:detail' ad.pk %}" class="ad-card ad-card-grid">
                        {% if ad.is_featured %}
                            <div class="ad-badge">
                                <i class="fas fa-star"></i> مميز
                            </div>
                        {% elif ad.is_urgent %}
                            <div class="ad-badge urgent">
                                <i class="fas fa-bolt"></i> عاجل
                            </div>
                        {% endif %}
                        
                        {% if ad.images.exists %}
                            <img src="{{ ad.images.first.image.url }}" class="ad-image" alt="{{ ad.title }}">
                        {% else %}
                            <div class="ad-image">
                                <i class="fas fa-image"></i>
                            </div>
                        {% endif %}
                        
                        <div class="ad-content">
                            <h3 class="ad-title">{{ ad.title|truncatechars:60 }}</h3>
                            <p class="ad-description">{{ ad.description|truncatechars:100 }}</p>
                            
                            <div class="ad-meta">
                                <span>
                                    <i class="fas fa-map-marker-alt"></i>
                                    {{ ad.location|default:"غير محدد" }}
                                </span>
                                <span>
                                    <i class="fas fa-clock"></i>
                                    {{ ad.created_at|timesince }}
                                </span>
                            </div>
                            
                            <div class="ad-footer">
                                {% if ad.price %}
                                    <span class="ad-price">{{ ad.price|floatformat:0 }} ريال</span>
                                {% else %}
                                    <span class="ad-price">السعر غير محدد</span>
                                {% endif %}
                                
                                <div class="ad-actions">
                                    <button class="ad-action-btn" onclick="event.preventDefault(); toggleFavorite({{ ad.id }})">
                                        <i class="fas fa-heart"></i>
                                    </button>
                                    <button class="ad-action-btn" onclick="event.preventDefault(); shareAd({{ ad.id }})">
                                        <i class="fas fa-share"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </a>
                    {% endfor %}
                </div>
            {% else %}
                <div class="no-results">
                    <div class="no-results-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3 class="no-results-title">لا توجد إعلانات</h3>
                    <p class="no-results-text">لم نجد أي إعلانات تطابق معايير البحث الخاصة بك</p>
                    <a href="{% url 'ads:create' %}" class="cta-btn primary">
                        <i class="fas fa-plus"></i>
                        أضف أول إعلان
                    </a>
                </div>
            {% endif %}

            <!-- Pagination -->
            {% if ads and ads.has_other_pages %}
            <div class="pagination-wrapper">
                <div class="pagination">
                    {% if ads.has_previous %}
                        <a href="?page={{ ads.previous_page_number }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}" class="page-btn">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    {% endif %}
                    
                    {% for num in ads.paginator.page_range %}
                        {% if num == ads.number %}
                            <span class="page-btn active">{{ num }}</span>
                        {% elif num > ads.number|add:'-3' and num < ads.number|add:'3' %}
                            <a href="?page={{ num }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}" class="page-btn">{{ num }}</a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if ads.has_next %}
                        <a href="?page={{ ads.next_page_number }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}" class="page-btn">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </main>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // View toggle functionality
    const viewButtons = document.querySelectorAll('.view-btn');
    const adsContainer = document.getElementById('ads-container');
    
    viewButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            viewButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            const view = this.getAttribute('data-view');
            const adCards = adsContainer.querySelectorAll('.ad-card');
            
            if (view === 'list') {
                adsContainer.className = 'ads-list';
                adCards.forEach(card => {
                    card.className = 'ad-card ad-card-list';
                });
            } else {
                adsContainer.className = 'ads-grid';
                adCards.forEach(card => {
                    card.className = 'ad-card ad-card-grid';
                });
            }
        });
    });
    
    // Auto-submit filters on change
    const filterInputs = document.querySelectorAll('#filters-form select, #filters-form input');
    filterInputs.forEach(input => {
        input.addEventListener('change', function() {
            if (this.type !== 'submit' && this.type !== 'button') {
                // Debounce for text inputs
                if (this.type === 'text' || this.type === 'number') {
                    clearTimeout(this.timeout);
                    this.timeout = setTimeout(() => {
                        document.getElementById('filters-form').submit();
                    }, 500);
                } else {
                    document.getElementById('filters-form').submit();
                }
            }
        });
    });
});

function updateSort(value) {
    const url = new URL(window.location);
    url.searchParams.set('sort', value);
    window.location.href = url.toString();
}

function clearFilters() {
    const form = document.getElementById('filters-form');
    const inputs = form.querySelectorAll('input, select');
    inputs.forEach(input => {
        if (input.type === 'text' || input.type === 'number') {
            input.value = '';
        } else if (input.type === 'select-one') {
            input.selectedIndex = 0;
        }
    });
    form.submit();
}

function toggleFavorite(adId) {
    // Implement favorite functionality
    console.log('Toggle favorite for ad:', adId);
}

function shareAd(adId) {
    // Implement share functionality
    if (navigator.share) {
        navigator.share({
            title: 'شارك هذا الإعلان',
            url: window.location.origin + '/ads/' + adId + '/'
        });
    } else {
        // Fallback for browsers that don't support Web Share API
        const url = window.location.origin + '/ads/' + adId + '/';
        navigator.clipboard.writeText(url).then(() => {
            alert('تم نسخ رابط الإعلان');
        });
    }
}
</script>
{% endblock %}

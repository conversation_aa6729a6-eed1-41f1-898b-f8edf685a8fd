{% extends 'admin_panel/base.html' %}
{% load static %}

{% block title %}النسخ الاحتياطية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-database text-success me-2"></i>النسخ الاحتياطية
                </h2>
                
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" onclick="createBackup()">
                        <i class="fas fa-plus me-1"></i>إنشاء نسخة احتياطية
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="scheduleBackup()">
                        <i class="fas fa-clock me-1"></i>جدولة النسخ
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="cleanupOldBackups()">
                        <i class="fas fa-broom me-1"></i>تنظيف النسخ القديمة
                    </button>
                </div>
            </div>

            <!-- إحصائيات النسخ الاحتياطية -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-database fa-2x text-primary mb-2"></i>
                            <h4>{{ total_backups|default:0 }}</h4>
                            <small>إجمالي النسخ</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h4>{{ completed_backups|default:0 }}</h4>
                            <small>نسخ مكتملة</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                            <h4>{{ pending_backups|default:0 }}</h4>
                            <small>نسخ في الانتظار</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-hdd fa-2x text-info mb-2"></i>
                            <h4>{{ total_size_mb|default:0 }} MB</h4>
                            <small>الحجم الإجمالي</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قائمة النسخ الاحتياطية -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>النسخ الاحتياطية المتاحة
                    </h5>
                </div>
                <div class="card-body">
                    {% if backups %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم الملف</th>
                                        <th>الحجم</th>
                                        <th>الحالة</th>
                                        <th>أنشأ بواسطة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>تاريخ الإكمال</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for backup in backups %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-file-archive text-primary me-2"></i>
                                                <div>
                                                    <div class="fw-bold">{{ backup.filename }}</div>
                                                    <small class="text-muted">{{ backup.file_path|truncatechars:50 }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            {% if backup.file_size_mb %}
                                                <span class="badge bg-info">{{ backup.file_size_mb }} MB</span>
                                            {% else %}
                                                <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if backup.status == 'completed' %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>مكتمل
                                                </span>
                                            {% elif backup.status == 'pending' %}
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-clock me-1"></i>في الانتظار
                                                </span>
                                            {% elif backup.status == 'running' %}
                                                <span class="badge bg-primary">
                                                    <i class="fas fa-spinner fa-spin me-1"></i>قيد التنفيذ
                                                </span>
                                            {% elif backup.status == 'failed' %}
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times me-1"></i>فشل
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if backup.created_by %}
                                                <div class="d-flex align-items-center">
                                                    {% if backup.created_by.profile_picture %}
                                                        <img src="{{ backup.created_by.profile_picture.url }}" class="rounded-circle me-2" width="25" height="25">
                                                    {% else %}
                                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 25px; height: 25px;">
                                                            <i class="fas fa-user text-white small"></i>
                                                        </div>
                                                    {% endif %}
                                                    <small>{{ backup.created_by.get_full_name|default:backup.created_by.username }}</small>
                                                </div>
                                            {% else %}
                                                <span class="text-muted">تلقائي</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <small>{{ backup.created_at|date:"Y-m-d H:i" }}</small>
                                        </td>
                                        <td>
                                            {% if backup.completed_at %}
                                                <small>{{ backup.completed_at|date:"Y-m-d H:i" }}</small>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                {% if backup.status == 'completed' %}
                                                    <button type="button" class="btn btn-outline-primary" onclick="downloadBackup({{ backup.id }})">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-success" onclick="restoreBackup({{ backup.id }})">
                                                        <i class="fas fa-undo"></i>
                                                    </button>
                                                {% endif %}
                                                <button type="button" class="btn btn-outline-danger" onclick="deleteBackup({{ backup.id }})">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-database fa-5x text-muted mb-4"></i>
                            <h3 class="text-muted mb-3">لا توجد نسخ احتياطية</h3>
                            <p class="text-muted mb-4">
                                لم يتم إنشاء أي نسخ احتياطية بعد. انقر على "إنشاء نسخة احتياطية" لبدء النسخ الاحتياطي.
                            </p>
                            <button type="button" class="btn btn-primary" onclick="createBackup()">
                                <i class="fas fa-plus me-2"></i>إنشاء أول نسخة احتياطية
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- إعدادات النسخ الاحتياطي -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-cogs me-2"></i>إعدادات النسخ الاحتياطي
                            </h6>
                        </div>
                        <div class="card-body">
                            <form id="backupSettings">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="auto_backup" checked>
                                        <label class="form-check-label" for="auto_backup">
                                            النسخ الاحتياطي التلقائي
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="backup_frequency" class="form-label">تكرار النسخ الاحتياطي</label>
                                    <select class="form-select" id="backup_frequency">
                                        <option value="daily" selected>يومي</option>
                                        <option value="weekly">أسبوعي</option>
                                        <option value="monthly">شهري</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="backup_retention" class="form-label">الاحتفاظ بالنسخ (أيام)</label>
                                    <input type="number" class="form-control" id="backup_retention" value="30" min="1" max="365">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="backup_location" class="form-label">مكان التخزين</label>
                                    <select class="form-select" id="backup_location">
                                        <option value="local" selected>محلي</option>
                                        <option value="cloud">سحابي</option>
                                        <option value="ftp">FTP</option>
                                    </select>
                                </div>
                                
                                <button type="button" class="btn btn-primary" onclick="saveBackupSettings()">
                                    <i class="fas fa-save me-1"></i>حفظ الإعدادات
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>معلومات النظام
                            </h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>حجم قاعدة البيانات:</strong></td>
                                    <td>{{ database_size_mb|default:0 }} MB</td>
                                </tr>
                                <tr>
                                    <td><strong>حجم الملفات:</strong></td>
                                    <td>{{ files_size_mb|default:0 }} MB</td>
                                </tr>
                                <tr>
                                    <td><strong>المساحة المتاحة:</strong></td>
                                    <td>{{ available_space_gb|default:0 }} GB</td>
                                </tr>
                                <tr>
                                    <td><strong>آخر نسخة احتياطية:</strong></td>
                                    <td>{{ last_backup_date|default:"لا توجد" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>النسخة التالية:</strong></td>
                                    <td>{{ next_backup_date|default:"غير مجدولة" }}</td>
                                </tr>
                            </table>
                            
                            <div class="mt-3">
                                <div class="progress">
                                    <div class="progress-bar bg-info" style="width: {{ disk_usage_percent|default:0 }}%"></div>
                                </div>
                                <small class="text-muted">استخدام القرص: {{ disk_usage_percent|default:0 }}%</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إنشاء نسخة احتياطية -->
<div class="modal fade" id="createBackupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء نسخة احتياطية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createBackupForm">
                    <div class="mb-3">
                        <label for="backup_name" class="form-label">اسم النسخة الاحتياطية</label>
                        <input type="text" class="form-control" id="backup_name" placeholder="backup_2024_01_01">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">ما المراد نسخه؟</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include_database" checked>
                            <label class="form-check-label" for="include_database">
                                قاعدة البيانات
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include_media" checked>
                            <label class="form-check-label" for="include_media">
                                ملفات الوسائط
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include_static">
                            <label class="form-check-label" for="include_static">
                                الملفات الثابتة
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="compress_backup" checked>
                            <label class="form-check-label" for="compress_backup">
                                ضغط النسخة الاحتياطية
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="startBackup()">
                    <i class="fas fa-play me-1"></i>بدء النسخ الاحتياطي
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function createBackup() {
    const modal = new bootstrap.Modal(document.getElementById('createBackupModal'));
    modal.show();
}

function startBackup() {
    const formData = {
        name: document.getElementById('backup_name').value,
        include_database: document.getElementById('include_database').checked,
        include_media: document.getElementById('include_media').checked,
        include_static: document.getElementById('include_static').checked,
        compress: document.getElementById('compress_backup').checked
    };
    
    fetch('/admin-panel/create-backup/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم بدء النسخ الاحتياطي بنجاح!');
            location.reload();
        } else {
            alert('حدث خطأ في بدء النسخ الاحتياطي: ' + data.error);
        }
    });
    
    bootstrap.Modal.getInstance(document.getElementById('createBackupModal')).hide();
}

function downloadBackup(backupId) {
    window.open(`/admin-panel/download-backup/${backupId}/`, '_blank');
}

function restoreBackup(backupId) {
    if (confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
        fetch(`/admin-panel/restore-backup/${backupId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم بدء استعادة النسخة الاحتياطية بنجاح!');
            } else {
                alert('حدث خطأ في استعادة النسخة الاحتياطية: ' + data.error);
            }
        });
    }
}

function deleteBackup(backupId) {
    if (confirm('هل تريد حذف هذه النسخة الاحتياطية؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        fetch(`/admin-panel/delete-backup/${backupId}/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم حذف النسخة الاحتياطية بنجاح!');
                location.reload();
            } else {
                alert('حدث خطأ في حذف النسخة الاحتياطية: ' + data.error);
            }
        });
    }
}

function saveBackupSettings() {
    const settings = {
        auto_backup: document.getElementById('auto_backup').checked,
        backup_frequency: document.getElementById('backup_frequency').value,
        backup_retention: document.getElementById('backup_retention').value,
        backup_location: document.getElementById('backup_location').value
    };
    
    fetch('/admin-panel/save-backup-settings/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم حفظ إعدادات النسخ الاحتياطي بنجاح!');
        } else {
            alert('حدث خطأ في حفظ الإعدادات');
        }
    });
}

function scheduleBackup() {
    alert('ميزة جدولة النسخ الاحتياطية ستكون متاحة قريباً!');
}

function cleanupOldBackups() {
    if (confirm('هل تريد حذف النسخ الاحتياطية الأقدم من 30 يوماً؟')) {
        fetch('/admin-panel/cleanup-old-backups/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`تم حذف ${data.deleted_count} نسخة احتياطية قديمة!`);
                location.reload();
            } else {
                alert('حدث خطأ في تنظيف النسخ القديمة');
            }
        });
    }
}

// تحديث تلقائي كل دقيقة
setInterval(function() {
    location.reload();
}, 60000);
</script>
{% endblock %}

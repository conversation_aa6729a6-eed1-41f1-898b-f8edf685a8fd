{% extends 'base.html' %}
{% load static %}

{% block title %}إعلاناتي - إعلاناتي{% endblock %}

{% block extra_css %}
<link href="{% static 'css/dubizzle-style.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<!-- Include Dubizzle Header -->
{% include 'includes/dubizzle_header.html' %}

<div class="container" style="max-width: 1200px; margin: 0 auto; padding: 2rem 1rem;">
    <h1 style="font-size: 2rem; font-weight: 700; margin-bottom: 2rem; color: #1e293b;">إعلاناتي</h1>
    
    {% if ads %}
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
            {% for ad in ads %}
            <div style="background: white; border-radius: 1rem; overflow: hidden; box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1); transition: all 0.3s ease;">
                {% if ad.images.exists %}
                    <img src="{{ ad.images.first.image.url }}" style="width: 100%; height: 200px; object-fit: cover;" alt="{{ ad.title }}">
                {% else %}
                    <div style="width: 100%; height: 200px; background: #f1f5f9; display: flex; align-items: center; justify-content: center; color: #94a3b8; font-size: 3rem;">
                        <i class="fas fa-image"></i>
                    </div>
                {% endif %}
                
                <div style="padding: 1.5rem;">
                    <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 0.75rem; color: #1e293b;">{{ ad.title }}</h3>
                    <p style="color: #64748b; margin-bottom: 1rem;">{{ ad.description|truncatechars:100 }}</p>
                    
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                        {% if ad.price %}
                            <span style="font-size: 1.25rem; font-weight: 700; color: #ff6b35;">{{ ad.price|floatformat:0 }} ريال</span>
                        {% else %}
                            <span style="color: #64748b;">السعر غير محدد</span>
                        {% endif %}
                        <span style="font-size: 0.875rem; color: #94a3b8;">{{ ad.created_at|timesince }}</span>
                    </div>
                    
                    <div style="display: flex; gap: 0.5rem;">
                        <a href="{% url 'ads:detail' ad.pk %}" style="flex: 1; background: #ff6b35; color: white; padding: 0.75rem; border-radius: 0.5rem; text-decoration: none; text-align: center; font-weight: 500;">
                            عرض
                        </a>
                        <a href="{% url 'ads:edit' ad.pk %}" style="flex: 1; background: #f1f5f9; color: #64748b; padding: 0.75rem; border-radius: 0.5rem; text-decoration: none; text-align: center; font-weight: 500;">
                            تعديل
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div style="text-align: center; padding: 4rem 0; color: #64748b;">
            <i class="fas fa-bullhorn" style="font-size: 4rem; margin-bottom: 1rem; color: #cbd5e1;"></i>
            <h3 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 1rem;">لا توجد إعلانات</h3>
            <p style="margin-bottom: 2rem;">لم تقم بنشر أي إعلانات بعد</p>
            <a href="{% url 'ads:create' %}" style="background: #ff6b35; color: white; padding: 1rem 2rem; border-radius: 0.75rem; text-decoration: none; font-weight: 600;">
                أضف إعلانك الأول
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}

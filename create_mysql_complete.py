#!/usr/bin/env python
"""
Complete MySQL database setup with all tables
"""
import os
import sys
import mysql.connector
from mysql.connector import Error
import subprocess

def check_mysql_service():
    """التحقق من خدمة MySQL"""
    print("🔍 التحقق من خدمة MySQL...")
    
    try:
        # محاولة الاتصال بـ MySQL
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            port=3306,
            connect_timeout=5
        )
        
        if connection.is_connected():
            print("✅ خدمة MySQL تعمل بشكل صحيح")
            connection.close()
            return True
        
    except Error as e:
        print(f"❌ خطأ في الاتصال بـ MySQL: {e}")
        print("\n💡 حلول مقترحة:")
        print("1. تأكد من تشغيل XAMPP")
        print("2. ابدأ خدمة MySQL من XAMPP Control Panel")
        print("3. تحقق من أن المنفذ 3306 غير مستخدم")
        return False
    
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def create_mysql_database():
    """إنشاء قاعدة بيانات MySQL"""
    print("\n🏗️ إنشاء قاعدة بيانات MySQL...")
    
    try:
        # الاتصال بـ MySQL
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            port=3306
        )
        
        cursor = connection.cursor()
        
        # حذف قاعدة البيانات إذا كانت موجودة
        print("🗑️ حذف قاعدة البيانات الموجودة (إن وجدت)...")
        cursor.execute("DROP DATABASE IF EXISTS classified_ads_db")
        
        # إنشاء قاعدة البيانات الجديدة
        print("📦 إنشاء قاعدة بيانات جديدة...")
        cursor.execute("""
            CREATE DATABASE classified_ads_db 
            CHARACTER SET utf8mb4 
            COLLATE utf8mb4_unicode_ci
        """)
        
        # التحقق من إنشاء قاعدة البيانات
        cursor.execute("SHOW DATABASES LIKE 'classified_ads_db'")
        result = cursor.fetchone()
        
        if result:
            print("✅ تم إنشاء قاعدة البيانات classified_ads_db بنجاح")
            
            # عرض معلومات قاعدة البيانات
            cursor.execute("""
                SELECT 
                    SCHEMA_NAME as 'Database_Name',
                    DEFAULT_CHARACTER_SET_NAME as 'Character_Set',
                    DEFAULT_COLLATION_NAME as 'Collation'
                FROM information_schema.SCHEMATA 
                WHERE SCHEMA_NAME = 'classified_ads_db'
            """)
            
            db_info = cursor.fetchone()
            if db_info:
                print(f"📊 معلومات قاعدة البيانات:")
                print(f"   - الاسم: {db_info[0]}")
                print(f"   - ترميز الأحرف: {db_info[1]}")
                print(f"   - ترتيب الأحرف: {db_info[2]}")
            
            return True
        else:
            print("❌ فشل في إنشاء قاعدة البيانات")
            return False
            
    except Error as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def update_django_settings():
    """تحديث إعدادات Django لاستخدام MySQL"""
    print("\n⚙️ تحديث إعدادات Django...")
    
    try:
        # قراءة ملف الإعدادات
        settings_path = 'classified_ads_site/settings.py'
        with open(settings_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # التأكد من وجود إعدادات MySQL
        if 'USE_MYSQL' in content:
            print("✅ إعدادات MySQL موجودة بالفعل")
            return True
        
        # إضافة إعدادات MySQL إذا لم تكن موجودة
        mysql_config = """
# MySQL Configuration
USE_MYSQL = True

if USE_MYSQL:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': 'classified_ads_db',
            'USER': 'root',
            'PASSWORD': '',
            'HOST': 'localhost',
            'PORT': '3306',
            'OPTIONS': {
                'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
                'charset': 'utf8mb4',
            },
        }
    }
"""
        
        # إضافة الإعدادات في نهاية الملف
        with open(settings_path, 'a', encoding='utf-8') as file:
            file.write(mysql_config)
        
        print("✅ تم تحديث إعدادات Django")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث الإعدادات: {e}")
        return False

def create_django_tables():
    """إنشاء جداول Django"""
    print("\n🔧 إنشاء جداول Django...")
    
    try:
        # تعيين متغير البيئة لاستخدام MySQL
        env = os.environ.copy()
        env['USE_MYSQL'] = 'true'
        
        # إنشاء الهجرات
        print("📦 إنشاء ملفات الهجرة...")
        result = subprocess.run([
            'python', 'manage.py', 'makemigrations'
        ], capture_output=True, text=True, env=env)
        
        if result.returncode != 0:
            print(f"⚠️ تحذير في إنشاء الهجرات: {result.stderr}")
        
        # تطبيق الهجرات
        print("🚀 تطبيق الهجرات على MySQL...")
        result = subprocess.run([
            'python', 'manage.py', 'migrate'
        ], capture_output=True, text=True, env=env)
        
        if result.returncode == 0:
            print("✅ تم إنشاء جميع الجداول في MySQL بنجاح!")
            return True
        else:
            print(f"❌ فشل في تطبيق الهجرات: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False

def verify_mysql_tables():
    """التحقق من الجداول في MySQL"""
    print("\n🔍 التحقق من الجداول في MySQL...")
    
    try:
        # الاتصال بقاعدة البيانات
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='classified_ads_db',
            port=3306
        )
        
        cursor = connection.cursor()
        
        # عرض الجداول
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        print(f"📁 عدد الجداول المنشأة: {len(tables)}")
        
        if tables:
            print("\n📋 الجداول الموجودة في MySQL:")
            for table in tables:
                table_name = table[0]
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                count = cursor.fetchone()[0]
                
                # تحديد نوع الجدول
                if table_name.startswith('accounts_'):
                    icon = "👥"
                elif table_name.startswith('ads_'):
                    icon = "📢"
                elif table_name.startswith('auth_'):
                    icon = "🔐"
                elif table_name.startswith('django_'):
                    icon = "⚙️"
                else:
                    icon = "📊"
                
                print(f"   {icon} {table_name}: {count} سجل")
        
        return True
        
    except Error as e:
        print(f"❌ خطأ في التحقق من الجداول: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def create_mysql_indexes():
    """إنشاء فهارس محسنة في MySQL"""
    print("\n📇 إنشاء فهارس محسنة...")
    
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='classified_ads_db',
            port=3306
        )
        
        cursor = connection.cursor()
        
        # فهارس محسنة للأداء
        indexes = [
            "CREATE INDEX idx_ads_status ON ads_advertisement(status)",
            "CREATE INDEX idx_ads_category ON ads_advertisement(category_id)",
            "CREATE INDEX idx_ads_user ON ads_advertisement(user_id)",
            "CREATE INDEX idx_ads_created ON ads_advertisement(created_at)",
            "CREATE INDEX idx_ads_featured ON ads_advertisement(is_featured)",
            "CREATE INDEX idx_ads_price ON ads_advertisement(price)",
            "CREATE INDEX idx_category_active ON ads_category(is_active)",
            "CREATE INDEX idx_user_active ON accounts_customuser(is_active)",
            "CREATE INDEX idx_user_verified ON accounts_customuser(is_verified)",
            "CREATE INDEX idx_reports_resolved ON ads_report(is_resolved)",
            # فهارس مركبة
            "CREATE INDEX idx_ads_status_category ON ads_advertisement(status, category_id)",
            "CREATE INDEX idx_ads_status_featured ON ads_advertisement(status, is_featured)",
        ]
        
        created_count = 0
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
                created_count += 1
                print(f"✅ تم إنشاء فهرس")
            except Error as e:
                if "Duplicate key name" in str(e):
                    print(f"⚠️ فهرس موجود بالفعل")
                else:
                    print(f"❌ خطأ في إنشاء فهرس: {e}")
        
        print(f"✅ تم إنشاء {created_count} فهرس في MySQL")
        return True
        
    except Error as e:
        print(f"❌ خطأ في إنشاء الفهارس: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def create_sample_data_mysql():
    """إنشاء البيانات التجريبية في MySQL"""
    print("\n📊 إنشاء البيانات التجريبية...")
    
    try:
        # تعيين متغير البيئة
        os.environ['USE_MYSQL'] = 'true'
        
        # Setup Django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
        import django
        django.setup()
        
        from accounts.models import CustomUser
        from ads.models import Category, Advertisement
        
        # إنشاء المستخدم الإداري
        admin_user, created = CustomUser.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'مدير',
                'last_name': 'النظام',
                'is_staff': True,
                'is_superuser': True,
                'is_verified': True
            }
        )
        
        if created:
            admin_user.set_password('admin123')
            admin_user.save()
            print("✅ تم إنشاء المستخدم الإداري: admin")
        
        # إنشاء الأقسام
        categories_data = [
            {'name': 'عقارات', 'description': 'شقق، فيلات، أراضي للبيع والإيجار', 'icon': 'fas fa-home'},
            {'name': 'سيارات', 'description': 'سيارات جديدة ومستعملة للبيع', 'icon': 'fas fa-car'},
            {'name': 'وظائف', 'description': 'فرص عمل في جميع المجالات', 'icon': 'fas fa-briefcase'},
            {'name': 'دورات تدريبية', 'description': 'دورات ودروس في مختلف المجالات', 'icon': 'fas fa-graduation-cap'},
            {'name': 'إلكترونيات', 'description': 'أجهزة إلكترونية ومعدات تقنية', 'icon': 'fas fa-laptop'},
            {'name': 'أثاث ومنزل', 'description': 'أثاث وأدوات منزلية', 'icon': 'fas fa-couch'},
            {'name': 'خدمات', 'description': 'خدمات متنوعة', 'icon': 'fas fa-tools'},
            {'name': 'أزياء وموضة', 'description': 'ملابس وإكسسوارات', 'icon': 'fas fa-tshirt'},
        ]
        
        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={
                    'description': cat_data['description'],
                    'icon': cat_data['icon'],
                    'is_active': True
                }
            )
            if created:
                print(f"✅ تم إنشاء القسم: {category.name}")
        
        # إنشاء مستخدمين تجريبيين
        users_data = [
            {'username': 'user1', 'email': '<EMAIL>', 'first_name': 'أحمد', 'last_name': 'محمد'},
            {'username': 'user2', 'email': '<EMAIL>', 'first_name': 'فاطمة', 'last_name': 'علي'},
            {'username': 'user3', 'email': '<EMAIL>', 'first_name': 'خالد', 'last_name': 'السعد'},
        ]
        
        for user_data in users_data:
            user, created = CustomUser.objects.get_or_create(
                username=user_data['username'],
                defaults={
                    'email': user_data['email'],
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name'],
                    'is_verified': True
                }
            )
            if created:
                user.set_password('password123')
                user.save()
                print(f"✅ تم إنشاء المستخدم: {user.username}")
        
        print("✅ تم إنشاء جميع البيانات التجريبية في MySQL!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 إعداد قاعدة بيانات MySQL الكاملة")
    print("=" * 60)
    
    # 1. التحقق من خدمة MySQL
    if not check_mysql_service():
        print("\n❌ فشل الإعداد: خدمة MySQL غير متاحة")
        return False
    
    # 2. إنشاء قاعدة البيانات
    if not create_mysql_database():
        print("\n❌ فشل في إنشاء قاعدة البيانات")
        return False
    
    # 3. تحديث إعدادات Django
    if not update_django_settings():
        print("\n❌ فشل في تحديث إعدادات Django")
        return False
    
    # 4. إنشاء الجداول
    if not create_django_tables():
        print("\n❌ فشل في إنشاء الجداول")
        return False
    
    # 5. التحقق من الجداول
    if not verify_mysql_tables():
        print("\n❌ فشل في التحقق من الجداول")
        return False
    
    # 6. إنشاء الفهارس
    create_mysql_indexes()
    
    # 7. إنشاء البيانات التجريبية
    create_sample_data_mysql()
    
    # 8. فحص نهائي
    print("\n🔍 فحص نهائي...")
    verify_mysql_tables()
    
    print("\n" + "=" * 60)
    print("🎉 تم إعداد قاعدة بيانات MySQL بنجاح!")
    print("=" * 60)
    
    print("\n📋 ما تم إنجازه:")
    print("✅ إنشاء قاعدة بيانات MySQL")
    print("✅ إنشاء جميع الجداول المطلوبة")
    print("✅ إنشاء فهارس محسنة للأداء")
    print("✅ إنشاء مستخدم إداري (admin/admin123)")
    print("✅ إنشاء أقسام وبيانات تجريبية")
    
    print("\n🔗 معلومات الاتصال:")
    print("- قاعدة البيانات: classified_ads_db")
    print("- النوع: MySQL")
    print("- المستخدم: root")
    print("- كلمة المرور: (فارغة)")
    print("- الخادم: localhost:3306")
    print("- الترميز: utf8mb4")
    
    print("\n🌐 لتشغيل الموقع مع MySQL:")
    print("set USE_MYSQL=true && python manage.py runserver")
    
    return True

if __name__ == '__main__':
    main()

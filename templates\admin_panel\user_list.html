{% extends 'admin_panel/base.html' %}

{% block title %}إدارة المستخدمين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users me-2"></i>
        إدارة المستخدمين
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-download me-1"></i>تصدير
            </button>
        </div>
    </div>
</div>

<!-- Search Bar -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-8">
                <input type="text" class="form-control" name="search" placeholder="البحث في المستخدمين..." value="{{ request.GET.search }}">
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>بحث
                </button>
                <a href="{% url 'admin_panel:user_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i>مسح
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-list me-2"></i>قائمة المستخدمين ({{ users|length }} مستخدم)</h5>
    </div>
    <div class="card-body">
        {% if users %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>الصورة</th>
                            <th>اسم المستخدم</th>
                            <th>الاسم الكامل</th>
                            <th>البريد الإلكتروني</th>
                            <th>الهاتف</th>
                            <th>عدد الإعلانات</th>
                            <th>تاريخ التسجيل</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>
                                {% if user.profile_image %}
                                    <img src="{{ user.profile_image.url }}" class="rounded-circle" width="40" height="40" alt="{{ user.username }}">
                                {% else %}
                                    <i class="fas fa-user-circle fa-2x text-muted"></i>
                                {% endif %}
                            </td>
                            <td>
                                <strong>{{ user.username }}</strong>
                                {% if user.is_verified %}
                                    <i class="fas fa-check-circle text-success ms-1" title="حساب موثق"></i>
                                {% endif %}
                                {% if user.is_staff %}
                                    <span class="badge bg-warning text-dark ms-1">إداري</span>
                                {% endif %}
                            </td>
                            <td>{{ user.get_full_name|default:"-" }}</td>
                            <td>{{ user.email|default:"-" }}</td>
                            <td>{{ user.phone|default:"-" }}</td>
                            <td>
                                <span class="badge bg-primary">{{ user.ads_count }}</span>
                            </td>
                            <td>{{ user.date_joined|date:"Y/m/d" }}</td>
                            <td>
                                {% if user.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                {% else %}
                                    <span class="badge bg-danger">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'admin_panel:user_detail' user.pk %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <form method="post" action="{% url 'admin_panel:toggle_user_status' user.pk %}" class="d-inline">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-sm {% if user.is_active %}btn-outline-warning{% else %}btn-outline-success{% endif %}" 
                                                onclick="return confirm('هل أنت متأكد؟')">
                                            {% if user.is_active %}
                                                <i class="fas fa-ban"></i>
                                            {% else %}
                                                <i class="fas fa-check"></i>
                                            {% endif %}
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
            <nav aria-label="Page navigation" class="mt-3">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page=1">الأولى</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}">السابقة</a>
                        </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                    </li>
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}">التالية</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-5x text-muted mb-3"></i>
                <h4>لا توجد مستخدمين</h4>
                <p class="text-muted">لم يتم العثور على مستخدمين مطابقين لمعايير البحث</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% extends 'base.html' %}
{% load static %}

{% block title %}الإشعارات{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-bell me-2"></i>الإشعارات
                    {% if unread_count > 0 %}
                        <span class="badge bg-danger">{{ unread_count }}</span>
                    {% endif %}
                </h2>
                
                {% if unread_count > 0 %}
                <form method="post" action="{% url 'ads:mark_all_read' %}" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-check-double me-1"></i>تحديد الكل كمقروء
                    </button>
                </form>
                {% endif %}
            </div>
            
            {% if notifications %}
                <div class="row">
                    {% for notification in notifications %}
                    <div class="col-12 mb-3">
                        <div class="card {% if not notification.is_read %}border-primary{% endif %}">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center mb-2">
                                            {% if notification.type == 'ad_approved' %}
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                            {% elif notification.type == 'ad_rejected' %}
                                                <i class="fas fa-times-circle text-danger me-2"></i>
                                            {% elif notification.type == 'ad_expired' %}
                                                <i class="fas fa-clock text-warning me-2"></i>
                                            {% else %}
                                                <i class="fas fa-info-circle text-info me-2"></i>
                                            {% endif %}
                                            
                                            <h5 class="card-title mb-0">{{ notification.title }}</h5>
                                            
                                            {% if not notification.is_read %}
                                                <span class="badge bg-primary ms-2">جديد</span>
                                            {% endif %}
                                        </div>
                                        
                                        <p class="card-text">{{ notification.message }}</p>
                                        
                                        <div class="d-flex align-items-center text-muted">
                                            <small>
                                                <i class="fas fa-calendar me-1"></i>
                                                {{ notification.created_at|date:"Y-m-d H:i" }}
                                            </small>
                                            
                                            {% if notification.advertisement %}
                                                <small class="ms-3">
                                                    <i class="fas fa-bullhorn me-1"></i>
                                                    <a href="{% url 'ads:detail' notification.advertisement.pk %}" class="text-decoration-none">
                                                        {{ notification.advertisement.title|truncatechars:30 }}
                                                    </a>
                                                </small>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <div class="ms-3">
                                        {% if not notification.is_read %}
                                            <form method="post" action="{% url 'ads:mark_read' notification.id %}" class="d-inline">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-outline-success btn-sm" title="تحديد كمقروء">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>
                                        {% endif %}
                                        
                                        <a href="{% url 'ads:notification_detail' notification.id %}" class="btn btn-outline-primary btn-sm" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Pagination if needed -->
                {% if is_paginated %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1">الأولى</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
            {% else %}
                <div class="text-center py-5">
                    <div class="card border-0 bg-light">
                        <div class="card-body p-5">
                            <i class="fas fa-bell-slash fa-5x text-muted mb-4"></i>
                            <h3 class="text-muted mb-3">لا توجد إشعارات</h3>
                            <p class="text-muted mb-4">
                                لم تتلق أي إشعارات بعد. ستظهر هنا الإشعارات المتعلقة بإعلاناتك وحسابك.
                            </p>
                            <a href="{% url 'ads:create' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>أنشئ إعلانك الأول
                            </a>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Auto-refresh notifications count
setInterval(function() {
    fetch('{% url "ads:unread_count" %}')
        .then(response => response.json())
        .then(data => {
            // Update notification badge in navbar if exists
            const badge = document.querySelector('.notification-badge');
            if (badge) {
                if (data.unread_count > 0) {
                    badge.textContent = data.unread_count;
                    badge.style.display = 'inline';
                } else {
                    badge.style.display = 'none';
                }
            }
        })
        .catch(error => console.log('Error fetching notifications:', error));
}, 30000); // Check every 30 seconds
</script>
{% endblock %}

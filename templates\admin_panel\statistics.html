{% extends 'admin_panel/base.html' %}

{% block title %}الإحصائيات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-chart-bar me-2"></i>
        الإحصائيات والتقارير
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-download me-1"></i>تصدير التقرير
            </button>
        </div>
    </div>
</div>

<!-- Overview Stats -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ active_users }}</h4>
                        <p class="card-text">مستخدمين نشطين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-white" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ inactive_users }}</h4>
                        <p class="card-text">مستخدمين غير نشطين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-slash fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-white" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ ads_by_category|length }}</h4>
                        <p class="card-text">أقسام نشطة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tags fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-white" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">
                            {% for status, count in ads_by_status.items %}
                                {% if status == 'approved' %}{{ count }}{% endif %}
                            {% endfor %}
                        </h4>
                        <p class="card-text">إعلانات موافق عليها</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Ads by Status -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie me-2"></i>الإعلانات حسب الحالة</h5>
            </div>
            <div class="card-body">
                {% for status, count in ads_by_status.items %}
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div class="d-flex align-items-center">
                        {% if status == 'approved' %}
                            <span class="badge bg-success me-2">موافق عليه</span>
                        {% elif status == 'pending' %}
                            <span class="badge bg-warning me-2">في الانتظار</span>
                        {% elif status == 'rejected' %}
                            <span class="badge bg-danger me-2">مرفوض</span>
                        {% elif status == 'expired' %}
                            <span class="badge bg-secondary me-2">منتهي الصلاحية</span>
                        {% endif %}
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="me-2">{{ count }}</span>
                        <div class="progress" style="width: 100px; height: 8px;">
                            {% with total_ads=ads_by_status.values|add:0 %}
                                {% if total_ads > 0 %}
                                    <div class="progress-bar 
                                        {% if status == 'approved' %}bg-success
                                        {% elif status == 'pending' %}bg-warning
                                        {% elif status == 'rejected' %}bg-danger
                                        {% else %}bg-secondary{% endif %}" 
                                        style="width: {% widthratio count total_ads 100 %}%"></div>
                                {% endif %}
                            {% endwith %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <!-- Ads by Category -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar me-2"></i>الإعلانات حسب القسم</h5>
            </div>
            <div class="card-body">
                {% for category in ads_by_category %}
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div class="d-flex align-items-center">
                        {% if category.icon %}
                            <i class="{{ category.icon }} me-2 text-primary"></i>
                        {% else %}
                            <i class="fas fa-tag me-2 text-muted"></i>
                        {% endif %}
                        <span>{{ category.name }}</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="me-2">{{ category.ads_count }}</span>
                        <div class="progress" style="width: 100px; height: 8px;">
                            {% with max_ads=ads_by_category.0.ads_count %}
                                {% if max_ads > 0 %}
                                    <div class="progress-bar bg-primary" 
                                        style="width: {% widthratio category.ads_count max_ads 100 %}%"></div>
                                {% endif %}
                            {% endwith %}
                        </div>
                    </div>
                </div>
                {% empty %}
                <p class="text-muted text-center">لا توجد أقسام</p>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Detailed Stats Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-table me-2"></i>تفاصيل الإحصائيات</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>القسم</th>
                                <th>إجمالي الإعلانات</th>
                                <th>موافق عليها</th>
                                <th>في الانتظار</th>
                                <th>مرفوضة</th>
                                <th>معدل الموافقة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for cat_stat in categories_with_stats %}
                            <tr>
                                <td>
                                    {% if cat_stat.category.icon %}
                                        <i class="{{ cat_stat.category.icon }} me-2 text-primary"></i>
                                    {% endif %}
                                    {{ cat_stat.category.name }}
                                </td>
                                <td>{{ cat_stat.total_count }}</td>
                                <td>
                                    <span class="badge bg-success">
                                        {{ cat_stat.approved_count }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-warning">
                                        {{ cat_stat.pending_count }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-danger">
                                        {{ cat_stat.rejected_count }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2">{{ cat_stat.approval_rate }}%</span>
                                        <div class="progress flex-grow-1" style="height: 8px;">
                                            <div class="progress-bar bg-success" style="width: {{ cat_stat.approval_rate }}%"></div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center text-muted">لا توجد بيانات</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

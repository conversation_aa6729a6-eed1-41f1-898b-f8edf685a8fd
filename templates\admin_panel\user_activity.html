{% extends 'admin_panel/base.html' %}
{% load static %}

{% block title %}أنشطة المستخدم - {{ target_user.get_full_name|default:target_user.username }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-history text-info me-2"></i>
                    أنشطة المستخدم: {{ target_user.get_full_name|default:target_user.username }}
                </h2>
                <div>
                    <a href="{% url 'admin_panel:user_detail' target_user.pk %}" class="btn btn-outline-primary">
                        <i class="fas fa-user me-1"></i>الملف الشخصي
                    </a>
                    <a href="{% url 'admin_panel:user_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>العودة للقائمة
                    </a>
                </div>
            </div>

            <!-- إحصائيات المستخدم -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-bullhorn fa-2x text-primary mb-2"></i>
                            <h4>{{ user_stats.total_ads }}</h4>
                            <small>إجمالي الإعلانات</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h4>{{ user_stats.approved_ads }}</h4>
                            <small>معتمد</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                            <h4>{{ user_stats.pending_ads }}</h4>
                            <small>معلق</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                            <h4>{{ user_stats.rejected_ads }}</h4>
                            <small>مرفوض</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-flag fa-2x text-info mb-2"></i>
                            <h4>{{ user_stats.total_reports }}</h4>
                            <small>التقارير</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-chart-line fa-2x text-secondary mb-2"></i>
                            <h4>{{ user_stats.total_activities }}</h4>
                            <small>الأنشطة</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات المستخدم -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-user me-2"></i>معلومات المستخدم
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-2 text-center">
                                    {% if target_user.profile_picture %}
                                        <img src="{{ target_user.profile_picture.url }}" class="rounded-circle" width="80" height="80">
                                    {% else %}
                                        <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                            <i class="fas fa-user fa-2x text-white"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-10">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <strong>الاسم:</strong><br>
                                            {{ target_user.get_full_name|default:target_user.username }}
                                        </div>
                                        <div class="col-md-3">
                                            <strong>البريد الإلكتروني:</strong><br>
                                            {{ target_user.email }}
                                        </div>
                                        <div class="col-md-3">
                                            <strong>الهاتف:</strong><br>
                                            {{ target_user.phone|default:"غير محدد" }}
                                        </div>
                                        <div class="col-md-3">
                                            <strong>تاريخ التسجيل:</strong><br>
                                            {{ target_user.date_joined|date:"Y-m-d H:i" }}
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-md-3">
                                            <strong>الحالة:</strong><br>
                                            {% if target_user.is_active %}
                                                <span class="badge bg-success">نشط</span>
                                            {% else %}
                                                <span class="badge bg-danger">غير نشط</span>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-3">
                                            <strong>الصلاحيات:</strong><br>
                                            {% if target_user.is_superuser %}
                                                <span class="badge bg-danger">مدير عام</span>
                                            {% elif target_user.is_staff %}
                                                <span class="badge bg-warning">مدير</span>
                                            {% else %}
                                                <span class="badge bg-info">مستخدم عادي</span>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-3">
                                            <strong>المدينة:</strong><br>
                                            {{ target_user.location|default:"غير محدد" }}
                                        </div>
                                        <div class="col-md-3">
                                            <strong>التحقق:</strong><br>
                                            {% if target_user.is_verified %}
                                                <span class="badge bg-success">محقق</span>
                                            {% else %}
                                                <span class="badge bg-secondary">غير محقق</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- سجل الأنشطة -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>سجل الأنشطة (آخر 50 نشاط)
                    </h5>
                </div>
                <div class="card-body">
                    {% if activities %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>النشاط</th>
                                        <th>الوصف</th>
                                        <th>عنوان IP</th>
                                        <th>المتصفح</th>
                                        <th>التاريخ والوقت</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for activity in activities %}
                                    <tr>
                                        <td>
                                            {% if activity.action == 'login' %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-sign-in-alt me-1"></i>{{ activity.get_action_display }}
                                                </span>
                                            {% elif activity.action == 'logout' %}
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-sign-out-alt me-1"></i>{{ activity.get_action_display }}
                                                </span>
                                            {% elif activity.action == 'create' %}
                                                <span class="badge bg-primary">
                                                    <i class="fas fa-plus me-1"></i>{{ activity.get_action_display }}
                                                </span>
                                            {% elif activity.action == 'update' %}
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-edit me-1"></i>{{ activity.get_action_display }}
                                                </span>
                                            {% elif activity.action == 'delete' %}
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-trash me-1"></i>{{ activity.get_action_display }}
                                                </span>
                                            {% elif activity.action == 'view' %}
                                                <span class="badge bg-info">
                                                    <i class="fas fa-eye me-1"></i>{{ activity.get_action_display }}
                                                </span>
                                            {% else %}
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-cog me-1"></i>{{ activity.get_action_display }}
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td>{{ activity.description|truncatechars:60 }}</td>
                                        <td>
                                            {% if activity.ip_address %}
                                                <code>{{ activity.ip_address }}</code>
                                            {% else %}
                                                <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if activity.user_agent %}
                                                <small title="{{ activity.user_agent }}">
                                                    {% if 'Chrome' in activity.user_agent %}
                                                        <i class="fab fa-chrome text-warning"></i> Chrome
                                                    {% elif 'Firefox' in activity.user_agent %}
                                                        <i class="fab fa-firefox text-orange"></i> Firefox
                                                    {% elif 'Safari' in activity.user_agent %}
                                                        <i class="fab fa-safari text-info"></i> Safari
                                                    {% elif 'Edge' in activity.user_agent %}
                                                        <i class="fab fa-edge text-primary"></i> Edge
                                                    {% else %}
                                                        <i class="fas fa-globe text-secondary"></i> أخرى
                                                    {% endif %}
                                                </small>
                                            {% else %}
                                                <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <small>{{ activity.timestamp|date:"Y-m-d H:i:s" }}</small>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-history fa-5x text-muted mb-4"></i>
                            <h3 class="text-muted mb-3">لا توجد أنشطة</h3>
                            <p class="text-muted">لم يتم تسجيل أي أنشطة لهذا المستخدم بعد.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

from django import forms
from .models import Advertisement, Category, Report, AdImage

class AdvertisementForm(forms.ModelForm):
    """نموذج إنشاء وتعديل الإعلانات"""
    class Meta:
        model = Advertisement
        fields = ['title', 'description', 'category', 'price', 'location', 'phone', 'email']
        labels = {
            'title': 'عنوان الإعلان',
            'description': 'وصف الإعلان',
            'category': 'القسم',
            'price': 'السعر (ريال)',
            'location': 'الموقع',
            'phone': 'رقم الهاتف',
            'email': 'البريد الإلكتروني',
        }
        widgets = {
            'description': forms.Textarea(attrs={'rows': 5}),
            'price': forms.NumberInput(attrs={'step': '0.01', 'min': '0'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field_name, field in self.fields.items():
            field.widget.attrs['class'] = 'form-control'
        
        # تخصيص placeholder للحقول
        self.fields['title'].widget.attrs['placeholder'] = 'أدخل عنوان الإعلان'
        self.fields['description'].widget.attrs['placeholder'] = 'اكتب وصف مفصل للإعلان'
        self.fields['price'].widget.attrs['placeholder'] = '0.00'
        self.fields['location'].widget.attrs['placeholder'] = 'المدينة أو المنطقة'
        self.fields['phone'].widget.attrs['placeholder'] = '+966xxxxxxxxx'
        self.fields['email'].widget.attrs['placeholder'] = '<EMAIL>'

class AdImageForm(forms.ModelForm):
    """نموذج رفع صور الإعلانات"""
    class Meta:
        model = AdImage
        fields = ['image', 'is_main']
        labels = {
            'image': 'الصورة',
            'is_main': 'صورة رئيسية',
        }

class ReportForm(forms.ModelForm):
    """نموذج الإبلاغ عن الإعلانات"""
    class Meta:
        model = Report
        fields = ['report_type', 'description']
        labels = {
            'report_type': 'نوع التقرير',
            'description': 'وصف المشكلة',
        }
        widgets = {
            'description': forms.Textarea(attrs={'rows': 4}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field_name, field in self.fields.items():
            field.widget.attrs['class'] = 'form-control'
        
        self.fields['description'].widget.attrs['placeholder'] = 'اشرح سبب الإبلاغ عن هذا الإعلان'

class CategoryForm(forms.ModelForm):
    """نموذج إنشاء وتعديل الأقسام"""
    class Meta:
        model = Category
        fields = ['name', 'description', 'icon', 'is_active']
        labels = {
            'name': 'اسم القسم',
            'description': 'وصف القسم',
            'icon': 'أيقونة القسم',
            'is_active': 'نشط',
        }
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field_name, field in self.fields.items():
            if field_name != 'is_active':
                field.widget.attrs['class'] = 'form-control'
            else:
                field.widget.attrs['class'] = 'form-check-input'
        
        self.fields['icon'].widget.attrs['placeholder'] = 'fas fa-home (Font Awesome class)'
        self.fields['icon'].help_text = 'استخدم أسماء أيقونات Font Awesome مثل: fas fa-home, fas fa-car'

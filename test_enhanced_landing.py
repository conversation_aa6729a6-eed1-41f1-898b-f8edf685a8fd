#!/usr/bin/env python
"""
اختبار شامل لصفحة الهبوط المحسنة
"""
import os
import django
import requests
import time
from urllib.parse import urljoin

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'classified_ads_site.settings')
django.setup()

def test_enhanced_landing_page():
    """اختبار صفحة الهبوط المحسنة"""
    print("🌟 اختبار صفحة الهبوط المحسنة")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:8000"
    
    try:
        start_time = time.time()
        response = requests.get(base_url, timeout=10)
        load_time = time.time() - start_time
        
        if response.status_code == 200:
            print(f"✅ الصفحة تحملت بنجاح")
            print(f"⏱️ وقت التحميل: {load_time:.2f} ثانية")
            
            content = response.text
            
            # فحص العناصر المحسنة
            enhanced_elements = [
                'hero-enhanced',
                'search-enhanced',
                'cta-buttons-enhanced',
                'categories-horizontal',
                'enhanced-slider',
                'featured-slider',
                'card-enhanced',
                'testimonial-card',
                'stat-number',
                'floating-elements'
            ]
            
            found_elements = []
            missing_elements = []
            
            for element in enhanced_elements:
                if element in content:
                    found_elements.append(element)
                    print(f"✅ {element}: موجود")
                else:
                    missing_elements.append(element)
                    print(f"❌ {element}: مفقود")
            
            # فحص الألوان المحسنة
            color_system = [
                '--primary-50',
                '--primary-500',
                '--secondary-500',
                '--gradient-primary',
                '--gradient-hero',
                '--shadow-xl'
            ]
            
            found_colors = sum(1 for color in color_system if color in content)
            color_score = (found_colors / len(color_system)) * 100
            
            print(f"\n🎨 نظام الألوان: {color_score:.1f}% ({found_colors}/{len(color_system)})")
            
            # فحص JavaScript المحسن
            js_features = [
                'EnhancedSlider',
                'HorizontalScroller',
                'animateCounters',
                'enhanceSearch',
                'initScrollAnimations'
            ]
            
            found_js = sum(1 for feature in js_features if feature in content)
            js_score = (found_js / len(js_features)) * 100
            
            print(f"⚡ JavaScript المحسن: {js_score:.1f}% ({found_js}/{len(js_features)})")
            
            return {
                'success': True,
                'load_time': load_time,
                'elements_score': (len(found_elements) / len(enhanced_elements)) * 100,
                'color_score': color_score,
                'js_score': js_score,
                'found_elements': len(found_elements),
                'total_elements': len(enhanced_elements)
            }
            
        else:
            print(f"❌ فشل في تحميل الصفحة: {response.status_code}")
            return {'success': False, 'error': f'HTTP {response.status_code}'}
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return {'success': False, 'error': str(e)}

def test_database_integration():
    """اختبار تكامل قاعدة البيانات"""
    print(f"\n💾 اختبار تكامل قاعدة البيانات")
    print("-" * 40)
    
    try:
        from ads.models import Advertisement, Category
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # إحصائيات قاعدة البيانات
        stats = {
            'users': User.objects.count(),
            'categories': Category.objects.filter(is_active=True).count(),
            'total_ads': Advertisement.objects.count(),
            'approved_ads': Advertisement.objects.filter(status='approved').count(),
            'featured_ads': Advertisement.objects.filter(status='approved', is_featured=True).count(),
            'urgent_ads': Advertisement.objects.filter(status='approved', is_urgent=True).count(),
        }
        
        print(f"📊 إحصائيات قاعدة البيانات:")
        for key, value in stats.items():
            print(f"   {key}: {value}")
        
        # اختبار البيانات للصفحة المحسنة
        from classified_ads_site.views import HomeView
        
        view = HomeView()
        context = view.get_context_data()
        
        print(f"\n📋 بيانات الصفحة المحسنة:")
        print(f"   الأقسام: {len(context.get('categories', []))}")
        print(f"   الإعلانات المميزة: {len(context.get('featured_ads', []))}")
        print(f"   أحدث الإعلانات: {len(context.get('latest_ads', []))}")
        print(f"   الإعلانات العاجلة: {len(context.get('urgent_ads', []))}")
        
        # فحص الأيقونات
        categories_with_icons = sum(1 for cat in context.get('categories', []) if cat.icon)
        print(f"   أقسام بأيقونات: {categories_with_icons}")
        
        return {
            'database_stats': stats,
            'page_data': {
                'categories_count': len(context.get('categories', [])),
                'featured_ads_count': len(context.get('featured_ads', [])),
                'latest_ads_count': len(context.get('latest_ads', [])),
                'urgent_ads_count': len(context.get('urgent_ads', [])),
                'categories_with_icons': categories_with_icons
            }
        }
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return {'error': str(e)}

def test_slider_functionality():
    """اختبار وظائف الـ Slider"""
    print(f"\n🎠 اختبار وظائف الـ Slider")
    print("-" * 30)
    
    base_url = "http://127.0.0.1:8000"
    
    try:
        response = requests.get(base_url, timeout=5)
        content = response.text
        
        # فحص عناصر الـ Slider
        slider_elements = [
            'enhanced-slider',
            'featured-slider',
            'slider-track',
            'slider-controls',
            'slider-dots',
            'categories-horizontal'
        ]
        
        found_sliders = []
        for element in slider_elements:
            if element in content:
                found_sliders.append(element)
                print(f"✅ {element}: موجود")
            else:
                print(f"❌ {element}: مفقود")
        
        slider_score = (len(found_sliders) / len(slider_elements)) * 100
        print(f"\n📊 نقاط الـ Slider: {slider_score:.1f}%")
        
        return {
            'score': slider_score,
            'found_elements': len(found_sliders),
            'total_elements': len(slider_elements)
        }
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الـ Slider: {e}")
        return {'error': str(e)}

def test_responsive_design():
    """اختبار التصميم المتجاوب"""
    print(f"\n📱 اختبار التصميم المتجاوب")
    print("-" * 35)
    
    base_url = "http://127.0.0.1:8000"
    
    device_types = {
        'Mobile': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
        'Tablet': 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
        'Desktop': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    results = {}
    
    for device_type, user_agent in device_types.items():
        try:
            headers = {'User-Agent': user_agent}
            start_time = time.time()
            response = requests.get(base_url, headers=headers, timeout=10)
            load_time = time.time() - start_time
            
            if response.status_code == 200:
                # فحص العناصر المتجاوبة
                content = response.text
                responsive_elements = [
                    'container-responsive',
                    'hero-enhanced',
                    'search-enhanced',
                    'card-enhanced',
                    'categories-horizontal'
                ]
                
                found_responsive = sum(1 for element in responsive_elements if element in content)
                responsive_score = (found_responsive / len(responsive_elements)) * 100
                
                results[device_type] = {
                    'load_time': load_time,
                    'responsive_score': responsive_score,
                    'status': 'success'
                }
                
                print(f"📱 {device_type}: {load_time:.2f}s - {responsive_score:.1f}%")
            else:
                print(f"❌ {device_type}: خطأ {response.status_code}")
                results[device_type] = {'status': 'error', 'code': response.status_code}
                
        except Exception:
            print(f"❌ {device_type}: خطأ في الاتصال")
            results[device_type] = {'status': 'error'}
    
    return results

def test_performance():
    """اختبار الأداء المحسن"""
    print(f"\n⚡ اختبار الأداء المحسن")
    print("-" * 30)
    
    base_url = "http://127.0.0.1:8000"
    
    load_times = []
    
    for i in range(5):
        try:
            start_time = time.time()
            response = requests.get(base_url, timeout=10)
            load_time = time.time() - start_time
            
            if response.status_code == 200:
                load_times.append(load_time)
                print(f"✅ اختبار {i+1}: {load_time:.2f} ثانية")
            else:
                print(f"❌ اختبار {i+1}: فشل")
                
        except Exception:
            print(f"❌ اختبار {i+1}: خطأ")
    
    if load_times:
        avg_time = sum(load_times) / len(load_times)
        min_time = min(load_times)
        max_time = max(load_times)
        
        print(f"\n📊 نتائج الأداء:")
        print(f"   متوسط وقت التحميل: {avg_time:.2f} ثانية")
        print(f"   أسرع تحميل: {min_time:.2f} ثانية")
        print(f"   أبطأ تحميل: {max_time:.2f} ثانية")
        
        # تقييم الأداء المحسن
        if avg_time < 1:
            print(f"🚀 الأداء ممتاز!")
        elif avg_time < 2:
            print(f"✅ الأداء جيد جداً")
        elif avg_time < 3:
            print(f"⚠️ الأداء مقبول")
        else:
            print(f"❌ الأداء يحتاج تحسين")
        
        return {
            'avg_time': avg_time,
            'min_time': min_time,
            'max_time': max_time,
            'tests_count': len(load_times)
        }
    
    return {'error': 'فشل في جميع الاختبارات'}

def generate_enhanced_report():
    """إنشاء تقرير شامل للصفحة المحسنة"""
    print("🧪 اختبار شامل لصفحة الهبوط المحسنة")
    print("=" * 70)
    
    # تشغيل جميع الاختبارات
    landing_test = test_enhanced_landing_page()
    database_test = test_database_integration()
    slider_test = test_slider_functionality()
    responsive_test = test_responsive_design()
    performance_test = test_performance()
    
    print(f"\n📊 التقرير النهائي للصفحة المحسنة")
    print("=" * 70)
    
    # حساب النتيجة الإجمالية
    scores = []
    
    # تقييم الصفحة المحسنة
    if landing_test.get('success'):
        overall_landing = (landing_test.get('elements_score', 0) + 
                          landing_test.get('color_score', 0) + 
                          landing_test.get('js_score', 0)) / 3
        scores.append(overall_landing)
        print(f"🌟 الصفحة المحسنة: {overall_landing:.1f}%")
        print(f"   - العناصر: {landing_test.get('elements_score', 0):.1f}%")
        print(f"   - الألوان: {landing_test.get('color_score', 0):.1f}%")
        print(f"   - JavaScript: {landing_test.get('js_score', 0):.1f}%")
    
    # تقييم قاعدة البيانات
    if 'database_stats' in database_test:
        db_score = 100  # إذا عملت فهي 100%
        scores.append(db_score)
        print(f"💾 قاعدة البيانات: {db_score}%")
        page_data = database_test['page_data']
        print(f"   - الأقسام: {page_data['categories_count']}")
        print(f"   - الإعلانات المميزة: {page_data['featured_ads_count']}")
        print(f"   - الإعلانات العاجلة: {page_data['urgent_ads_count']}")
    
    # تقييم الـ Slider
    if 'score' in slider_test:
        scores.append(slider_test['score'])
        print(f"🎠 الـ Sliders: {slider_test['score']:.1f}%")
    
    # تقييم التصميم المتجاوب
    if responsive_test:
        responsive_scores = [result.get('responsive_score', 0) for result in responsive_test.values() 
                           if result.get('status') == 'success']
        if responsive_scores:
            avg_responsive = sum(responsive_scores) / len(responsive_scores)
            scores.append(avg_responsive)
            print(f"📱 التصميم المتجاوب: {avg_responsive:.1f}%")
    
    # تقييم الأداء
    if 'avg_time' in performance_test:
        if performance_test['avg_time'] < 1:
            perf_score = 100
        elif performance_test['avg_time'] < 2:
            perf_score = 90
        elif performance_test['avg_time'] < 3:
            perf_score = 75
        else:
            perf_score = 50
        scores.append(perf_score)
        print(f"⚡ الأداء: {perf_score}% (متوسط: {performance_test['avg_time']:.2f}s)")
    
    # النتيجة الإجمالية
    if scores:
        overall_score = sum(scores) / len(scores)
        print(f"\n🏆 النتيجة الإجمالية: {overall_score:.1f}%")
        
        if overall_score >= 95:
            print(f"🎉 ممتاز! صفحة الهبوط المحسنة تعمل بشكل مثالي")
            print(f"✨ جميع التحسينات مطبقة بنجاح")
        elif overall_score >= 85:
            print(f"🌟 ممتاز! صفحة الهبوط محسنة بشكل رائع")
            print(f"🔧 بعض التحسينات الطفيفة مطلوبة")
        elif overall_score >= 75:
            print(f"✅ جيد جداً! الصفحة محسنة بشكل جيد")
        else:
            print(f"⚠️ يحتاج تحسين! بعض الميزات تحتاج إصلاح")
    
    print(f"\n🌐 الصفحة المحسنة: http://127.0.0.1:8000/")
    print("=" * 70)
    
    return overall_score if scores else 0

def main():
    """الدالة الرئيسية"""
    score = generate_enhanced_report()
    return score >= 85

if __name__ == '__main__':
    main()

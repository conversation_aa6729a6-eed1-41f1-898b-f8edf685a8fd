#!/usr/bin/env python
"""
Direct MySQL table creation script
"""
import mysql.connector
from mysql.connector import Error

def create_mysql_tables_direct():
    """إنشاء الجداول مباشرة في MySQL"""
    print("🏗️ إنشاء جداول MySQL مباشرة")
    print("=" * 50)
    
    try:
        # الاتصال بقاعدة البيانات
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='classified_ads_db',
            port=3306
        )
        
        cursor = connection.cursor()
        
        # إنشاء جدول المستخدمين المخصص
        print("👥 إنشاء جدول المستخدمين...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS accounts_customuser (
                id INT AUTO_INCREMENT PRIMARY KEY,
                password VARCHAR(128) NOT NULL,
                last_login DATETIME(6) NULL,
                is_superuser BOOLEAN NOT NULL DEFAULT FALSE,
                username VARCHAR(150) NOT NULL UNIQUE,
                first_name VARCHAR(150) NOT NULL DEFAULT '',
                last_name VARCHAR(150) NOT NULL DEFAULT '',
                email VARCHAR(254) NOT NULL DEFAULT '',
                is_staff BOOLEAN NOT NULL DEFAULT FALSE,
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                date_joined DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                phone VARCHAR(20) NULL,
                address TEXT NULL,
                profile_image VARCHAR(100) NULL,
                is_verified BOOLEAN NOT NULL DEFAULT FALSE,
                created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                updated_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # إنشاء جدول الأقسام
        print("📂 إنشاء جدول الأقسام...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS ads_category (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL UNIQUE,
                description TEXT NULL,
                icon VARCHAR(50) NULL,
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                updated_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # إنشاء جدول الإعلانات
        print("📢 إنشاء جدول الإعلانات...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS ads_advertisement (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(200) NOT NULL,
                description TEXT NOT NULL,
                price DECIMAL(10,2) NULL,
                location VARCHAR(100) NULL,
                phone VARCHAR(20) NULL,
                email VARCHAR(254) NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'pending',
                is_featured BOOLEAN NOT NULL DEFAULT FALSE,
                views_count INT NOT NULL DEFAULT 0,
                created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                updated_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                expires_at DATETIME(6) NULL,
                category_id INT NOT NULL,
                user_id INT NOT NULL,
                FOREIGN KEY (category_id) REFERENCES ads_category(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES accounts_customuser(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # إنشاء جدول صور الإعلانات
        print("🖼️ إنشاء جدول صور الإعلانات...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS ads_adimage (
                id INT AUTO_INCREMENT PRIMARY KEY,
                image VARCHAR(100) NOT NULL,
                is_main BOOLEAN NOT NULL DEFAULT FALSE,
                created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                advertisement_id INT NOT NULL,
                FOREIGN KEY (advertisement_id) REFERENCES ads_advertisement(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # إنشاء جدول التقارير
        print("📋 إنشاء جدول التقارير...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS ads_report (
                id INT AUTO_INCREMENT PRIMARY KEY,
                report_type VARCHAR(50) NOT NULL,
                description TEXT NOT NULL,
                is_resolved BOOLEAN NOT NULL DEFAULT FALSE,
                created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                advertisement_id INT NOT NULL,
                user_id INT NOT NULL,
                FOREIGN KEY (advertisement_id) REFERENCES ads_advertisement(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES accounts_customuser(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # إنشاء جداول Django الأساسية
        print("⚙️ إنشاء جداول Django الأساسية...")
        
        # جدول أنواع المحتوى
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS django_content_type (
                id INT AUTO_INCREMENT PRIMARY KEY,
                app_label VARCHAR(100) NOT NULL,
                model VARCHAR(100) NOT NULL,
                UNIQUE KEY django_content_type_app_label_model (app_label, model)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # جدول الصلاحيات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS auth_permission (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                content_type_id INT NOT NULL,
                codename VARCHAR(100) NOT NULL,
                UNIQUE KEY auth_permission_content_type_id_codename (content_type_id, codename),
                FOREIGN KEY (content_type_id) REFERENCES django_content_type(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # جدول المجموعات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS auth_group (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(150) NOT NULL UNIQUE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # جدول صلاحيات المجموعات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS auth_group_permissions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                group_id INT NOT NULL,
                permission_id INT NOT NULL,
                UNIQUE KEY auth_group_permissions_group_id_permission_id (group_id, permission_id),
                FOREIGN KEY (group_id) REFERENCES auth_group(id),
                FOREIGN KEY (permission_id) REFERENCES auth_permission(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # جدول مجموعات المستخدمين
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS accounts_customuser_groups (
                id INT AUTO_INCREMENT PRIMARY KEY,
                customuser_id INT NOT NULL,
                group_id INT NOT NULL,
                UNIQUE KEY accounts_customuser_groups_customuser_id_group_id (customuser_id, group_id),
                FOREIGN KEY (customuser_id) REFERENCES accounts_customuser(id),
                FOREIGN KEY (group_id) REFERENCES auth_group(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # جدول صلاحيات المستخدمين
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS accounts_customuser_user_permissions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                customuser_id INT NOT NULL,
                permission_id INT NOT NULL,
                UNIQUE KEY accounts_customuser_user_permissions_customuser_id_permission_id (customuser_id, permission_id),
                FOREIGN KEY (customuser_id) REFERENCES accounts_customuser(id),
                FOREIGN KEY (permission_id) REFERENCES auth_permission(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # جدول الجلسات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS django_session (
                session_key VARCHAR(40) NOT NULL PRIMARY KEY,
                session_data LONGTEXT NOT NULL,
                expire_date DATETIME(6) NOT NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # جدول سجل الإدارة
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS django_admin_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                action_time DATETIME(6) NOT NULL,
                object_id LONGTEXT NULL,
                object_repr VARCHAR(200) NOT NULL,
                action_flag SMALLINT UNSIGNED NOT NULL,
                change_message LONGTEXT NOT NULL,
                content_type_id INT NULL,
                user_id INT NOT NULL,
                FOREIGN KEY (content_type_id) REFERENCES django_content_type(id),
                FOREIGN KEY (user_id) REFERENCES accounts_customuser(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # جدول الهجرات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS django_migrations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                app VARCHAR(255) NOT NULL,
                name VARCHAR(255) NOT NULL,
                applied DATETIME(6) NOT NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        print("✅ تم إنشاء جميع الجداول بنجاح!")
        
        # إنشاء الفهارس
        print("\n📇 إنشاء الفهارس...")
        indexes = [
            "CREATE INDEX idx_ads_status ON ads_advertisement(status)",
            "CREATE INDEX idx_ads_category ON ads_advertisement(category_id)",
            "CREATE INDEX idx_ads_user ON ads_advertisement(user_id)",
            "CREATE INDEX idx_ads_created ON ads_advertisement(created_at)",
            "CREATE INDEX idx_ads_featured ON ads_advertisement(is_featured)",
            "CREATE INDEX idx_category_active ON ads_category(is_active)",
            "CREATE INDEX idx_user_active ON accounts_customuser(is_active)",
            "CREATE INDEX idx_user_verified ON accounts_customuser(is_verified)",
            "CREATE INDEX idx_session_expire ON django_session(expire_date)",
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
                print("✅ تم إنشاء فهرس")
            except Error as e:
                if "Duplicate key name" in str(e):
                    print("⚠️ فهرس موجود بالفعل")
                else:
                    print(f"❌ خطأ في إنشاء فهرس: {e}")
        
        return True
        
    except Error as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()
            print("🔌 تم إغلاق الاتصال بـ MySQL")

def verify_tables():
    """التحقق من الجداول المنشأة"""
    print("\n🔍 التحقق من الجداول...")
    
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='classified_ads_db',
            port=3306
        )
        
        cursor = connection.cursor()
        
        # عرض الجداول
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        print(f"📁 عدد الجداول: {len(tables)}")
        
        if tables:
            print("\n📋 الجداول الموجودة:")
            for table in tables:
                table_name = table[0]
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                count = cursor.fetchone()[0]
                
                # تحديد أيقونة الجدول
                if table_name.startswith('accounts_'):
                    icon = "👥"
                elif table_name.startswith('ads_'):
                    icon = "📢"
                elif table_name.startswith('auth_'):
                    icon = "🔐"
                elif table_name.startswith('django_'):
                    icon = "⚙️"
                else:
                    icon = "📊"
                
                print(f"   {icon} {table_name}: {count} سجل")
        
        return True
        
    except Error as e:
        print(f"❌ خطأ في التحقق: {e}")
        return False
    
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء جداول MySQL مباشرة")
    print("=" * 60)
    
    # إنشاء الجداول
    if not create_mysql_tables_direct():
        print("❌ فشل في إنشاء الجداول")
        return False
    
    # التحقق من الجداول
    if not verify_tables():
        print("❌ فشل في التحقق من الجداول")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 تم إنشاء جداول MySQL بنجاح!")
    print("=" * 60)
    
    print("\n📋 الجداول المنشأة:")
    print("✅ accounts_customuser - جدول المستخدمين المخصص")
    print("✅ ads_category - جدول أقسام الإعلانات")
    print("✅ ads_advertisement - جدول الإعلانات الرئيسي")
    print("✅ ads_adimage - جدول صور الإعلانات")
    print("✅ ads_report - جدول تقارير الإعلانات")
    print("✅ django_* - جداول Django الأساسية")
    print("✅ auth_* - جداول نظام المصادقة")
    
    print("\n🔧 المميزات:")
    print("✅ ترميز UTF8MB4 لدعم العربية")
    print("✅ فهارس محسنة للأداء")
    print("✅ علاقات قاعدة البيانات")
    print("✅ قيود المرجعية")
    
    print("\n📋 الخطوات التالية:")
    print("1. إضافة البيانات التجريبية")
    print("2. إنشاء مستخدم إداري")
    print("3. تشغيل الموقع")
    
    return True

if __name__ == '__main__':
    main()

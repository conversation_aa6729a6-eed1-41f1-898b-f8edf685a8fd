{% extends 'admin_panel/base.html' %}
{% load static %}

{% block title %}تفاصيل التقرير #{{ object.id }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>
                        <i class="fas fa-flag text-danger me-2"></i>تفاصيل التقرير #{{ object.id }}
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'admin_panel:dashboard' %}">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'admin_panel:report_list' %}">التقارير</a></li>
                            <li class="breadcrumb-item active">تقرير #{{ object.id }}</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'admin_panel:report_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>العودة للقائمة
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- تفاصيل التقرير -->
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>معلومات التقرير
                            </h5>
                            {% if object.status == 'pending' %}
                                <span class="badge bg-danger">معلق</span>
                            {% else %}
                                <span class="badge bg-success">محلول</span>
                            {% endif %}
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td width="150"><strong>رقم التقرير:</strong></td>
                                    <td>#{{ object.id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>المبلغ:</strong></td>
                                    <td>
                                        {{ object.user.get_full_name|default:object.user.username }}
                                        <small class="text-muted">({{ object.user.email }})</small>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>سبب التبليغ:</strong></td>
                                    <td>
                                        <span class="badge bg-warning">{{ object.get_reason_display }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ التبليغ:</strong></td>
                                    <td>{{ object.created_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الحالة:</strong></td>
                                    <td>
                                        {% if object.status == 'pending' %}
                                            <span class="badge bg-danger">معلق</span>
                                        {% else %}
                                            <span class="badge bg-success">محلول</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% if object.resolved_at %}
                                <tr>
                                    <td><strong>تاريخ الحل:</strong></td>
                                    <td>{{ object.resolved_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                                {% endif %}
                            </table>
                            
                            {% if object.description %}
                            <div class="mt-4">
                                <h6>وصف التقرير:</h6>
                                <div class="bg-light p-3 rounded">
                                    {{ object.description|linebreaks }}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- الإعلان المبلغ عنه -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bullhorn me-2"></i>الإعلان المبلغ عنه
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                {% if object.advertisement.images.exists %}
                                <div class="col-md-4">
                                    <img src="{{ object.advertisement.images.first.image.url }}" class="img-fluid rounded" alt="{{ object.advertisement.title }}">
                                </div>
                                <div class="col-md-8">
                                {% else %}
                                <div class="col-12">
                                {% endif %}
                                    <h6>{{ object.advertisement.title }}</h6>
                                    <p class="text-muted">{{ object.advertisement.description|truncatechars:200 }}</p>
                                    
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td><strong>القسم:</strong></td>
                                            <td>{{ object.advertisement.category.name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>المعلن:</strong></td>
                                            <td>{{ object.advertisement.user.get_full_name|default:object.advertisement.user.username }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ النشر:</strong></td>
                                            <td>{{ object.advertisement.created_at|date:"Y-m-d" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الحالة:</strong></td>
                                            <td>
                                                {% if object.advertisement.status == 'approved' %}
                                                    <span class="badge bg-success">معتمد</span>
                                                {% elif object.advertisement.status == 'pending' %}
                                                    <span class="badge bg-warning">معلق</span>
                                                {% elif object.advertisement.status == 'rejected' %}
                                                    <span class="badge bg-danger">مرفوض</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إجراءات الإدارة -->
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-cogs me-2"></i>إجراءات الإدارة
                            </h6>
                        </div>
                        <div class="card-body">
                            {% if object.status == 'pending' %}
                            <form method="post" action="{% url 'admin_panel:resolve_report' object.pk %}" class="mb-3">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-success w-100" onclick="return confirm('هل تريد وضع علامة محلول على هذا التقرير؟')">
                                    <i class="fas fa-check me-1"></i>وضع علامة محلول
                                </button>
                            </form>
                            {% endif %}

                            <a href="{% url 'ads:detail' object.advertisement.pk %}" class="btn btn-outline-primary w-100 mb-2" target="_blank">
                                <i class="fas fa-external-link-alt me-1"></i>عرض الإعلان
                            </a>

                            <a href="{% url 'admin_panel:ad_detail' object.advertisement.pk %}" class="btn btn-outline-info w-100 mb-2">
                                <i class="fas fa-edit me-1"></i>إدارة الإعلان
                            </a>

                            <a href="{% url 'admin_panel:user_detail' object.advertisement.user.pk %}" class="btn btn-outline-secondary w-100 mb-2">
                                <i class="fas fa-user me-1"></i>ملف المعلن
                            </a>

                            <a href="{% url 'admin_panel:user_detail' object.user.pk %}" class="btn btn-outline-warning w-100">
                                <i class="fas fa-flag me-1"></i>ملف المبلغ
                            </a>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>إحصائيات
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h5 class="text-primary">{{ object.advertisement.views_count }}</h5>
                                        <small class="text-muted">مشاهدات الإعلان</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h5 class="text-warning">{{ object.advertisement.reports.count }}</h5>
                                    <small class="text-muted">تقارير الإعلان</small>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h5 class="text-info">{{ object.user.reports_made.count }}</h5>
                                        <small class="text-muted">تقارير المبلغ</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h5 class="text-success">{{ object.advertisement.user.advertisements.count }}</h5>
                                    <small class="text-muted">إعلانات المعلن</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if object.status == 'resolved' %}
                    <div class="alert alert-success mt-4">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>تم حل هذا التقرير</strong>
                        {% if object.resolved_at %}
                        <br><small>في {{ object.resolved_at|date:"Y-m-d H:i" }}</small>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

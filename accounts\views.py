from django.shortcuts import render
from django.views.generic import C<PERSON><PERSON><PERSON><PERSON>, DetailView, UpdateView, ListView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth import login
from django.contrib import messages
from django.urls import reverse_lazy
from .models import CustomUser
from .forms import CustomUserCreationForm, UserProfileForm
from ads.models import Advertisement

class RegisterView(CreateView):
    """عرض التسجيل"""
    model = CustomUser
    form_class = CustomUserCreationForm
    template_name = 'accounts/register.html'
    success_url = reverse_lazy('home')

    def form_valid(self, form):
        response = super().form_valid(form)
        login(self.request, self.object)
        messages.success(self.request, 'تم إنشاء حسابك بنجاح!')
        return response

class ProfileView(LoginRequiredMixin, DetailView):
    """عرض الملف الشخصي"""
    model = CustomUser
    template_name = 'accounts/profile.html'
    context_object_name = 'user_profile'

    def get_object(self):
        return self.request.user

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['user_ads'] = Advertisement.objects.filter(
            user=self.request.user
        ).order_by('-created_at')[:5]
        return context

class EditProfileView(LoginRequiredMixin, UpdateView):
    """تعديل الملف الشخصي"""
    model = CustomUser
    form_class = UserProfileForm
    template_name = 'accounts/edit_profile.html'
    success_url = reverse_lazy('accounts:profile')

    def get_object(self):
        return self.request.user

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث ملفك الشخصي بنجاح!')
        return super().form_valid(form)

class MyAdsView(LoginRequiredMixin, ListView):
    """عرض إعلانات المستخدم"""
    model = Advertisement
    template_name = 'accounts/my_ads.html'
    context_object_name = 'ads'
    paginate_by = 10

    def get_queryset(self):
        return Advertisement.objects.filter(
            user=self.request.user
        ).order_by('-created_at')

{% extends 'base.html' %}

{% block title %}الملف الشخصي - {{ user_profile.username }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                {% if user_profile.profile_image %}
                    <img src="{{ user_profile.profile_image.url }}" class="rounded-circle mb-3" width="150" height="150" alt="صورة الملف الشخصي">
                {% else %}
                    <i class="fas fa-user-circle fa-9x text-muted mb-3"></i>
                {% endif %}
                <h4>{{ user_profile.get_full_name|default:user_profile.username }}</h4>
                <p class="text-muted">عضو منذ {{ user_profile.date_joined|date:"Y/m/d" }}</p>
                
                {% if user_profile.is_verified %}
                    <span class="badge bg-success mb-2">
                        <i class="fas fa-check-circle me-1"></i>حساب موثق
                    </span>
                {% endif %}
                
                <div class="d-grid gap-2">
                    <a href="{% url 'accounts:edit_profile' %}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>تعديل الملف الشخصي
                    </a>
                    <a href="{% url 'accounts:my_ads' %}" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>إعلاناتي
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle me-2"></i>معلومات الحساب</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>اسم المستخدم:</strong></div>
                    <div class="col-sm-9">{{ user_profile.username }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>البريد الإلكتروني:</strong></div>
                    <div class="col-sm-9">{{ user_profile.email|default:"غير محدد" }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>رقم الهاتف:</strong></div>
                    <div class="col-sm-9">{{ user_profile.phone|default:"غير محدد" }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>العنوان:</strong></div>
                    <div class="col-sm-9">{{ user_profile.address|default:"غير محدد" }}</div>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar me-2"></i>إحصائيات</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4">
                        <h3 class="text-primary">{{ user_profile.advertisements.count }}</h3>
                        <p>إجمالي الإعلانات</p>
                    </div>
                    <div class="col-md-4">
                        <h3 class="text-success">{{ user_profile.advertisements.filter.status='approved'.count }}</h3>
                        <p>إعلانات موافق عليها</p>
                    </div>
                    <div class="col-md-4">
                        <h3 class="text-warning">{{ user_profile.advertisements.filter.status='pending'.count }}</h3>
                        <p>إعلانات في الانتظار</p>
                    </div>
                </div>
            </div>
        </div>
        
        {% if user_ads %}
        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="fas fa-bullhorn me-2"></i>آخر الإعلانات</h5>
            </div>
            <div class="card-body">
                {% for ad in user_ads %}
                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                    <div>
                        <h6 class="mb-1">{{ ad.title }}</h6>
                        <small class="text-muted">{{ ad.created_at|date:"Y/m/d" }}</small>
                    </div>
                    <div>
                        {% if ad.status == 'approved' %}
                            <span class="badge bg-success">موافق عليه</span>
                        {% elif ad.status == 'pending' %}
                            <span class="badge bg-warning">في الانتظار</span>
                        {% elif ad.status == 'rejected' %}
                            <span class="badge bg-danger">مرفوض</span>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
                <div class="text-center mt-3">
                    <a href="{% url 'accounts:my_ads' %}" class="btn btn-outline-primary">عرض جميع الإعلانات</a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

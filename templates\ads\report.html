{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}الإبلاغ عن الإعلان{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h3><i class="fas fa-flag me-2"></i>الإبلاغ عن الإعلان</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>تنبيه:</strong> يرجى استخدام هذه الخاصية فقط للإبلاغ عن الإعلانات التي تنتهك شروط الاستخدام.
                </div>
                
                <!-- Ad Preview -->
                {% if ad %}
                <div class="card bg-light mb-4">
                    <div class="card-body">
                        <h5 class="card-title">الإعلان المراد الإبلاغ عنه:</h5>
                        <h6 class="text-primary">{{ ad.title }}</h6>
                        <p class="card-text">{{ ad.description|truncatewords:20 }}</p>
                        <small class="text-muted">
                            نشر بواسطة: {{ ad.user.username }} في {{ ad.created_at|date:"Y/m/d" }}
                        </small>
                    </div>
                </div>
                {% endif %}
                
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.report_type.id_for_label }}" class="form-label">نوع التقرير *</label>
                        {{ form.report_type }}
                        {% if form.report_type.errors %}
                            <div class="text-danger">{{ form.report_type.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">وصف المشكلة *</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger">{{ form.description.errors }}</div>
                        {% endif %}
                        <small class="form-text text-muted">يرجى شرح سبب الإبلاغ عن هذا الإعلان بالتفصيل</small>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>ملاحظة:</strong> سيتم مراجعة تقريرك من قبل فريق الإدارة وسيتم اتخاذ الإجراء المناسب.
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        {% if ad %}
                            <a href="{{ ad.get_absolute_url }}" class="btn btn-secondary me-md-2">إلغاء</a>
                        {% else %}
                            <a href="{% url 'ads:list' %}" class="btn btn-secondary me-md-2">إلغاء</a>
                        {% endif %}
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-flag me-2"></i>إرسال التقرير
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
